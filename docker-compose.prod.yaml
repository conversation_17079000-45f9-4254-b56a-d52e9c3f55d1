services:
  local-prod:
    container_name: eip-ossmosis6-api-prod
    build:
      dockerfile: Dockerfile
      context: .
      target: production
    working_dir: /application
    command: java -jar eip-ossmosis6-web-services.jar
    environment:
      - ENVIRONMENT=docker
      - DBUSER=${DBUSER}
      - DBPASS=${DBPASS}
      - DBENCSECRET=${DBENCSECRET}
      - SNOW_HOST=${SNOW_HOST}
      - OAUTH2_JWT_URI_DOMAIN=${OAUTH2_JWT_URI_DOMAIN}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS}
      - DBHOST=${DBHOST}
      - SNOW_USER=${SNOW_USER}
      - SNOW_PASS=${SNOW_PASS}
      - RELAY_HOST=${RELAY_HOST}
      - JPA_DDL_AUTO=${JPA_DDL_AUTO}
      - ELASTIC_APM_SECRET_TOKEN=${ELASTIC_APM_SECRET_TOKEN}
      - ELASTIC_APM_URL=${ELASTIC_APM_URL}
      - LOG_REFORMATTING=${LOG_REFORMATTING}
      - SNYK_TOKEN=${SNYK_TOKEN}
    ports:
      - 8000:8000
      - 8080:8080
    restart: unless-stopped
    depends_on:
      postgres:
        condition: "service_healthy"

  postgres:
    image: bitnami/postgresql:latest
    environment:
      - POSTGRESQL_PASSWORD=${POSTGRESQL_PASSWORD}
      # this second entry allows us to use pg_isready as a health check
      - POSTGRES_PASSWORD=${POSTGRESQL_PASSWORD}
    # this lets us run any sql statements in the setup directory
    volumes:
      - ./src/main/resources/sql/setup/:/docker-entrypoint-initdb.d/
      # Persist data in your home directory
      # - ~/data/postgresql:/bitnami/postgresql
    # port is exposed if you want to have other app instances hit localhost:5432
    ports:
      - 5432:5432
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -d ossmosis6_repo -U postgres"]
      start_period: 10s
      interval: 5s
      timeout: 10s
      retries: 5


  pgadmin:
    image: dpage/pgadmin4:2023-11-20-2
    # localhost:8002
    ports:
      - 8002:80
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=postgres
      - PGADMIN_CONFIG_SERVER_MODE=False
      - PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED=False
    entrypoint: /bin/sh -c "chmod 600 /pgpass; /entrypoint.sh;"
    configs:
      - source: servers.json
        target: /pgadmin4/servers.json
      - source: pgpass
        target: /pgpass
    user: root
    depends_on:
      postgres:
        condition: "service_healthy"

networks:
  default:
    name: ossmosis6

# these are just for the db web ui
configs:
  pgpass:
    content: postgres:5432:*:postgres:${POSTGRESQL_PASSWORD}
  servers.json:
    content: |
      {"Servers": {"1": {
        "Group": "Servers",
        "Name": "My Local Postgres",
        "Host": "postgres",
        "Port": 5432,
        "MaintenanceDB": "postgres",
        "Username": "postgres",
        "PassFile": "/pgpass",
        "SSLMode": "prefer"
      }}}
