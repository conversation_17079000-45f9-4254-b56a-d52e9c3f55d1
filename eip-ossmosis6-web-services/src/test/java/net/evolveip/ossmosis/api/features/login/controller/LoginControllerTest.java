package net.evolveip.ossmosis.api.features.login.controller;


import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.isA;
import static org.hamcrest.Matchers.notNullValue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import net.evolveip.ossmosis.api.config.auth.DynamicJwtDecoderResolver;
import net.evolveip.ossmosis.api.features.login.constant.LoginConstants;
import net.evolveip.ossmosis.api.features.login.dto.LoginRequestDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginResponseDTO;
import net.evolveip.ossmosis.api.features.login.service.LoginService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.service.AuthorizationService;
import net.minidev.json.JSONArray;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ContextConfiguration(classes = DynamicJwtDecoderResolver.class)
@WebMvcTest(controllers = {LoginController.class})
public class LoginControllerTest {

  @MockBean
  private LoginService loginService;

  @MockBean
  private AuthorizationService authorizationService;
  private MockMvc mockMvc;
  private Long loginId1, loginId2;
  private String loginEmail1, loginEmail2;
  private String loginNameFirst1, loginNameFirst2;
  private String loginNameLast1, loginNameLast2;
  private Boolean active1, active2;
  private Boolean locked1, locked2;
  private String loginGroup1, loginGroup2;
  private String loginPhoneNumber1, loginPhoneNumber2;
  private String loginPrimaryEnterpriseId1, loginPrimaryEnterpriseId2;
  private LoginRequestDTO loginRequestDTO, loginRequestDTO1, loginRequestDTO2;
  private LoginResponseDTO loginResponseDTO, loginResponseDTO1, loginResponseDTO2;
  private ObjectMapper objectMapper;

  @BeforeEach
  public void setup() {
    this.mockMvc = MockMvcBuilders.standaloneSetup(new LoginController(loginService),
        new CurrentLoginController(authorizationService)).build();

    loginId1 = 1L;
    loginEmail1 = "<EMAIL>";
    loginNameFirst1 = "NameFirst1";
    loginNameLast1 = "NameLast1";
    active1 = true;
    locked1 = false;
    loginGroup1 = "admin";
    loginPhoneNumber1 = "(*************";
    loginPrimaryEnterpriseId1 = "1000000009";

    loginId2 = 1L;
    loginEmail2 = "<EMAIL>";
    loginNameFirst2 = "NameFirst2";
    loginNameLast2 = "NameLast1";
    active2 = true;
    locked2 = false;
    loginGroup2 = "admin";
    loginPhoneNumber2 = "(*************";
    loginPrimaryEnterpriseId2 = "1000000009";

    objectMapper = new ObjectMapper();
  }

  @WithMockUser(roles = LoginConstants.LOGINS_READ)
  @DisplayName("Test: get all logins")
  @Test
  public void testGetAllLogins() throws Exception {
    loginResponseDTO1 = LoginResponseDTO.builder().loginId(loginId1).loginEmail(loginEmail1)
        .loginNameFirst(loginNameFirst1).loginNameLast(loginNameLast1).active(active1)
        .locked(locked1)
        .loginGroup(loginGroup1)
        .loginPhoneNumber(loginPhoneNumber1).loginPrimaryEnterpriseId(loginPrimaryEnterpriseId1)
        .build();

    loginResponseDTO2 = LoginResponseDTO.builder().loginId(loginId2).loginEmail(loginEmail2)
        .loginNameFirst(loginNameFirst2).loginNameLast(loginNameLast2).active(active2)
        .locked(locked2)
        .loginGroup(loginGroup2)
        .loginPhoneNumber(loginPhoneNumber2).loginPrimaryEnterpriseId(loginPrimaryEnterpriseId2)
        .build();

    List<LoginResponseDTO> loginResponseDTOList = Arrays.asList(loginResponseDTO1,
        loginResponseDTO2);

    when(loginService.processGetListByEnterpriseId("ent1")).thenReturn(ApiResponse.create(loginResponseDTOList));

    mockMvc.perform(MockMvcRequestBuilders.get("/enterprise/ent1/logins")).andDo(print())
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload", is(notNullValue())))
        .andExpect(jsonPath("$.payload", hasSize(2))).andExpect(jsonPath("$.errors").isArray())
        .andExpect(jsonPath("$.errors", hasSize(0)));
  }

  @WithMockUser(roles = LoginConstants.LOGINS_CREATE)
  @DisplayName("Test: create login by loginRequestDTO")
  @Test
  public void testCreatePlatformByPlatformRequestDTO() throws Exception {
    loginRequestDTO = LoginRequestDTO.builder().loginId(loginId1).loginEmail(loginEmail1)
        .loginNameFirst(loginNameFirst1).loginNameLast(loginNameLast1).active(active1)
        .locked(locked1).loginGroup(loginGroup1)
        .loginPhoneNumber(loginPhoneNumber1).loginPrimaryEnterpriseId(loginPrimaryEnterpriseId1)
        .build();

    loginResponseDTO = LoginResponseDTO.builder().loginId(loginId1).loginEmail(loginEmail1)
        .loginNameFirst(loginNameFirst1).loginNameLast(loginNameLast1).active(active1)
        .locked(locked1)
        .loginGroup(loginGroup1)
        .loginPhoneNumber(loginPhoneNumber1).loginPrimaryEnterpriseId(loginPrimaryEnterpriseId1)
        .build();

    when(loginService.processCreateLogin(loginRequestDTO, "1")).thenReturn(
        ApiResponse.create(loginResponseDTO));

    String jsonRequest = objectMapper.writeValueAsString(loginRequestDTO);

    mockMvc.perform(
            MockMvcRequestBuilders.post("/enterprise/1/logins").contentType(MediaType.APPLICATION_JSON)
            .content(jsonRequest)).andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload", is(notNullValue())))
        .andExpect(jsonPath("$.payload", isA(LinkedHashMap.class)))
        .andExpect(jsonPath("$.errors").isArray()).andExpect(jsonPath("$.errors", hasSize(0)));
  }

  @WithMockUser(roles = {LoginConstants.LOGINS_UPDATE,
      LoginConstants.LOGINS_READ})
  @DisplayName("Test: update login by loginRequestDTO")
  @Test
  public void testUpdateLoginByLoginRequestDTO() throws Exception {
    loginRequestDTO = LoginRequestDTO.builder().loginId(loginId1).loginEmail(loginEmail1)
        .loginNameFirst("New Login First Name").loginNameLast("New Login Last Name").active(active1)
        .locked(locked1).loginGroup(loginGroup1)
        .loginPhoneNumber(loginPhoneNumber1).loginPrimaryEnterpriseId(loginPrimaryEnterpriseId1)
        .build();

    loginResponseDTO = LoginResponseDTO.builder().loginId(loginId1).loginEmail(loginEmail1)
        .loginNameFirst(loginNameFirst1).loginNameLast(loginNameLast1).active(active1)
        .locked(locked1)
        .loginGroup(loginGroup1)
        .loginPhoneNumber(loginPhoneNumber1).loginPrimaryEnterpriseId(loginPrimaryEnterpriseId1)
        .build();

    when(loginService.updateLogin(any(LoginRequestDTO.class), anyString())).thenReturn(
        ApiResponse.create(loginResponseDTO));

    String jsonRequest = objectMapper.writeValueAsString(loginRequestDTO);

    mockMvc.perform(
            MockMvcRequestBuilders.put("/enterprise/1/logins").contentType(MediaType.APPLICATION_JSON)
            .content(jsonRequest)).andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload", is(notNullValue())))
        .andExpect(jsonPath("$.payload", isA(LinkedHashMap.class)))
        .andExpect(jsonPath("$.errors").isArray()).andExpect(jsonPath("$.errors", hasSize(0)));
  }

  @WithMockUser(roles = {LoginConstants.LOGINS_UPDATE,
      LoginConstants.LOGINS_READ})
  @DisplayName("Test: update multiple logins using a list of loginRequestDTO")
  @Test
  public void testUpdateMultipleLoginsByLoginRequestDTO() throws Exception {
    loginRequestDTO1 = LoginRequestDTO.builder().loginId(loginId1).loginEmail(loginEmail1)
        .loginNameFirst(loginNameFirst1).loginNameLast(loginNameLast1).active(active1)
        .locked(locked1).loginGroup(loginGroup1)
        .loginPhoneNumber(loginPhoneNumber1).loginPrimaryEnterpriseId(loginPrimaryEnterpriseId1)
        .build();

    loginRequestDTO2 = LoginRequestDTO.builder().loginId(loginId2).loginEmail(loginEmail2)
        .loginNameFirst(loginNameFirst2).loginNameLast(loginNameLast2).active(active2)
        .locked(locked2)
        .loginGroup(loginGroup2)
        .loginPhoneNumber(loginPhoneNumber2).loginPrimaryEnterpriseId(loginPrimaryEnterpriseId2)
        .build();

    loginResponseDTO1 = LoginResponseDTO.builder().loginId(loginId1).loginEmail(loginEmail1)
        .loginNameFirst(loginNameFirst1).loginNameLast(loginNameLast1).active(active1)
        .locked(locked1)
        .loginGroup(loginGroup1)
        .loginPhoneNumber(loginPhoneNumber1).loginPrimaryEnterpriseId(loginPrimaryEnterpriseId1)
        .build();

    loginResponseDTO2 = LoginResponseDTO.builder().loginId(loginId2).loginEmail(loginEmail2)
        .loginNameFirst(loginNameFirst2).loginNameLast(loginNameLast2).active(active2)
        .locked(locked2)
        .loginGroup(loginGroup2)
        .loginPhoneNumber(loginPhoneNumber2).loginPrimaryEnterpriseId(loginPrimaryEnterpriseId2)
        .build();

    List<LoginRequestDTO> loginRequestDTOs = Arrays.asList(loginRequestDTO1, loginRequestDTO2);
    List<LoginResponseDTO> loginResponseDTOS = Arrays.asList(loginResponseDTO1, loginResponseDTO2);

    when(loginService.updateLogins(loginRequestDTOs, "1")).thenReturn(
        ApiResponse.create(loginResponseDTOS));

    String jsonRequest = objectMapper.writeValueAsString(loginRequestDTOs);

    mockMvc.perform(
            MockMvcRequestBuilders.put("/enterprise/1/logins/multiple")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonRequest)).andDo(print()).andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload", is(notNullValue())))
        .andExpect(jsonPath("$.payload", isA(JSONArray.class)))
        .andExpect(jsonPath("$.errors").isArray()).andExpect(jsonPath("$.errors", hasSize(0)));
  }

  @WithMockUser(roles = {LoginConstants.LOGINS_CREATE})
  @DisplayName("Test: create multiple logins using a list of loginRequestDTO")
  @Test
  public void testCreateMultipleLoginsByLoginRequestDTO() throws Exception {
    loginRequestDTO1 = LoginRequestDTO.builder().loginId(loginId1).loginEmail(loginEmail1)
        .loginNameFirst(loginNameFirst1).loginNameLast(loginNameLast1).active(active1)
        .locked(locked1).loginGroup(loginGroup1)
        .loginPhoneNumber(loginPhoneNumber1).loginPrimaryEnterpriseId(loginPrimaryEnterpriseId1)
        .build();

    loginRequestDTO2 = LoginRequestDTO.builder().loginId(loginId2).loginEmail(loginEmail2)
        .loginNameFirst(loginNameFirst2).loginNameLast(loginNameLast2).active(active2)
        .locked(locked2).loginGroup(loginGroup2)
        .loginPhoneNumber(loginPhoneNumber2).loginPrimaryEnterpriseId(loginPrimaryEnterpriseId2)
        .build();

    loginResponseDTO1 = LoginResponseDTO.builder().loginId(loginId1).loginEmail(loginEmail1)
        .loginNameFirst(loginNameFirst1).loginNameLast(loginNameLast1).active(active1)
        .locked(locked1)
        .loginGroup(loginGroup1)
        .loginPhoneNumber(loginPhoneNumber1).loginPrimaryEnterpriseId(loginPrimaryEnterpriseId1)
        .build();

    loginResponseDTO2 = LoginResponseDTO.builder().loginId(loginId2).loginEmail(loginEmail2)
        .loginNameFirst(loginNameFirst2).loginNameLast(loginNameLast2).active(active2)
        .locked(locked2)
        .loginGroup(loginGroup2)
        .loginPhoneNumber(loginPhoneNumber2).loginPrimaryEnterpriseId(loginPrimaryEnterpriseId2)
        .build();

    List<LoginRequestDTO> loginRequestDTOs = Arrays.asList(loginRequestDTO1, loginRequestDTO2);
    List<LoginResponseDTO> loginResponseDTOS = Arrays.asList(loginResponseDTO1, loginResponseDTO2);

    when(loginService.processCreateLogins(loginRequestDTOs, "1")).thenReturn(
        ApiResponse.create(loginResponseDTOS));

    String jsonRequest = objectMapper.writeValueAsString(loginRequestDTOs);

    mockMvc.perform(
            MockMvcRequestBuilders.post("/enterprise/1/logins/multiple")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonRequest)).andDo(print()).andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload", is(notNullValue())))
        .andExpect(jsonPath("$.payload", isA(JSONArray.class)))
        .andExpect(jsonPath("$.errors").isArray()).andExpect(jsonPath("$.errors", hasSize(0)));
  }

  @WithMockUser(roles = {LoginConstants.LOGINS_CREATE})
  @DisplayName("Test: create multiple logins using a list of loginRequestDTO")
  @Test
  public void testDeleteLoginByLoginId() throws Exception {
    when(loginService.processDeleteByLoginId(loginId1, "1")).thenReturn(ApiResponse.create(true));

    mockMvc.perform(MockMvcRequestBuilders.delete("/enterprise/1/logins/{loginId}", loginId1))
        .andDo(print())
        .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload", is(notNullValue())))
        .andExpect(jsonPath("$.payload", isA(Boolean.class)))
        .andExpect(jsonPath("$.errors").isArray()).andExpect(jsonPath("$.errors", hasSize(0)));

  }
}