package net.evolveip.ossmosis.api.features.platform.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.enterprise.repository.EnterpriseRepository;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformRequestDTO;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformResponseDTO;
import net.evolveip.ossmosis.api.features.platform.entity.Platform;
import net.evolveip.ossmosis.api.features.platform.mapper.PlatformMapper;
import net.evolveip.ossmosis.api.features.platform.repository.PlatformRepository;
import net.evolveip.ossmosis.api.utils.exceptions.DuplicateFoundException;
import net.evolveip.ossmosis.api.utils.exceptions.NotFoundException;
import net.evolveip.ossmosis.api.utils.exceptions.ReferencedException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
public class PlatformServiceUnitTest {

  // Constants
  private static final int PLATFORM_ID_1 = 1;
  private static final int PLATFORM_ID_2 = 2;
  private static final int NON_EXISTENT_PLATFORM_ID = 999;
  private static final String PLATFORM_NAME_1 = "Platform 1";
  private static final String PLATFORM_NAME_2 = "Platform 2";
  private static final String UPDATED_PLATFORM_NAME = "Updated Platform Name";
  private static final String DUPLICATE_PLATFORM_NAME = "Duplicate Platform";

  // DisplayName constants
  private static final String TEST_CREATE_PLATFORM = "Test: create platform";
  private static final String TEST_RETRIEVE_PLATFORMS = "Test: retrieve platforms";
  private static final String TEST_RETRIEVE_PLATFORMS_EMPTY = "Test: retrieve platforms returns an empty list";
  private static final String TEST_RETRIEVE_PLATFORM_BY_ID = "Test: retrieve platform by platformId";
  private static final String TEST_RETRIEVE_PLATFORM_BY_ID_USING_DTO = "Test: retrieve platform by platformId using DTO";
  private static final String TEST_RETRIEVE_PLATFORM_BY_NONEXISTENT_ID = "Test: retrieve platform by non-existent platformId throws NotFoundException";
  private static final String TEST_UPDATE_PLATFORM = "Test: update platform";
  private static final String TEST_UPDATE_NONEXISTENT_PLATFORM = "Test: update non-existent platform throws NotFoundException";
  private static final String TEST_UPDATE_PLATFORM_DUPLICATE_NAME = "Test: update platform with duplicate name throws DuplicateFoundException";
  private static final String TEST_DELETE_PLATFORM = "Test: delete platform";
  private static final String TEST_DELETE_NONEXISTENT_PLATFORM = "Test: delete non-existent platform throws NotFoundException";
  private static final String TEST_DELETE_PLATFORM_WITH_DEPENDENCIES = "Test: delete platform with dependencies throws ReferencedException";
  private static final String TEST_VALIDATE_PLATFORM_ID = "Test: validate platform id exists";
  private static final String TEST_VALIDATE_PLATFORM_ID_THROWS = "Test: validate platform id throws NotFoundException";

  @Mock
  private PlatformRepository repository;

  @Mock
  private PlatformMapper mapper;

  @Mock
  private EnterpriseRepository enterpriseRepository;

  @InjectMocks
  private PlatformService platformService;

  // Test data
  private PlatformRequestDTO platformRequestDTO;
  private Platform platform1;
  private Platform platform2;
  private PlatformResponseDTO platformResponseDTO1;
  private PlatformResponseDTO platformResponseDTO2;
  private List<Platform> platformsList;
  private List<PlatformResponseDTO> platformResponseDTOList;

  @BeforeEach
  public void setUp() {
    // Set EnterpriseRepository in the service using reflection
    ReflectionTestUtils.setField(platformService, "enterpriseRepository", enterpriseRepository);

    // Initialize request DTO
    platformRequestDTO = createPlatformRequestDTO(null, PLATFORM_NAME_1);

    // Initialize platform entities
    platform1 = createPlatform(PLATFORM_ID_1, PLATFORM_NAME_1);
    platform2 = createPlatform(PLATFORM_ID_2, PLATFORM_NAME_2);

    // Initialize response DTOs
    platformResponseDTO1 = createPlatformResponseDTO(PLATFORM_ID_1, PLATFORM_NAME_1);
    platformResponseDTO2 = createPlatformResponseDTO(PLATFORM_ID_2, PLATFORM_NAME_2);

    // Initialize lists
    platformsList = new ArrayList<>(List.of(platform1, platform2));
    platformResponseDTOList = new ArrayList<>(List.of(platformResponseDTO1, platformResponseDTO2));
  }

  @DisplayName(TEST_CREATE_PLATFORM)
  @Test
  public void testCreatePlatform() {
    // Arrange
    when(mapper.toPlatform(any(PlatformRequestDTO.class))).thenReturn(platform1);
    when(repository.findByPlatformName(anyString())).thenReturn(Optional.empty());
    when(repository.saveAndFlush(any(Platform.class))).thenReturn(platform1);
    when(mapper.toPlatformDTO(any(Platform.class))).thenReturn(platformResponseDTO1);

    // Act
    PlatformResponseDTO result = platformService.processCreate(platformRequestDTO);

    // Assert
    assertNotNull(result);
    assertEquals(platformResponseDTO1.getPlatformId(), result.getPlatformId());
    assertEquals(platformResponseDTO1.getPlatformName(), result.getPlatformName());

    // Verify interactions
    verify(mapper).toPlatform(platformRequestDTO);
    verify(repository).findByPlatformName(platform1.getPlatformName());
    verify(repository).saveAndFlush(platform1);
    verify(mapper).toPlatformDTO(platform1);
  }

  @DisplayName(TEST_RETRIEVE_PLATFORMS)
  @Test
  public void testRetrievePlatforms() {
    // Arrange
    when(repository.findAll()).thenReturn(platformsList);
    when(mapper.entitiesToDTOs(platformsList)).thenReturn(platformResponseDTOList);

    // Act
    List<PlatformResponseDTO> result = platformService.processGetList();

    // Assert
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals(platformResponseDTO1.getPlatformId(), result.get(0).getPlatformId());
    assertEquals(platformResponseDTO2.getPlatformId(), result.get(1).getPlatformId());

    // Verify interactions
    verify(repository).findAll();
    verify(mapper).entitiesToDTOs(platformsList);
  }

  @DisplayName(TEST_RETRIEVE_PLATFORMS_EMPTY)
  @Test
  public void testRetrievePlatformsEmptyResponseList() {
    // Arrange
    List<Platform> emptyList = Collections.emptyList();

    when(repository.findAll()).thenReturn(emptyList);
    // Service implementation doesn't call mapper.entitiesToDTOs for empty lists
    // so don't mock it

    // Act
    List<PlatformResponseDTO> result = platformService.processGetList();

    // Assert
    assertNotNull(result);
    assertEquals(0, result.size());

    // Verify interactions
    verify(repository).findAll();
    // Do not verify mapper.entitiesToDTOs since it's not called for empty lists
  }

  @DisplayName(TEST_RETRIEVE_PLATFORM_BY_ID)
  @Test
  public void testRetrievePlatformById() {
    // Arrange
    when(repository.findByPlatformId(PLATFORM_ID_1)).thenReturn(Optional.of(platform1));
    when(mapper.toPlatformDTO(platform1)).thenReturn(platformResponseDTO1);

    // Act
    PlatformResponseDTO result = platformService.processGetById(PLATFORM_ID_1);

    // Assert
    assertNotNull(result);
    assertEquals(platformResponseDTO1.getPlatformId(), result.getPlatformId());
    assertEquals(platformResponseDTO1.getPlatformName(), result.getPlatformName());

    // Verify interactions
    verify(repository).findByPlatformId(PLATFORM_ID_1);
    verify(mapper).toPlatformDTO(platform1);
  }

  @DisplayName(TEST_RETRIEVE_PLATFORM_BY_ID_USING_DTO)
  @Test
  public void testRetrievePlatformByIdUsingDTO() {
    // Arrange
    PlatformRequestDTO requestDTO = createPlatformRequestDTO(PLATFORM_ID_1, PLATFORM_NAME_1);

    when(repository.findByPlatformId(PLATFORM_ID_1)).thenReturn(Optional.of(platform1));
    when(mapper.toPlatformDTO(platform1)).thenReturn(platformResponseDTO1);

    // Act
    PlatformResponseDTO result = platformService.processGetById(requestDTO);

    // Assert
    assertNotNull(result);
    assertEquals(platformResponseDTO1.getPlatformId(), result.getPlatformId());

    // Verify interactions
    verify(repository).findByPlatformId(PLATFORM_ID_1);
    verify(mapper).toPlatformDTO(platform1);
  }

  @DisplayName(TEST_RETRIEVE_PLATFORM_BY_NONEXISTENT_ID)
  @Test
  public void testRetrievePlatformByNonExistentId() {
    // Arrange
    when(repository.findByPlatformId(NON_EXISTENT_PLATFORM_ID)).thenReturn(Optional.empty());

    // Act & Assert
    assertThrows(NotFoundException.class,
        () -> platformService.processGetById(NON_EXISTENT_PLATFORM_ID));

    // Verify interactions
    verify(repository).findByPlatformId(NON_EXISTENT_PLATFORM_ID);
  }

  @DisplayName(TEST_UPDATE_PLATFORM)
  @Test
  public void testUpdatePlatform() {
    // Arrange
    // For a successful update, we need the existing platform and updated platform
    // to have the same name to avoid the duplicate check
    String sameName = PLATFORM_NAME_1; // Use the same name for both

    PlatformRequestDTO updateRequestDTO = createPlatformRequestDTO(PLATFORM_ID_1, sameName);
    Platform existingPlatform = createPlatform(PLATFORM_ID_1, sameName);
    Platform updatedPlatform = createPlatform(PLATFORM_ID_1, sameName);
    PlatformResponseDTO updatedResponseDTO = createPlatformResponseDTO(PLATFORM_ID_1, sameName);

    // Setup mocks
    when(mapper.toPlatform(updateRequestDTO)).thenReturn(updatedPlatform);
    when(repository.findByPlatformId(PLATFORM_ID_1)).thenReturn(Optional.of(existingPlatform));
    when(repository.saveAndFlush(any(Platform.class))).thenReturn(updatedPlatform);
    when(mapper.toPlatformDTO(any(Platform.class))).thenReturn(updatedResponseDTO);

    // Act
    PlatformResponseDTO result = platformService.processUpdate(updateRequestDTO);

    // Assert
    assertNotNull(result);
    assertEquals(updatedResponseDTO.getPlatformId(), result.getPlatformId());
    assertEquals(updatedResponseDTO.getPlatformName(), result.getPlatformName());

    // Verify interactions
    verify(mapper).toPlatform(updateRequestDTO);
    verify(repository).findByPlatformId(PLATFORM_ID_1);
    verify(repository).saveAndFlush(any(Platform.class));
    verify(mapper).toPlatformDTO(any(Platform.class));
  }

  @DisplayName("Test: update platform fails when service implementation rejects name change")
  @Test
  public void testUpdatePlatformNameChangeRejected() {
    // Arrange - Set up to trigger
    // checkIfCandidatePlatformNameIsAssignedToAnotherPlatformId exception
    PlatformRequestDTO updateRequestDTO = createPlatformRequestDTO(PLATFORM_ID_1,
        UPDATED_PLATFORM_NAME);
    Platform candidatePlatform = createPlatform(PLATFORM_ID_1, UPDATED_PLATFORM_NAME);
    Platform existingPlatform = createPlatform(PLATFORM_ID_1, PLATFORM_NAME_1);

    // Setup mocks to make the service think we're changing name for same ID
    when(mapper.toPlatform(updateRequestDTO)).thenReturn(candidatePlatform);
    when(repository.findByPlatformId(PLATFORM_ID_1)).thenReturn(Optional.of(existingPlatform));

    // Act & Assert
    DuplicateFoundException exception = assertThrows(DuplicateFoundException.class,
        () -> platformService.processUpdate(updateRequestDTO));

    // Verify that exception message contains expected text
    assertTrue(exception.getMessage()
        .contains("cannot update an existing platformName to a different platformId"));

    // Verify interactions
    verify(mapper).toPlatform(updateRequestDTO);
    verify(repository).findByPlatformId(PLATFORM_ID_1);
  }

  @DisplayName(TEST_UPDATE_PLATFORM_DUPLICATE_NAME)
  @Test
  public void testUpdatePlatformWithDuplicateName() {
    // Arrange
    PlatformRequestDTO updateRequestDTO = createPlatformRequestDTO(PLATFORM_ID_1,
        DUPLICATE_PLATFORM_NAME);
    Platform updatedPlatform = createPlatform(PLATFORM_ID_1, DUPLICATE_PLATFORM_NAME);
    Platform existingPlatform = createPlatform(PLATFORM_ID_1, PLATFORM_NAME_1);

    // Setup mocks - the exception happens in
    // checkIfCandidatePlatformNameIsAssignedToAnotherPlatformId
    // which is called right after findByPlatformId, before any saveAndFlush
    when(mapper.toPlatform(updateRequestDTO)).thenReturn(updatedPlatform);
    when(repository.findByPlatformId(PLATFORM_ID_1)).thenReturn(Optional.of(existingPlatform));

    // No need to mock saveAndFlush as we never get to that call
    // The exception happens directly in processUpdate after comparing the names

    // Act & Assert
    DuplicateFoundException exception = assertThrows(DuplicateFoundException.class,
        () -> platformService.processUpdate(updateRequestDTO));

    // Verify the exception message contains the expected text
    assertTrue(exception.getMessage()
        .contains("cannot update an existing platformName to a different platformId"));

    // Verify interactions
    verify(mapper).toPlatform(updateRequestDTO);
    verify(repository).findByPlatformId(PLATFORM_ID_1);
    // Verify saveAndFlush is never called
    verify(repository, Mockito.never()).saveAndFlush(any(Platform.class));
  }

  @DisplayName("Test: create platform with duplicate name throws DuplicateFoundException")
  @Test
  public void testCreatePlatformWithDuplicateName() {
    // Arrange - For creation, validateDuplicateName is called which uses
    // findByPlatformName
    PlatformRequestDTO createRequestDTO = createPlatformRequestDTO(null, DUPLICATE_PLATFORM_NAME);
    Platform newPlatform = createPlatform(null, DUPLICATE_PLATFORM_NAME);
    Platform existingPlatformWithSameName = createPlatform(PLATFORM_ID_2, DUPLICATE_PLATFORM_NAME);

    when(mapper.toPlatform(createRequestDTO)).thenReturn(newPlatform);
    when(repository.findByPlatformName(DUPLICATE_PLATFORM_NAME))
        .thenReturn(Optional.of(existingPlatformWithSameName));

    // Act & Assert
    DuplicateFoundException exception = assertThrows(DuplicateFoundException.class,
        () -> platformService.processCreate(createRequestDTO));

    // Verify that exception message contains expected text
    assertTrue(exception.getMessage().contains("already exists"));

    // Verify interactions - for create, findByPlatformName IS called
    verify(mapper).toPlatform(createRequestDTO);
    verify(repository).findByPlatformName(DUPLICATE_PLATFORM_NAME);
  }

  @DisplayName(TEST_UPDATE_NONEXISTENT_PLATFORM)
  @Test
  public void testUpdateNonExistentPlatform() {
    // Arrange
    PlatformRequestDTO updateRequestDTO = createPlatformRequestDTO(NON_EXISTENT_PLATFORM_ID,
        UPDATED_PLATFORM_NAME);
    Platform updatedPlatform = createPlatform(NON_EXISTENT_PLATFORM_ID, UPDATED_PLATFORM_NAME);

    when(mapper.toPlatform(updateRequestDTO)).thenReturn(updatedPlatform);
    when(repository.findByPlatformId(NON_EXISTENT_PLATFORM_ID)).thenReturn(Optional.empty());

    // Act & Assert
    assertThrows(NotFoundException.class, () -> platformService.processUpdate(updateRequestDTO));

    // Verify interactions
    verify(mapper).toPlatform(updateRequestDTO);
    verify(repository).findByPlatformId(NON_EXISTENT_PLATFORM_ID);
  }

  @DisplayName(TEST_DELETE_PLATFORM)
  @Test
  public void testDeletePlatform() {
    // Arrange
    when(repository.existsById(PLATFORM_ID_1)).thenReturn(true);
    when(repository.findById(PLATFORM_ID_1)).thenReturn(Optional.of(platform1));
    when(enterpriseRepository.findFirst10ByPlatform(any(Platform.class))).thenReturn(
        Collections.emptyList());
    doNothing().when(repository).deleteById(PLATFORM_ID_1);

    // Act
    platformService.processDelete(PLATFORM_ID_1);

    // Assert - verify method calls
    verify(repository).existsById(PLATFORM_ID_1);
    verify(repository).findById(PLATFORM_ID_1);
    verify(enterpriseRepository).findFirst10ByPlatform(platform1);
    verify(repository).deleteById(PLATFORM_ID_1);
  }

  @DisplayName(TEST_DELETE_NONEXISTENT_PLATFORM)
  @Test
  public void testDeleteNonExistentPlatform() {
    // Arrange
    when(repository.existsById(NON_EXISTENT_PLATFORM_ID)).thenReturn(false);

    // Act & Assert
    assertThrows(NotFoundException.class,
        () -> platformService.processDelete(NON_EXISTENT_PLATFORM_ID));

    // Verify interactions
    verify(repository).existsById(NON_EXISTENT_PLATFORM_ID);
  }

  @DisplayName(TEST_DELETE_PLATFORM_WITH_DEPENDENCIES)
  @Test
  public void testDeletePlatformWithDependencies() {
    // Arrange
    when(repository.existsById(PLATFORM_ID_1)).thenReturn(true);
    when(repository.findById(PLATFORM_ID_1)).thenReturn(Optional.of(platform1));

    // Mock enterprise entities that reference this platform
    List<Enterprise> enterprises = new ArrayList<>();
    Enterprise mockEnterprise = mock(Enterprise.class);
    when(mockEnterprise.getEnterpriseId()).thenReturn("TEST-ENT-1");
    enterprises.add(mockEnterprise);

    // Make enterpriseRepository return the mocked enterprises
    when(enterpriseRepository.findFirst10ByPlatform(platform1)).thenReturn(enterprises);

    // Act & Assert
    assertThrows(ReferencedException.class,
        () -> platformService.processDelete(PLATFORM_ID_1));

    // Verify interactions
    verify(repository).existsById(PLATFORM_ID_1);
    verify(repository).findById(PLATFORM_ID_1);
    verify(enterpriseRepository).findFirst10ByPlatform(platform1);
  }

  @DisplayName(TEST_VALIDATE_PLATFORM_ID)
  @Test
  public void testValidatePlatformId() {
    // Arrange
    when(repository.existsById(PLATFORM_ID_1)).thenReturn(true);

    // Act
    platformService.validatePlatformId(PLATFORM_ID_1);

    // Verify interactions
    verify(repository).existsById(PLATFORM_ID_1);
  }

  @DisplayName(TEST_VALIDATE_PLATFORM_ID_THROWS)
  @Test
  public void testValidatePlatformIdThrowsNotFoundException() {
    // Arrange
    when(repository.existsById(NON_EXISTENT_PLATFORM_ID)).thenReturn(false);

    // Act & Assert
    assertThrows(NotFoundException.class,
        () -> platformService.validatePlatformId(NON_EXISTENT_PLATFORM_ID));

    // Verify interactions
    verify(repository).existsById(NON_EXISTENT_PLATFORM_ID);
  }

  // Helper methods for creating test data
  private PlatformRequestDTO createPlatformRequestDTO(Integer platformId, String platformName) {
    return PlatformRequestDTO.builder()
        .platformId(platformId)
        .platformName(platformName)
        .build();
  }

  private Platform createPlatform(Integer platformId, String platformName) {
    return Platform.builder()
        .platformId(platformId)
        .platformName(platformName)
        .build();
  }

  private PlatformResponseDTO createPlatformResponseDTO(Integer platformId, String platformName) {
    return PlatformResponseDTO.builder()
        .platformId(platformId)
        .platformName(platformName)
        .dateCreated(ZonedDateTime.now())
        .dateUpdated(ZonedDateTime.now())
        .build();
  }
}