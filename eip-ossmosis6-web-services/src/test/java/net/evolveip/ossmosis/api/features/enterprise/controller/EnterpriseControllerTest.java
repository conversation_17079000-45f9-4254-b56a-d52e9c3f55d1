package net.evolveip.ossmosis.api.features.enterprise.controller;


import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.time.ZonedDateTime;
import java.util.Arrays;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import net.evolveip.ossmosis.api.config.auth.DynamicJwtDecoderResolver;
import net.evolveip.ossmosis.api.features.enterprise.constant.EnterpriseConstants;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseRequestDTO;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseResponseDTO;
import net.evolveip.ossmosis.api.features.enterprise.service.EnterpriseService;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformRequestDTO;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformResponseDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ContextConfiguration(classes = DynamicJwtDecoderResolver.class)
@WebMvcTest(controllers = {EnterpriseController.class})
public class EnterpriseControllerTest {

  @MockBean
  EnterpriseService enterpriseService;
  private MockMvc mockMvc;
  @Autowired
  private ObjectMapper objectMapper;

  @BeforeEach
  public void setup() {
    this.mockMvc = MockMvcBuilders.standaloneSetup(new EnterpriseController(enterpriseService))
        .setCustomArgumentResolvers(new org.springframework.data.web.PageableHandlerMethodArgumentResolver())
        .build();
    objectMapper.registerModule(new JavaTimeModule());
  }

  @WithMockUser(roles = "enterprises_read")
  @DisplayName("Test: get all enterprises")
  @Test
  public void testGetAllEnterprises() throws Exception {
    String enterpriseId1 = "**********";
    String enterpriseName1 = "Evolve IP Wayne";

    String enterpriseId2 = "**********";
    String enterpriseName2 = "ShannonTestingLabB";

    String accountCountry = "USA";

    Integer platformId = 1;
    ZonedDateTime dateCreated = ZonedDateTime.now();
    ZonedDateTime dateUpdated = ZonedDateTime.now();
    String platformName = "NewPlatformName";

    PlatformResponseDTO platform = PlatformResponseDTO
        .builder()
        .platformId(platformId)
        .dateCreated(dateCreated)
        .dateUpdated(dateUpdated)
        .platformName(platformName)
        .build();

    EnterpriseResponseDTO enterpriseResponseDTO1 = EnterpriseResponseDTO
        .builder()
        .enterpriseId(enterpriseId1)
        .dateCreated(dateCreated)
        .dateUpdated(dateUpdated)
        .billingId(enterpriseId1)
        .enterpriseName(enterpriseName1)
        .accountCountry(accountCountry)
        .platform(platform)
        .build();

    EnterpriseResponseDTO enterpriseResponseDTO2 = EnterpriseResponseDTO
        .builder()
        .enterpriseId(enterpriseId2)
        .dateCreated(dateCreated)
        .dateUpdated(dateUpdated)
        .billingId(enterpriseId2)
        .enterpriseName(enterpriseName2)
        .accountCountry(accountCountry)
        .platform(platform)
        .build();

    var enterpriseResponseDTOList = Arrays.asList(enterpriseResponseDTO1, enterpriseResponseDTO2);

    // Create a Page of EnterpriseResponseDTO
    Page<EnterpriseResponseDTO> page = new PageImpl<>(
        enterpriseResponseDTOList, 
        PageRequest.of(0, 10), 
        enterpriseResponseDTOList.size()
    );

    // Mock the processGet method instead of processGetList
    when(enterpriseService.processGet(any(String.class), any(Pageable.class))).thenReturn(page);

    // Test the GET /enterprises endpoint
    mockMvc.perform(MockMvcRequestBuilders
            .get("/enterprises"))
        .andDo(print())
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON));
  }

  @WithMockUser(roles = "enterprises_read")
  @DisplayName("Test: get an enterprise by enterpriseId")
  @Test
  public void testGetAnEnterpriseByEnterpriseId() throws Exception {
    String enterpriseId = "**********";
    String enterpriseName = "Evolve IP Wayne";
    String accountCountry = "USA";
    Integer platformId = 1;
    ZonedDateTime dateCreated = ZonedDateTime.now();
    ZonedDateTime dateUpdated = ZonedDateTime.now();
    String platformName = "NewPlatformName";

    PlatformResponseDTO platform = PlatformResponseDTO
        .builder()
        .platformId(platformId)
        .dateCreated(dateCreated)
        .dateUpdated(dateUpdated)
        .platformName(platformName)
        .build();

    EnterpriseResponseDTO enterpriseResponseDTO = EnterpriseResponseDTO
        .builder()
        .enterpriseId(enterpriseId)
        .dateCreated(dateCreated)
        .dateUpdated(dateUpdated)
        .billingId(enterpriseId)
        .enterpriseName(enterpriseName)
        .accountCountry(accountCountry)
        .platform(platform)
        .build();

    when(enterpriseService.processGetById(any(String.class))).thenReturn(enterpriseResponseDTO);

    // TODO: inspect the jsonPath and match the response body's json with the originating object's
    mockMvc.perform(MockMvcRequestBuilders.get("/enterprises/{enterpriseId}", enterpriseId))
        .andDo(print())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload", is(notNullValue())))
        .andExpect(jsonPath("$.errors").isArray())
        .andExpect(jsonPath("$.errors", hasSize(0)));
  }

  @WithMockUser(roles = {"enterprises_create", "enterprises_read"})
  @DisplayName("Test: create an enterprise by enterpriseRequestDTO")
  @Test
  public void testCreateAnEnterpriseByEnterpriseRequestDTO() throws Exception {
    String enterpriseId = "**********";
    String enterpriseName = "Evolve IP Wayne";
    String accountCountry = "USA";
    Integer platformId = 1;
    ZonedDateTime dateCreated = ZonedDateTime.now();
    ZonedDateTime dateUpdated = ZonedDateTime.now();
    String platformName = "NewPlatformName";

    PlatformResponseDTO platform = PlatformResponseDTO
        .builder()
        .platformId(platformId)
        .dateCreated(dateCreated)
        .dateUpdated(dateUpdated)
        .platformName(platformName)
        .build();

    PlatformRequestDTO platformRequestDTO = PlatformRequestDTO
        .builder()
        .platformId(platformId)
        .platformName(platformName)
        .build();

    EnterpriseRequestDTO enterpriseRequestDTO = EnterpriseRequestDTO
        .builder()
        .enterpriseId(enterpriseId)
        .billingId(enterpriseId)
        .enterpriseName(enterpriseName)
        .accountCountry(accountCountry)
        .platformId(platformRequestDTO.getPlatformId())
        .parentEnterpriseId(EnterpriseConstants.ROOT_ENTERPRISE_ID)
        .build();

    // No need to mock return value as the method is void and starts a temporal workflow
    doNothing().when(enterpriseService).processCreate(enterpriseRequestDTO);

    mockMvc.perform(MockMvcRequestBuilders
            .post("/enterprises")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(enterpriseRequestDTO)))
        .andDo(print())
        .andExpect(status().isAccepted()); // 202 Accepted status for temporal workflow
  }

  @WithMockUser(roles = {"enterprises_update", "enterprises_read"})
  @DisplayName("Test: update an enterprise by enterpriseRequestDTO")
  @Test
  public void testUpdateAnEnterpriseByEnterpriseId() throws Exception {
    String enterpriseId = "**********";
    String enterpriseName = "Evolve IP Wayne";
    String accountCountry = "USA";
    Integer platformId = 1;
    ZonedDateTime dateCreated = ZonedDateTime.now();
    ZonedDateTime dateUpdated = ZonedDateTime.now();
    String platformName = "NewPlatformName";

    PlatformRequestDTO platformRequestDTO = PlatformRequestDTO
        .builder()
        .platformId(platformId)
        .platformName(platformName)
        .build();

    EnterpriseRequestDTO enterpriseRequestDTO = EnterpriseRequestDTO
        .builder()
        .enterpriseId(enterpriseId)
        .billingId(enterpriseId)
        .enterpriseName(enterpriseName)
        .accountCountry(accountCountry)
        .platformId(platformRequestDTO.getPlatformId())
        .parentEnterpriseId(EnterpriseConstants.ROOT_ENTERPRISE_ID)
        .build();

    // No need to mock return value as the method is void and starts a temporal workflow
    doNothing().when(enterpriseService).processUpdate(enterpriseRequestDTO);

    mockMvc.perform(MockMvcRequestBuilders
            .put("/enterprises")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(enterpriseRequestDTO)))
        .andDo(print())
        .andExpect(status().isAccepted()); // 202 Accepted status for temporal workflow
  }

  @WithMockUser(roles = {"enterprises_delete"})
  @DisplayName("Test: delete an enterprise by enterpriseId")
  @Test
  public void testDeleteAnEnterpriseByEnterpriseId() throws Exception {
    String enterpriseId = "**********";

    // No need to mock return value as the method is void and starts a temporal workflow
    doNothing().when(enterpriseService).processDelete(enterpriseId);

    mockMvc.perform(MockMvcRequestBuilders
            .delete("/enterprises/{enterpriseId}", enterpriseId))
        .andDo(print())
        .andExpect(status().isAccepted()); // 202 Accepted status for temporal workflow
  }
}
