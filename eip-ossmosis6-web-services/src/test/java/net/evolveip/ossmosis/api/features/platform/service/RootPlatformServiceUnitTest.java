package net.evolveip.ossmosis.api.features.platform.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Optional;
import net.evolveip.ossmosis.api.features.platform.constant.PlatformConstants;
import net.evolveip.ossmosis.api.features.platform.entity.Platform;
import net.evolveip.ossmosis.api.features.platform.repository.PlatformRepository;
import net.evolveip.ossmosis.api.utils.exceptions.NotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class RootPlatformServiceUnitTest {

  private static final String ROOT_PLATFORM_NAME = PlatformConstants.ROOT_PLATFORM_NAME;

  @Mock
  private PlatformRepository platformRepository;

  @InjectMocks
  private RootPlatformService rootPlatformService;

  private Platform rootPlatform;

  @BeforeEach
  void setUp() {
    // Setup root platform
    rootPlatform = createRootPlatform();
  }

  @Test
  void createRootPlatform_whenRootPlatformDoesNotExist_shouldCreateRootPlatform() {
    // Given
    when(platformRepository.findByPlatformName(ROOT_PLATFORM_NAME)).thenReturn(Optional.empty());
    when(platformRepository.save(any(Platform.class))).thenReturn(rootPlatform);

    // When
    rootPlatformService.createRootPlatform();

    // Then
    verify(platformRepository, times(1)).findByPlatformName(ROOT_PLATFORM_NAME);
    verify(platformRepository, times(1)).save(any(Platform.class));
  }

  @Test
  void createRootPlatform_whenRootPlatformExists_shouldNotCreateRootPlatform() {
    // Given
    when(platformRepository.findByPlatformName(ROOT_PLATFORM_NAME)).thenReturn(
        Optional.of(rootPlatform));

    // When
    rootPlatformService.createRootPlatform();

    // Then
    verify(platformRepository, times(1)).findByPlatformName(ROOT_PLATFORM_NAME);
    verify(platformRepository, times(0)).save(any(Platform.class));
  }

  @Test
  void getRootPlatform_whenRootPlatformExists_shouldReturnRootPlatform() {
    // Given
    when(platformRepository.findByPlatformName(ROOT_PLATFORM_NAME)).thenReturn(
        Optional.of(rootPlatform));

    // When
    Platform result = rootPlatformService.getRootPlatform();

    // Then
    verify(platformRepository, times(1)).findByPlatformName(ROOT_PLATFORM_NAME);
    assertEquals(rootPlatform, result);
    assertEquals(ROOT_PLATFORM_NAME, result.getPlatformName());
  }

  @Test
  void getRootPlatform_whenRootPlatformDoesNotExist_shouldThrowException() {
    // Given
    when(platformRepository.findByPlatformName(ROOT_PLATFORM_NAME)).thenReturn(Optional.empty());

    // When & Then
    assertThrows(NotFoundException.class, () -> rootPlatformService.getRootPlatform());
    verify(platformRepository, times(1)).findByPlatformName(ROOT_PLATFORM_NAME);
  }

  // Helper method to create test entity
  private Platform createRootPlatform() {
    return Platform.builder()
        .platformId(1)
        .platformName(ROOT_PLATFORM_NAME)
        .build();
  }
}