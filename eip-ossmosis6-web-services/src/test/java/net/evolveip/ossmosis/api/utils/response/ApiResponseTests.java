package net.evolveip.ossmosis.api.utils.response;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;
import java.util.UUID;
import net.evolveip.ossmosis.api.config.audit.AuditFilterConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.postgresql.util.PSQLException;
import org.postgresql.util.ServerErrorMessage;
import org.slf4j.MDC;
import org.springframework.dao.QueryTimeoutException;
import org.springframework.http.HttpStatus;

public class ApiResponseTests {

  private UUID reqId;

  @BeforeEach
  public void setup() {
    reqId = UUID.randomUUID();
    MDC.put(AuditFilterConfig.DEFAULT_MDC_UUID_TOKEN_KEY, reqId.toString());
  }

  @Test
  @DisplayName("Test: test static constructor of ApiResponse for data param only")
  public void testApiResponseMethodDataConstructor() {
    ApiResponse<String> response = ApiResponse.create("test");

    assertTrue(response.isSuccessful());
    assertEquals("test", response.getPayload());
    assertEquals(reqId, response.getResponseId());
  }

  @Test
  @DisplayName("Test: test static constructor of ApiResponse for data and api response errors params")
  public void testApiResponseMethodDataAndApiResponseErrorsConstructor() {
    ArrayList<ApiResponseError> errors = new ArrayList<>();
    errors.add(ApiResponseError.builder()
        .header("Error")
        .errorMessage("Try again.")
        .build());

    ApiResponse<String> response = ApiResponse.create(null, false, errors, HttpStatus.BAD_REQUEST);

    assertFalse(response.isSuccessful());
    assertNull(response.getPayload());
    assertEquals(reqId, response.getResponseId());
    assertEquals(errors, response.getErrors());
    assertEquals(HttpStatus.BAD_REQUEST.value(), response.getHttpStatusCode());
  }


  @Test
  @DisplayName("Test: test static constructor of ApiResponse for data and error string params")
  public void testApiResponseMethodDataAndErrorStrConstructor() {
    ApiResponse<String> response = ApiResponse.create(null, false, "Error", HttpStatus.BAD_REQUEST);

    assertFalse(response.isSuccessful());
    assertNull(response.getPayload());
    assertEquals(reqId, response.getResponseId());
    assertEquals(1, response.getErrors().size());
    assertEquals(HttpStatus.BAD_REQUEST.value(), response.getHttpStatusCode());
  }

  @Test
  @DisplayName("Test: test static constructor of ApiResponse for exception param with psql message from db")
  public void testApiResponseMethodExceptionConstructorWithPSQLException() {
    ServerErrorMessage serverErrorMessage = new ServerErrorMessage(
        "Rexec_stmt_raise\0SERROR\0C57014\0VERROR\0Fpl_exec.c\0WPL/pgSQL"
            + "function partnerprovider.fn_logins_create(character varying,uuid) line 1 at RAISE\0"
            + "L3909\0MDuplicate email(s): <EMAIL>. Login ids must be less then one.");

    QueryTimeoutException e = new QueryTimeoutException("Error",
        new PSQLException(serverErrorMessage));

    ApiResponse<String> response = ApiResponse.create(e);

    assertFalse(response.isSuccessful());
    assertNull(response.getPayload());
    assertEquals(reqId, response.getResponseId());
    assertEquals(1, response.getErrors().size());
    assertTrue(response.getErrors().get(0).getErrorMessage()
        .contains("Duplicate email(s): <EMAIL>."));
    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getHttpStatusCode());
  }

  @Test
  @DisplayName("Test: test static constructor of ApiResponse for exception param")
  public void testApiResponseMethodExceptionConstructor() {
    ApiResponse<String> response = ApiResponse.create(new Exception("Test Error"));

    assertFalse(response.isSuccessful());
    assertNull(response.getPayload());
    assertEquals(reqId, response.getResponseId());
    assertEquals(1, response.getErrors().size());
    assertTrue(response.getErrors().get(0).getErrorMessage().contains("Test Error"));
    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getHttpStatusCode());
  }

}
