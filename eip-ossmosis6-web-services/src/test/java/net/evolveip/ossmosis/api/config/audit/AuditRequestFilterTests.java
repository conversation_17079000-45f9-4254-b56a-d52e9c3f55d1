package net.evolveip.ossmosis.api.config.audit;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;

import jakarta.servlet.ServletException;
import java.io.IOException;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;
import org.springframework.http.HttpMethod;
import org.springframework.mock.web.MockFilterChain;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

public class AuditRequestFilterTests {

  private final AuditRequestFilter filter = new AuditRequestFilter();

  @Test
  @DisplayName("Test: confirm auditRequestFilter requestId setup is functioning.")
  public void auditRequestFilterTest() throws ServletException, IOException {
    MockHttpServletRequest req = new MockHttpServletRequest(HttpMethod.GET.name(), "/logins");
    MockHttpServletResponse res = new MockHttpServletResponse();
    MockFilterChain chain = new MockFilterChain();
    assertEquals(new UUID(0, 0), AuditFilterConfig.getCurrentRequestId());
    filter.doFilter(req, res, chain);
    assertFalse(filter.isAsyncDispatch(req));
    assertFalse(filter.shouldNotFilterErrorDispatch());
    assertEquals(new UUID(0, 0), AuditFilterConfig.getCurrentRequestId());
    assertNull(MDC.get(AuditFilterConfig.DEFAULT_MDC_UUID_TOKEN_KEY));
  }

}
