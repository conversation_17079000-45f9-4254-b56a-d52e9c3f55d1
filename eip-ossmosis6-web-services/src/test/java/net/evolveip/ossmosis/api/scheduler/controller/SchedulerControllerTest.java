package net.evolveip.ossmosis.api.scheduler.controller;


import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import net.evolveip.ossmosis.api.config.auth.DynamicJwtDecoderResolver;
import net.evolveip.ossmosis.api.features.role.constant.RoleConstants;
import net.evolveip.ossmosis.api.scheduler.common.dto.SchedulerJobsStatesDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.SchedulerMetadataDTO;
import net.evolveip.ossmosis.api.scheduler.service.scheduler.SchedulerService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ContextConfiguration(classes = DynamicJwtDecoderResolver.class)
@WebMvcTest(controllers = {SchedulerController.class})
public class SchedulerControllerTest {

  @MockBean
  private SchedulerService schedulerService;
  private MockMvc mockMvc;
  private SchedulerMetadataDTO metadataStartDTO, metadataStandbyDTO, metadataDTO;
  private SchedulerJobsStatesDTO jobsStatesAllResumedDTO, jobStatesAllPausedDTO, jobsStatesDTO;

  @BeforeEach
  public void setUp() throws ParseException {
    /***
     *         "schedulerName": "Ossmosis6ClusteredQuartzScheduler",
     *         "version": "2.3.2",
     *         "schedulerInstanceId": "auto",
     *         "started": true,
     *         "shutdown": false,
     *         "inStandbyMode": false,
     *         "startTime": "2024-06-21T18:25:40.392+00:00",
     *         "schedulerClass": "org.quartz.impl.StdScheduler",
     *         "jobStoreClass": "org.springframework.scheduling.quartz.LocalDataSourceJobStore",
     *         "threadPoolClass": "org.quartz.simpl.SimpleThreadPool",
     *         "threadPoolSize": 5,
     *         "schedulerRemote": false,
     *         "numberOfJobsExecuted": 0,
     *         "jobStoreSupportsPersistence": true,
     *         "jobStoreClustered": true
     */
    this.mockMvc = MockMvcBuilders.standaloneSetup(new SchedulerController(schedulerService))
        .build();
    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
    String dateInString = "2024-06-21 18:25:40";
    Date date = formatter.parse(dateInString);

    metadataDTO = metadataStartDTO = SchedulerMetadataDTO
        .builder()
        .schedulerName("Ossmosis6ClusteredQuartzScheduler")
        .version("2.3.2")
        .schedulerInstanceId("auto")
        .started(true)
        .shutdown(false)
        .inStandbyMode(false)
        .startTime(date)
        .schedulerClass("org.quartz.impl.StdScheduler")
        .jobStoreClass("org.springframework.scheduling.quartz.LocalDataSourceJobStore")
        .threadPoolClass("org.quartz.simpl.SimpleThreadPool")
        .threadPoolSize(5)
        .schedulerRemote(false)
        .numberOfJobsExecuted(10)
        .jobStoreClustered(true)
        .jobStoreClustered(true)
        .build();

    metadataStandbyDTO = SchedulerMetadataDTO
        .builder()
        .schedulerName("Ossmosis6ClusteredQuartzScheduler")
        .version("2.3.2")
        .schedulerInstanceId("auto")
        .started(true)
        .shutdown(false)
        .inStandbyMode(true)
        .startTime(date)
        .schedulerClass("org.quartz.impl.StdScheduler")
        .jobStoreClass("org.springframework.scheduling.quartz.LocalDataSourceJobStore")
        .threadPoolClass("org.quartz.simpl.SimpleThreadPool")
        .threadPoolSize(5)
        .schedulerRemote(false)
        .numberOfJobsExecuted(10)
        .jobStoreClustered(true)
        .jobStoreClustered(true)
        .build();

    jobStatesAllPausedDTO = SchedulerJobsStatesDTO
        .builder()
        .normal(0)
        .paused(10)
        .complete(0)
        .blocked(0)
        .error(0)
        .none(0)
        .build();

    jobsStatesAllResumedDTO = SchedulerJobsStatesDTO
        .builder()
        .normal(10)
        .paused(0)
        .complete(0)
        .blocked(0)
        .error(0)
        .none(0)
        .build();

    jobsStatesDTO = SchedulerJobsStatesDTO
        .builder()
        .normal(10)
        .paused(0)
        .complete(10)
        .blocked(0)
        .error(0)
        .none(0)
        .build();
  }

  @WithMockUser(roles = RoleConstants.TOOLBOX_FULL_ACCESS)
  @DisplayName("Test: start scheduler")
  @Test
  public void testDoStart() throws Exception {

    ApiResponse<SchedulerMetadataDTO> apiResponse = ApiResponse.create(metadataStartDTO);
    when(schedulerService.processStart()).thenReturn(apiResponse);

    mockMvc.perform(MockMvcRequestBuilders.post("/scheduler/start"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload.started", is(true)))
        .andExpect(jsonPath("$.payload.shutdown", is(false)))
        .andExpect(jsonPath("$.payload.inStandbyMode", is(false)))
        .andExpect(jsonPath("$.errors").isArray())
        .andExpect(jsonPath("$.errors", hasSize(0)))
        .andDo(print());
  }

  @WithMockUser(roles = RoleConstants.TOOLBOX_FULL_ACCESS)
  @DisplayName("Test: standby scheduler")
  @Test
  public void testDoStandby() throws Exception {
    ApiResponse<SchedulerMetadataDTO> apiResponse = ApiResponse.create(metadataStandbyDTO);
    when(schedulerService.processStandby()).thenReturn(apiResponse);

    mockMvc.perform(MockMvcRequestBuilders.post("/scheduler/standby"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload", is(notNullValue())))
        .andExpect(jsonPath("$.payload.started", is(true)))
        .andExpect(jsonPath("$.payload.shutdown", is(false)))
        .andExpect(jsonPath("$.payload.inStandbyMode", is(true)))
        .andExpect(jsonPath("$.errors").isArray())
        .andExpect(jsonPath("$.errors", hasSize(0)))
        .andDo(print());
  }

  @WithMockUser(roles = RoleConstants.TOOLBOX_FULL_ACCESS)
  @DisplayName("Test: get status on scheduler")
  @Test
  public void testDoStatus() throws Exception {
    ApiResponse<SchedulerMetadataDTO> apiResponse = ApiResponse.create(metadataDTO);
    when(schedulerService.processStatus()).thenReturn(apiResponse);

    mockMvc.perform(MockMvcRequestBuilders.get("/scheduler/status"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload", is(notNullValue())))
        .andExpect(jsonPath("$.payload.started", is(true)))
        .andExpect(jsonPath("$.payload.shutdown", is(false)))
        .andExpect(jsonPath("$.payload.inStandbyMode", is(false)))
        .andExpect(jsonPath("$.errors").isArray())
        .andExpect(jsonPath("$.errors", hasSize(0)))
        .andDo(print());
  }


  @WithMockUser(roles = RoleConstants.TOOLBOX_FULL_ACCESS)
  @DisplayName("Test: pause all jobs in scheduler")
  @Test
  public void testDoPauseAll() throws Exception {
    ApiResponse<SchedulerJobsStatesDTO> apiResponse = ApiResponse.create(jobStatesAllPausedDTO);
    when(schedulerService.processPauseAll()).thenReturn(apiResponse);

    mockMvc.perform(MockMvcRequestBuilders.post("/scheduler/pauseall"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload", is(notNullValue())))
        .andExpect(jsonPath("$.payload.normal", is(0)))
        .andExpect(jsonPath("$.payload.paused", is(10)))
        .andExpect(jsonPath("$.payload.complete", is(0)))
        .andExpect(jsonPath("$.errors").isArray())
        .andExpect(jsonPath("$.errors", hasSize(0)))
        .andDo(print());
  }

  @WithMockUser(roles = RoleConstants.TOOLBOX_FULL_ACCESS)
  @DisplayName("Test: resume all jobs in scheduler")
  @Test
  public void testDoResumeAll() throws Exception {
    ApiResponse<SchedulerJobsStatesDTO> apiResponse = ApiResponse.create(jobsStatesAllResumedDTO);
    when(schedulerService.processResumeAll()).thenReturn(apiResponse);

    mockMvc.perform(MockMvcRequestBuilders.post("/scheduler/resumeall"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload", is(notNullValue())))
        .andExpect(jsonPath("$.payload.normal", is(10)))
        .andExpect(jsonPath("$.payload.paused", is(0)))
        .andExpect(jsonPath("$.payload.complete", is(0)))
        .andExpect(jsonPath("$.errors").isArray())
        .andExpect(jsonPath("$.errors", hasSize(0)))
        .andDo(print());
  }

  @WithMockUser(roles = "reports_read")
  @DisplayName("Test: get all jobs in scheduler")
  @Test
  public void testDoJobsStatus() throws Exception {
    ApiResponse<SchedulerJobsStatesDTO> apiResponse = ApiResponse.create(jobsStatesDTO);
    when(schedulerService.processJobsStatus()).thenReturn(apiResponse);

    mockMvc.perform(MockMvcRequestBuilders.get("/scheduler/jobsstatus"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload", is(notNullValue())))
        .andExpect(jsonPath("$.payload.normal", is(10)))
        .andExpect(jsonPath("$.payload.paused", is(0)))
        .andExpect(jsonPath("$.payload.complete", is(10)))
        .andExpect(jsonPath("$.errors").isArray())
        .andExpect(jsonPath("$.errors", hasSize(0)))
        .andDo(print());
  }
}