package net.evolveip.ossmosis.api.config.controller;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.ConstraintViolationException;
import net.evolveip.ossmosis.api.config.audit.AuditFilterConfig;
import net.evolveip.ossmosis.api.config.audit.AuditRequestFilter;
import net.evolveip.ossmosis.api.features.login.constant.LoginConstants;
import net.evolveip.ossmosis.api.features.login.controller.LoginController;
import net.evolveip.ossmosis.api.features.login.dto.LoginRequestDTO;
import net.evolveip.ossmosis.api.features.login.service.LoginService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.context.WebApplicationContext;

@WebMvcTest()
@ContextConfiguration(classes = {LoginController.class, AuditFilterConfig.class,
    AuditRequestFilter.class,
    OssmosisExceptionHandler.class})
public class OssmosisExceptionHandlerTests {

  private MockMvc mockMvc;


  @MockBean
  private LoginService loginService;

  @InjectMocks
  private LoginController loginController;

  @Autowired
  private WebApplicationContext context;

  @BeforeEach
  public void setup() {
    this.mockMvc = MockMvcBuilders.webAppContextSetup(context)
        .build();
  }

  @Test
  @WithMockUser(authorities = LoginConstants.LOGINS_READ)
  @DisplayName("Test: confirm OssmosisExceptionHandler is configured and returns ConstraintViolationException.")
  public void testOssmosisExceptionHandlerConstraintViolationException() throws Exception {
    mockMvc.perform(MockMvcRequestBuilders.get("/enterprise/1/logins/email/{loginEmail}", "test"))
        .andExpect(status().isBadRequest())
        .andExpect(result -> assertTrue(
            result.getResolvedException() instanceof ConstraintViolationException));
  }

  @Test
  @WithMockUser()
  @DisplayName("Test: confirm OssmosisExceptionHandler is configured and returns AccessDeniedException.")
  public void testOssmosisExceptionHandlerAccessDeniedException() throws Exception {
    Mockito.when(loginService.processGetByEmail(anyString(), anyString()))
        .thenThrow(new AccessDeniedException("err"));
    mockMvc.perform(
            MockMvcRequestBuilders.get("/enterprise/1/logins/email/{loginEmail}", "<EMAIL>"))
        .andExpect(status().isForbidden())
        .andExpect(result -> assertTrue(
            result.getResolvedException() instanceof AccessDeniedException));
  }


  @Test
  @WithMockUser(authorities = LoginConstants.LOGINS_UPDATE)
  @DisplayName("Test: confirm OssmosisExceptionHandler is configured and returns MethodArgumentNotValidException.")
  public void testOssmosisExceptionHandlerMethodArgumentNotValidException() throws Exception {
    LoginRequestDTO loginRequestDTO = LoginRequestDTO.builder()
        .loginId(1L).active(true)
        .loginGroup("test").loginEmail("<EMAIL>")
        .loginNameFirst("first").loginNameLast("last")
        .loginPhoneNumber("this value will cause the exception")
        .loginPrimaryEnterpriseId("000100101")
        .build();
    mockMvc.perform(MockMvcRequestBuilders.post("/enterprise/1/logins")
            .content((new ObjectMapper()).writeValueAsString(loginRequestDTO))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andExpect(result -> assertTrue(
            result.getResolvedException() instanceof MethodArgumentNotValidException));
  }
}
