package net.evolveip.ossmosis.api.features.platform.controller;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.isA;
import static org.hamcrest.Matchers.notNullValue;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import net.evolveip.ossmosis.api.config.auth.DynamicJwtDecoderResolver;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformRequestDTO;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformResponseDTO;
import net.evolveip.ossmosis.api.features.platform.service.PlatformService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ContextConfiguration(classes = DynamicJwtDecoderResolver.class)
@WebMvcTest(controllers = {PlatformController.class})
public class PlatformControllerTest {

  @MockBean
  private PlatformService platformService;
  private MockMvc mockMvc;

  @BeforeEach
  public void setup() {
    this.mockMvc = MockMvcBuilders.standaloneSetup(new PlatformController(platformService)).build();
  }

  @WithMockUser(roles = "platforms_read")
  @DisplayName("Test: get all platforms")
  @Test
  public void testGetAllPlatforms() throws Exception {
    int platformId1 = 1;
    String platformName1 = "NewPlatform1";

    int platformId2 = 2;
    String platformName2 = "NewPlatform2";

    PlatformResponseDTO platformResponseDTO1 = new PlatformResponseDTO();
    platformResponseDTO1.setPlatformId(platformId1);
    platformResponseDTO1.setPlatformName(platformName1);

    PlatformResponseDTO platformResponseDTO2 = new PlatformResponseDTO();
    platformResponseDTO2.setPlatformId(platformId2);
    platformResponseDTO2.setPlatformName(platformName2);

    List<PlatformResponseDTO> platformsList = Arrays.asList(platformResponseDTO1,
        platformResponseDTO2);

    when(platformService.processGetList()).thenReturn(platformsList);

    mockMvc.perform(MockMvcRequestBuilders.get("/platforms")).andDo(print())
        .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload", is(notNullValue())))
        .andExpect(jsonPath("$.payload", hasSize(2)))
        .andExpect(jsonPath("$.errors").isArray())
        .andExpect(jsonPath("$.errors", hasSize(0)));
  }

  @WithMockUser(roles = "platforms_read")
  @DisplayName("Test: get a platform by platformId")
  @Test
  public void testGetPlatformById() throws Exception {
    int platformId = 1;
    String platformName = "NewPlatformName";
    ZonedDateTime dateCreated = ZonedDateTime.now();
    ZonedDateTime dateUpdated = ZonedDateTime.now();

    PlatformResponseDTO platformResponseDTO = PlatformResponseDTO.builder().platformId(platformId)
        .dateCreated(dateCreated).dateUpdated(dateUpdated).platformName(platformName).build();

    // mock the service response:
    when(platformService.processGetById(platformId)).thenReturn(platformResponseDTO);

    // execute the call:
    mockMvc.perform(MockMvcRequestBuilders.get("/platforms/" + platformId)).andDo(print())
        .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload", is(notNullValue())))
        .andExpect(jsonPath("$.payload", isA(LinkedHashMap.class)))
        .andExpect(jsonPath("$.errors").isArray())
        .andExpect(jsonPath("$.errors", hasSize(0)));
  }

  @WithMockUser(roles = "platforms_create")
  @DisplayName("Test: create platform by platformRequestDTO")
  @Test
  public void testCreatePlatformByPlatformRequestDTO() throws Exception {
    int platformId = 1;
    String platformName = "NewPlatformName";
    ZonedDateTime dateCreated = ZonedDateTime.now();
    ZonedDateTime dateUpdated = ZonedDateTime.now();

    PlatformRequestDTO platformRequestDTO = PlatformRequestDTO.builder().platformId(platformId)
        .platformName(platformName).build();

    PlatformResponseDTO platformResponseDTO = PlatformResponseDTO.builder().platformId(platformId)
        .dateCreated(dateCreated).dateUpdated(dateUpdated).platformName(platformName).build();

    when(platformService.processCreate(platformRequestDTO)).thenReturn(platformResponseDTO);

    mockMvc.perform(
            MockMvcRequestBuilders.post("/platforms").contentType(MediaType.APPLICATION_JSON)
                .content(new ObjectMapper().writeValueAsString(platformRequestDTO)))
        .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload", is(notNullValue())))
        .andExpect(jsonPath("$.payload", isA(LinkedHashMap.class)))
        .andExpect(jsonPath("$.errors").isArray())
        .andExpect(jsonPath("$.errors", hasSize(0)));
  }

  @WithMockUser(roles = {"platforms_update", "platforms_read"})
  @DisplayName("Test: update platform by platformId")
  @Test
  public void testUpdatePlatformByPlatformId() throws Exception {
    int platformId = 1;
    String platformName = "NewPlatformName";
    ZonedDateTime dateCreated = ZonedDateTime.now();
    ZonedDateTime dateUpdated = ZonedDateTime.now();

    PlatformRequestDTO platformRequestDTO = PlatformRequestDTO.builder().platformId(platformId)
        .platformName(platformName).build();

    PlatformResponseDTO platformResponseDTO = PlatformResponseDTO.builder().platformId(platformId)
        .platformName(platformName).dateCreated(dateCreated).dateUpdated(dateUpdated).build();

    when(platformService.processUpdate(platformRequestDTO)).thenReturn(platformResponseDTO);

    mockMvc.perform(MockMvcRequestBuilders.put("/platforms/{platformId}", platformId)
            .contentType(MediaType.APPLICATION_JSON)
            .content(new ObjectMapper().writeValueAsString(platformRequestDTO)))
        .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload", is(notNullValue())))
        .andExpect(jsonPath("$.payload", isA(LinkedHashMap.class)))
        .andExpect(jsonPath("$.errors").isArray())
        .andExpect(jsonPath("$.errors", hasSize(0)));
  }

  @WithMockUser(roles = {"platforms_delete"})
  @DisplayName("Test: delete platform by platformId")
  @Test
  public void testDeletePlatformByPlatformId() throws Exception {
    int platformId = 1;

    platformService.processDelete(anyInt());

    mockMvc.perform(MockMvcRequestBuilders.delete("/platforms/{platformId}", platformId))
        .andExpect(status().isOk()).andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload", is(notNullValue())))
        .andExpect(jsonPath("$.payload", isA(Boolean.class)))
        .andExpect(jsonPath("$.errors").isArray())
        .andExpect(jsonPath("$.errors", hasSize(0)));
  }
}