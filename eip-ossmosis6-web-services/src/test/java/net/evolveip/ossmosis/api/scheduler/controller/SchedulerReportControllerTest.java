package net.evolveip.ossmosis.api.scheduler.controller;

import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.CRON_JOB_EXPRESSION;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.FREQUENCY;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.FREQUENCY_UNITS;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.REPORT_RUN_TIME;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.SCHEDULE_TYPE;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.SELECTED_MONTH_RUN_DAYS;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.SELECTED_WEEK_RUN_DAYS;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.USER_TIME_ZONE;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import net.evolveip.ossmosis.api.config.auth.DynamicJwtDecoderResolver;
import net.evolveip.ossmosis.api.features.calldetails.constant.CallDetailConstants;
import net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants;
import net.evolveip.ossmosis.api.scheduler.common.dto.CallDetailReportDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.CronScheduleDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.EmailDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.JobDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.JobSpecificationDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.SimpleScheduleDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.TriggerDTO;
import net.evolveip.ossmosis.api.scheduler.common.enums.WeekDays;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateCallDetailReport;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateCronScheduleDefinition;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateCronScheduleJob;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateEmailDefinition;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateJobDefinition;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateSimpleScheduleDefinition;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateSimpleScheduleJob;
import net.evolveip.ossmosis.api.scheduler.common.response.HttpResponseGetScheduledJob;
import net.evolveip.ossmosis.api.scheduler.job.BaseJobDetail;
import net.evolveip.ossmosis.api.scheduler.service.scheduler.ScheduledCallDetailService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.quartz.JobDataMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ActiveProfiles("test")
@ContextConfiguration(classes = DynamicJwtDecoderResolver.class)
@WebMvcTest(controllers = {ScheduledCallDetailController.class})
@TestPropertySource(locations = "/test.properties")
public class SchedulerReportControllerTest implements BaseJobDetail {

  @MockBean
  private ScheduledCallDetailService schedulerReportService;

  @Autowired
  private ObjectMapper objectMapper;

  private MockMvc mockMvc;
  private HttpResponseGetScheduledJob reportDTO;
  private EmailDefinitionDTO emailDefinition;
  private JobSpecificationDTO jobSpecification;
  private JobDefinitionDTO jobDefinition;
  private JobDataMap jobScheduleDefinition;
  private String enterpriseId, enterpriseName, fromEmail, jobId;
  private List<String> toEmails;

  @BeforeEach
  void setUp() {
    this.mockMvc = MockMvcBuilders.standaloneSetup(
        new ScheduledCallDetailController(schedulerReportService)).build();

    jobId = generateJobId();
    enterpriseId = this.getEnterpriseId();
    enterpriseName = this.getEnterpriseName();
    fromEmail = "<EMAIL>";
    toEmails = new ArrayList<>(Arrays.asList("<EMAIL>", "<EMAIL>"));

    // emailDefinition:
    emailDefinition = EmailDefinitionDTO.builder().enterpriseId(enterpriseId)
        .enterpriseName(enterpriseName).fromEmail(fromEmail).toEmail(toEmails).build();

    // jobScheduleDefinition:
    SimpleScheduleDefinitionDTO simpleScheduleDefinitionDTO = getSimpleScheduleDefinitionDTO();
    jobScheduleDefinition = createScheduleDefinitionMap(simpleScheduleDefinitionDTO);

    // jobRunTimes:
    List<TriggerDTO> jobRunTimes = new ArrayList<>(Collections.singletonList(getTriggerDTO()));

    // jobSpecification:
    jobSpecification = getJobSpecificationDTO();

    // jobDefinition:
    jobDefinition = getJobDefinition();

    reportDTO = HttpResponseGetScheduledJob.builder().emailDefinition(emailDefinition)
        .jobScheduleDefinition(jobScheduleDefinition).jobRuntimes(jobRunTimes)
        .jobSpecification(jobSpecification).jobDefinition(jobDefinition).build();


  }

  @WithMockUser(roles = CallDetailConstants.CALL_DETAIL_REPORTS_READ)
  @DisplayName("Test: Retrieve Scheduled Report By Id")
  @Test
  void testDoGetById() throws Exception {
    ApiResponse<HttpResponseGetScheduledJob> apiResponse = ApiResponse.create(reportDTO);
    when(schedulerReportService.processGetByJobId(enterpriseId, jobId)).thenReturn(apiResponse);

    mockMvc.perform(
            MockMvcRequestBuilders.get("/enterprise/{enterpriseId}/scheduler/report/{reportId}",
                enterpriseId, jobId))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload.emailDefinition.enterpriseId", is(enterpriseId)))
        .andExpect(jsonPath("$.payload.emailDefinition.enterpriseName", is(enterpriseName)))
        .andExpect(jsonPath("$.payload.emailDefinition.fromEmail", is(fromEmail)))
        .andExpect(jsonPath("$.payload.emailDefinition.toEmail", is(toEmails)))
        .andExpect(jsonPath("$.errors").isArray()).andExpect(jsonPath("$.errors", hasSize(0)))
        .andDo(print());
  }

  @WithMockUser(roles = {CallDetailConstants.CALL_DETAIL_REPORTS_CREATE, CallDetailConstants.CALL_DETAIL_REPORTS_READ})
  @DisplayName("Test: Create Simple Scheduled Report")
  @Test
  void testCreateSimpleScheduledReport() throws Exception {
    HttpRequestCreateSimpleScheduleJob request = createHttpRequestCreateSimpleScheduleJobRequest();

    ApiResponse<HttpResponseGetScheduledJob> apiResponse = ApiResponse.create(reportDTO);

    when(schedulerReportService.processCreateSimpleScheduleJob(any())).thenReturn(apiResponse);

    mockMvc.perform(post("/enterprise/{enterpriseId}/scheduler/report/simpleschedule",
            enterpriseId).contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload.emailDefinition.enterpriseId", is(enterpriseId)))
        .andExpect(jsonPath("$.payload.emailDefinition.enterpriseName", is(enterpriseName)))
        .andExpect(jsonPath("$.payload.emailDefinition.fromEmail", is(fromEmail)))
        .andExpect(jsonPath("$.payload.emailDefinition.toEmail", is(toEmails)))
        .andExpect(jsonPath("$.errors").isArray())
        .andExpect(jsonPath("$.errors", hasSize(0)))
        .andDo(print());
  }

  @WithMockUser(roles = {CallDetailConstants.CALL_DETAIL_REPORTS_CREATE, CallDetailConstants.CALL_DETAIL_REPORTS_READ})
  @DisplayName("Test: Create Cron Scheduled Report")
  @Test
  void testCreateCronScheduledReport() throws Exception {
    HttpRequestCreateCronScheduleJob request = createHttpRequestCreateCronScheduleJobRequest();

    ApiResponse<HttpResponseGetScheduledJob> apiResponse = ApiResponse.create(reportDTO);

    when(schedulerReportService.processCreateCronScheduleJob(any())).thenReturn(apiResponse);

    mockMvc.perform(post("/enterprise/{enterpriseId}/scheduler/report/cronschedule",
            enterpriseId).contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.successful", is(true)))
        .andExpect(jsonPath("$.payload.emailDefinition.enterpriseId", is(enterpriseId)))
        .andExpect(jsonPath("$.payload.emailDefinition.enterpriseName", is(enterpriseName)))
        .andExpect(jsonPath("$.payload.emailDefinition.fromEmail", is(fromEmail)))
        .andExpect(jsonPath("$.payload.emailDefinition.toEmail", is(toEmails)))
        .andExpect(jsonPath("$.errors").isArray())
        .andExpect(jsonPath("$.errors", hasSize(0)))
        .andDo(print());
  }

  private HttpRequestCreateCronScheduleJob createHttpRequestCreateCronScheduleJobRequest() {
    HttpRequestCreateEmailDefinition emailDefinition = HttpRequestCreateEmailDefinition
        .builder()
        .enterpriseId(enterpriseId)
        .toEmail(toEmails)
        .build();

    HttpRequestCreateJobDefinition jobDefinition = getRequestJobDefinition(getRequestJobData());

    HttpRequestCreateCronScheduleDefinition jobScheduleDefinition = HttpRequestCreateCronScheduleDefinition
        .builder()
        .userTimeZone("America/New_York")
        .cronJobExpression("0 0 12 * * ?")
        .build();

    return HttpRequestCreateCronScheduleJob
        .builder()
        .emailDefinition(emailDefinition)
        .jobScheduleDefinition(jobScheduleDefinition)
        .jobDefinition(jobDefinition)
        .build();
  }

  // creates a JobDataMap for SimpleScheduleDefinition job types:
  private JobDataMap createScheduleDefinitionMap(
      SimpleScheduleDefinitionDTO scheduleDefinitionDTO) {
    JobDataMap jobDataMap = getJobDataMap();
    jobDataMap.put(SCHEDULE_TYPE, scheduleDefinitionDTO.getScheduleType());
    jobDataMap.put(USER_TIME_ZONE, scheduleDefinitionDTO.getUserTimeZone());
    jobDataMap.put(REPORT_RUN_TIME, scheduleDefinitionDTO.getReportRunTime());
    jobDataMap.put(FREQUENCY, scheduleDefinitionDTO.getFrequency());
    jobDataMap.put(FREQUENCY_UNITS, scheduleDefinitionDTO.getFrequencyUnits());
    jobDataMap.put(SELECTED_WEEK_RUN_DAYS, scheduleDefinitionDTO.getSelectedWeekRunDays());
    jobDataMap.put(SELECTED_MONTH_RUN_DAYS, scheduleDefinitionDTO.getSelectedMonthRunDays());
    return jobDataMap;
  }

  private JobDataMap createScheduleDefinitionMap(CronScheduleDefinitionDTO scheduleDefinitionDTO) {
    JobDataMap jobDataMap = getJobDataMap();
    jobDataMap.put(SCHEDULE_TYPE, scheduleDefinitionDTO.getScheduleType());
    jobDataMap.put(CRON_JOB_EXPRESSION, scheduleDefinitionDTO.getCronJobExpression().toString());
    jobDataMap.put(JobScheduleDefinitionConstants.USER_TIME_ZONE,
        scheduleDefinitionDTO.getUserTimeZone().toString());
    return jobDataMap;
  }

  private SimpleScheduleDefinitionDTO getSimpleScheduleDefinitionDTO() {
    return SimpleScheduleDefinitionDTO.builder().userTimeZone(ZoneId.of("America/New_York"))
        .reportRunTime(LocalTime.parse("10:00")).frequency(1).frequencyUnits(ChronoUnit.MINUTES)
        .selectedWeekRunDays(Collections.singleton(WeekDays.MONDAY))
        .selectedMonthRunDays(Collections.singleton(1)).build();
  }

  private TriggerDTO getTriggerDTO() {
    return TriggerDTO.builder().jobGroup(getEnterpriseId()).jobName(generateJobId())
        .startTime("10:00:00").endTime("10:01:00").previousFireTime("09:00:00")
        .nextFireTime("11:00:00").triggerState("enabled").build();
  }

  private String getEnterpriseId() {
    return "0001005437";
  }

  private String getEnterpriseName() {
    return "EvolveIP, LLC";
  }

  private JobSpecificationDTO getJobSpecificationDTO() {
    return JobSpecificationDTO.builder().jobGroup(getEnterpriseId()).jobId(jobId)
        .jobDescription("CallDetail Report Scheduled Job")
        .jobRunnerClass("class net.evolveip.ossmosis.api.scheduler.job.CallDetailReportRunner")
        .persistJobDataAfterExecution(false).isExecuting(false).durable(true)
        .requestsRecovery(false).concurrentExecutionDisallowed(false).build();
  }

  private CallDetailReportDTO getJobData() {
    return CallDetailReportDTO.builder()
        .reportTitle("Every 2 Minute, 2 Days Data  Offset, Test Report On Answered External")
        .answerStatus("answered")
        .externalOnly("external")
        .direction("all")
        .callType("all")
        .userType("voicemail")
        .userNumbers(new ArrayList<>())
        .groupNumbers(new ArrayList<>())
        .dataWindowOffset(2)
        .dataWindowOffsetUnits(ChronoUnit.DAYS)
        .dataWindowEndTime(LocalTime.parse("20:00:00"))
        .dataWindow(7)
        .dataWindowUnits(ChronoUnit.DAYS)
        .build();

  }

  private JobDefinitionDTO getJobDefinition() {
    return JobDefinitionDTO
        .builder()
        .jobType("CallDetailReport")
        .enabled(true)
        .createdBy("<EMAIL>")
        .createdTimestamp("20240826T135500")
        .updatedBy("<EMAIL>")
        .updatedTimestamp("20240826T135500")
        .jobData(getJobData())
        .build();
  }

  private HttpRequestCreateJobDefinition getRequestJobDefinition(HttpRequestCreateCallDetailReport jobData) {
    return HttpRequestCreateJobDefinition
        .builder()
        .jobType("CallDetailReport")
        .enabled("true")
        .createdBy("<EMAIL>")
        .createdTimestamp("20240826T135500")
        .updatedBy("<EMAIL>")
        .updatedTimestamp("20240826T135500")
        .jobData(jobData)
        .build();
  }

  private HttpRequestCreateCallDetailReport getRequestJobData() {
    return HttpRequestCreateCallDetailReport
        .builder()
        .reportTitle("Every 2 Minute, 2 Days Data  Offset, Test Report On Answered External")
        .answerStatus("answered")
        .externalOnly("external")
        .direction("all")
        .callType("all")
        .userType("voicemail")
        .userNumbers(new ArrayList<>())
        .groupNumbers(new ArrayList<>())
        .groupBy("group")
        .dataWindowOffset("2")
        .dataWindowOffsetUnits(ChronoUnit.DAYS.toString())
        .dataWindowEndTime("20:00:00")
        .dataWindow("7")
        .dataWindowUnits(ChronoUnit.DAYS.toString())
        .build();
  }


  private HttpRequestCreateSimpleScheduleJob createHttpRequestCreateSimpleScheduleJobRequest() {
    HttpRequestCreateEmailDefinition emailDefinition = HttpRequestCreateEmailDefinition
        .builder()
        .enterpriseId(enterpriseId)
        .toEmail(toEmails)
        .build();

    HttpRequestCreateSimpleScheduleDefinition jobScheduleDefinition =
        HttpRequestCreateSimpleScheduleDefinition
            .builder()
            .userTimeZone("America/New_York")
            .reportRunTime("13:43:00")
            .frequency("2")
            .frequencyUnits(ChronoUnit.MINUTES.toString())
            .selectedWeekRunDays(new HashSet<>())
            .selectedMonthRunDays(new HashSet<>())
            .build();

    HttpRequestCreateCallDetailReport jobData = HttpRequestCreateCallDetailReport
        .builder()
        .reportTitle("Every 2 Minute, 2 Days Data  Offset, Test Report On Answered External")
        .answerStatus("answered")
        .externalOnly("external")
        .direction("all")
        .callType("all")
        .userType("voicemail")
        .userNumbers(new ArrayList<>())
        .groupNumbers(new ArrayList<>())
        .groupBy("group")
        .dataWindowOffset("2")
        .dataWindowOffsetUnits(ChronoUnit.DAYS.toString())
        .dataWindowEndTime("20:00:00")
        .dataWindow("7")
        .dataWindowUnits(ChronoUnit.DAYS.toString())
        .build();

    HttpRequestCreateJobDefinition jobDefinition = HttpRequestCreateJobDefinition
        .builder()
        .jobType("CallDetailReport")
        .enabled("true")
        .createdBy("<EMAIL>")
        .createdTimestamp("20240826T135500")
        .updatedBy("<EMAIL>")
        .updatedTimestamp("20240826T135500")
        .jobData(jobData)
        .build();

    return HttpRequestCreateSimpleScheduleJob
        .builder()
        .emailDefinition(emailDefinition)
        .jobScheduleDefinition(jobScheduleDefinition)
        .jobDefinition(jobDefinition)
        .build();
  }
}