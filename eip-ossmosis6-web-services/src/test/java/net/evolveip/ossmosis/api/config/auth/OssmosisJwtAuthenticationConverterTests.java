package net.evolveip.ossmosis.api.config.auth;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;

import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import net.evolveip.ossmosis.api.config.SecurityConfig;
import net.evolveip.ossmosis.api.features.enterprise.service.EnterpriseValidationService;
import net.evolveip.ossmosis.api.features.login.constant.LoginConstants;
import net.evolveip.ossmosis.api.features.login.dto.LoginInfoResponseDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginRequestDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginResponseDTO;
import net.evolveip.ossmosis.api.features.login.entity.Login;
import net.evolveip.ossmosis.api.features.login.entity.view.LoginView;
import net.evolveip.ossmosis.api.features.login.mapper.LoginMapper;
import net.evolveip.ossmosis.api.features.login.mapper.LoginMapperImpl;
import net.evolveip.ossmosis.api.features.login.mapper.LoginResourcePermissionMapperImpl;
import net.evolveip.ossmosis.api.features.login.mapper.LoginRoleMapperImpl;
import net.evolveip.ossmosis.api.features.login.mapper.LoginRoleViewMapperImpl;
import net.evolveip.ossmosis.api.features.login.mapper.LoginViewMapperImpl;
import net.evolveip.ossmosis.api.features.login.service.LoginService;
import net.evolveip.ossmosis.api.features.role.constant.RoleConstants;
import net.evolveip.ossmosis.api.features.role.entity.Role;
import net.evolveip.ossmosis.api.features.role.service.RoleService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.service.AuthorizationService;
import net.evolveip.ossmosis.api.utils.service.JwtService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.test.context.ContextConfiguration;

@WebMvcTest()
@ContextConfiguration(classes = { SecurityConfig.class, JwtService.class,
    OssmosisJwtAuthenticationConverter.class, JwtDecoder.class, LoginService.class,
    AuthorizationService.class, LoginMapperImpl.class, LoginResourcePermissionMapperImpl.class,
    LoginRoleMapperImpl.class, LoginRoleViewMapperImpl.class, LoginViewMapperImpl.class })
public class OssmosisJwtAuthenticationConverterTests {

  @MockBean
  private JwtService jwtService;

  @MockBean
  private EnterpriseValidationService validationService;

  @MockBean
  private LoginService loginService;

  @MockBean
  private AuthorizationService authorizationService;

  @MockBean
  private RoleService roleService;

  @Autowired
  private LoginMapper loginMapper;

  @Autowired
  private OssmosisJwtAuthenticationConverter ossmosisJwtAuthenticationConverter;

  private String testEmail,
      testEnterpriseId,
      testFirstname,
      testLastName;
  private Jwt jwtSource;

  private Role role;
  private Login testLogin;

  private Collection<SimpleGrantedAuthority> authorities;

  @BeforeEach
  public void setup() {
    testEmail = "<EMAIL>";
    testEnterpriseId = "0000000001";
    testFirstname = "firstName";
    testLastName = "lastName";
    Map<String, Object> jwtHeaders = new HashMap<>();
    jwtHeaders.put("header", "useless");

    Map<String, Object> claims = new HashMap<>();
    claims.put("scope", "");
    claims.put("enterprise_id", testEnterpriseId);
    claims.put("email", testEmail);
    claims.put("sub", "test");
    claims.put("exp", Instant.now().plusSeconds(60000));

    authorities = new ArrayList<>();
    authorities.add(new SimpleGrantedAuthority(LoginConstants.LOGINS_READ));

    jwtSource = new Jwt("test-token-val", Instant.now(), Instant.now().plusSeconds(60000L),
        jwtHeaders, claims);

    role = Role.builder().roleId(1).roleName(RoleConstants.ROLE_STANDARD_USER).build();

    testLogin = Login.builder().loginId(1L).active(true).locked(false).loginGroup("test")
        .loginNameFirst(testFirstname).loginNameLast(testFirstname).loginEmail(testEmail)
        .dateCreated(ZonedDateTime.now()).dateUpdated(ZonedDateTime.now())
        .userPermissionList(new ArrayList<>()).build();

  }

  @Test
  @DisplayName("Test: jwt authentication should pass when a user is found in the DB")
  public void testJwtAuthenticationConverterSuccess() {
    LoginView loginView = Mockito.mock(LoginView.class);
    LoginInfoResponseDTO loginInfoResponseDTO = new LoginInfoResponseDTO();
    loginInfoResponseDTO.setLoginResponseDTO(
        LoginResponseDTO.builder().loginEmail(testEmail).loginId(0L).active(false)
            .locked(false)
            .userPermissionList(new ArrayList<>()).roles(new ArrayList<>()).build());
    loginInfoResponseDTO.setAuthorities(
        authorities.stream().map(SimpleGrantedAuthority::getAuthority).collect(
            Collectors.toList()));
    loginInfoResponseDTO.setEnterprises(new LinkedList<>());

    Mockito.when(loginView.getLoginEmail()).thenReturn(testEmail);
    Mockito.when(jwtService.extractUserEmail(jwtSource.getTokenValue())).thenReturn(testEmail);
    Optional<LoginView> login = Optional.of(loginView);

    Mockito.when(authorizationService.loadUserByUserEmail(testEmail)).thenReturn(login);

    Mockito.when(jwtService.isTokenValid(anyString(), any(UserDetails.class))).thenReturn(true);
    Mockito.when(authorizationService.getAuthorities(any(LoginView.class))).thenReturn(authorities);

    AbstractAuthenticationToken jwtAuthenticationToken = this.ossmosisJwtAuthenticationConverter.convert(
        jwtSource);

    assertNotNull(SecurityContextHolder.getContext().getAuthentication());
    assertTrue(SecurityContextHolder.getContext().getAuthentication().isAuthenticated());
    assertTrue(SecurityContextHolder.getContext().getAuthentication().getAuthorities()
        .containsAll(authorities));
    assertInstanceOf(JwtAuthenticationToken.class, jwtAuthenticationToken);
    assertNotNull(jwtAuthenticationToken);
    assertThat(jwtAuthenticationToken.getDetails()).usingRecursiveComparison()
        .isEqualTo(loginInfoResponseDTO);

  }

  @Test
  @DisplayName("Test: jwt authentication should create a new login when a user is not found in the DB")
  public void testJwtAuthenticationConverterFail() {

    Optional<LoginView> login = Optional.empty();

    LoginView loginView = Mockito.mock(LoginView.class);
    Mockito.when(loginView.getLoginEmail()).thenReturn(testEmail);

    doReturn(testEnterpriseId).when(jwtService).extractEnterpriseId(any(String.class));
    doReturn(testFirstname).when(jwtService).extractFirstName(any(String.class));
    doReturn(testLastName).when(jwtService).extractLastName(any(String.class));
    doReturn(true).when(validationService).isEnterpriseIdValid(testEnterpriseId);

    Mockito.when(authorizationService.loadUserByUserEmail(testEmail)).thenReturn(login)
        .thenReturn(Optional.of(loginView));
    Mockito.when(loginService.createLoginForFirstTimeOnApi(any(LoginRequestDTO.class)))
        .thenReturn(ApiResponse.create(loginMapper.toLoginResponseDTO(testLogin)));

    Mockito.when(jwtService.extractUserEmail(jwtSource.getTokenValue())).thenReturn(testEmail);
    Mockito.when(jwtService.isTokenValid(anyString(), any(UserDetails.class))).thenReturn(true);

    Mockito.when(authorizationService.getAuthorities(loginView)).thenReturn(authorities);

    AbstractAuthenticationToken jwtAuthenticationToken = this.ossmosisJwtAuthenticationConverter.convert(
        jwtSource);

    assertNotNull(SecurityContextHolder.getContext().getAuthentication());
    assertTrue(SecurityContextHolder.getContext().getAuthentication().isAuthenticated());
    assertTrue(SecurityContextHolder.getContext().getAuthentication().getAuthorities()
        .containsAll(authorities));
    assertInstanceOf(JwtAuthenticationToken.class, jwtAuthenticationToken);
    assertNotNull(jwtAuthenticationToken);
    assertInstanceOf(LoginInfoResponseDTO.class, jwtAuthenticationToken.getDetails());
  }

  @Test
  @DisplayName("Test: jwt authentication should fail when a user is not found in the DB and create failed due to exception")
  public void testJwtAuthenticationConverterFailOnExceptionInCreate() {

    Mockito.when(authorizationService.loadUserByUserEmail(testEmail)).thenReturn(Optional.empty());
    Mockito.when(loginService.processCreateLogin(any(LoginRequestDTO.class), anyString()))
        .thenReturn(ApiResponse.create(new Exception()));

    Mockito.when(jwtService.extractUserEmail(jwtSource.getTokenValue())).thenReturn(testEmail);
    Mockito.when(jwtService.isTokenValid(anyString(), any(UserDetails.class))).thenReturn(false);

    Mockito.when(authorizationService.getAuthorities(any(LoginView.class))).thenReturn(authorities);

    AbstractAuthenticationToken jwtAuthenticationToken = this.ossmosisJwtAuthenticationConverter.convert(
        jwtSource);

    assertNull(SecurityContextHolder.getContext().getAuthentication());
    assertInstanceOf(JwtAuthenticationToken.class, jwtAuthenticationToken);
    assertNotNull(jwtAuthenticationToken);
    assertNull(jwtAuthenticationToken.getDetails());

  }

  @Test
  @DisplayName("Test: jwt authentication should fail when a user is not found in the DB due to an exception")
  public void testJwtAuthenticationConverterFailOnException() {

    Mockito.when(authorizationService.loadUserByUserEmail(testEmail)).thenReturn(null);
    Mockito.when(loginService.processCreateLogin(any(LoginRequestDTO.class), anyString()))
        .thenReturn(ApiResponse.create(new Exception()));

    Mockito.when(jwtService.extractUserEmail(jwtSource.getTokenValue())).thenReturn(testEmail);
    Mockito.when(jwtService.isTokenValid(anyString(), any(UserDetails.class))).thenReturn(false);

    Mockito.when(authorizationService.getAuthorities(any(LoginView.class))).thenReturn(authorities);

    AbstractAuthenticationToken jwtAuthenticationToken = this.ossmosisJwtAuthenticationConverter.convert(
        jwtSource);

    assertNull(SecurityContextHolder.getContext().getAuthentication());
    assertInstanceOf(JwtAuthenticationToken.class, jwtAuthenticationToken);
    assertNotNull(jwtAuthenticationToken);
    assertNull(jwtAuthenticationToken.getDetails());

  }
}
