package net.evolveip.ossmosis.api.config.auth;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;

@WebMvcTest
@ContextConfiguration(classes = DynamicJwtDecoderResolver.class)
@TestPropertySource("classpath:application-test.properties")
public class OssmosisJwtDecoderConfigTests {

  @Autowired
  private JwtDecoder jwtDecoder;

  @Test
  @DisplayName("Test: confirm jwtDecoder is configured.")
  public void testOssmosisJwtDecoderConfig() {
    assertNotNull(jwtDecoder);
  }

}
