package net.evolveip.ossmosis.api.config.logging;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.nio.charset.StandardCharsets;
import net.evolveip.ossmosis.api.config.audit.AuditFilterConfig;
import net.evolveip.ossmosis.api.config.audit.AuditRequestFilter;
import net.evolveip.ossmosis.api.features.audit.entity.RequestAuditLog;
import net.evolveip.ossmosis.api.features.audit.service.RecordAuditLogService;
import net.evolveip.ossmosis.api.features.audit.service.RequestAuditLogService;
import net.evolveip.ossmosis.api.features.login.constant.LoginConstants;
import net.evolveip.ossmosis.api.features.login.controller.LoginController;
import net.evolveip.ossmosis.api.features.login.dto.LoginRequestDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginResponseDTO;
import net.evolveip.ossmosis.api.features.login.mapper.LoginMapper;
import net.evolveip.ossmosis.api.features.login.mapper.LoginMapperImpl;
import net.evolveip.ossmosis.api.features.login.mapper.LoginResourcePermissionMapperImpl;
import net.evolveip.ossmosis.api.features.login.mapper.LoginRoleMapperImpl;
import net.evolveip.ossmosis.api.features.login.service.LoginService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.support.AnnotationConfigContextLoader;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

@WebMvcTest()
@ContextConfiguration(classes = {
    LoginController.class, AuditFilterConfig.class,
    AuditRequestFilter.class,
    AnnotationConfigContextLoader.class,
    OssmosisRequestLoggingFilter.class, LoginMapperImpl.class,
    LoginRoleMapperImpl.class,
    LoginResourcePermissionMapperImpl.class})
@TestPropertySource(locations = "classpath:application.properties")
public class OssmosisRequestLoggingFilterTests {

  private MockMvc mockMvc;


  @MockBean
  private RequestAuditLogService requestAuditLogService;

  @MockBean
  private RecordAuditLogService recordAuditLogService;

  @MockBean
  private LoginService loginService;

  @InjectMocks
  private LoginController loginController;

  @Autowired
  private OssmosisRequestLoggingFilter ossmosisRequestLoggingFilter;

  @Autowired
  private WebApplicationContext context;

  @Autowired
  private LoginMapper loginMapper;

  @BeforeEach
  public void setup() {
    this.mockMvc = MockMvcBuilders.webAppContextSetup(context)
        .addFilter(ossmosisRequestLoggingFilter)
        .build();
  }

  @Test
  @WithMockUser(authorities = LoginConstants.LOGINS_READ)
  @DisplayName("Test: confirm request logging filter does not save the get request.")
  public void testOssmosisRequestLoggingFilterGetResponse() throws Exception {

    LoginResponseDTO loginRequestDTO = LoginResponseDTO.builder()
        .loginId(1L).active(true)
        .loginGroup("test").loginEmail("<EMAIL>")
        .loginNameFirst("first").loginNameLast("last")
        .loginPhoneNumber("this value will cause the exception")
        .loginPrimaryEnterpriseId("000100101")
        .build();

    Mockito.when(loginService.processGetByEmail(anyString(), anyString()))
        .thenReturn(ApiResponse.create(loginRequestDTO));
    mockMvc.perform(
            MockMvcRequestBuilders.get("/enterprise/1/logins/email/{loginEmail}", "<EMAIL>"))
        .andExpect(status().isOk());

    Mockito.verify(requestAuditLogService, Mockito.never())
        .addUpdateRequestToLog(any(RequestAuditLog.class));
  }

  @Test
  @WithMockUser(authorities = LoginConstants.LOGINS_CREATE)
  @DisplayName("Test: confirm request logging filter saves the request for put and post.")
  public void testOssmosisRequestLoggingFilterPostOrPutResponse() throws Exception {
    LoginRequestDTO loginRequestDTO = LoginRequestDTO.builder()
        .loginId(1L).active(true)
        .loginGroup("test").loginEmail("<EMAIL>")
        .loginNameFirst("first").loginNameLast("last")
        .loginPhoneNumber("6102020023")
        .locked(false)
        .loginPrimaryEnterpriseId("0001001010")
        .build();

    Mockito.when(loginService.processCreateLogin(any(LoginRequestDTO.class), anyString()))
        .thenReturn(ApiResponse.create(
            loginMapper.toLoginResponseDTO(loginMapper.toLogin(loginRequestDTO))));

    Mockito.when(requestAuditLogService.addUpdateRequestToLog(any(RequestAuditLog.class)))
        .thenReturn(ApiResponse.create(RequestAuditLog.builder().build()));

    mockMvc.perform(MockMvcRequestBuilders.post("/enterprise/1/logins")
            .content((new ObjectMapper()).writeValueAsString(loginRequestDTO))
            .contentType(MediaType.APPLICATION_JSON)
            .characterEncoding(StandardCharsets.UTF_8))
        .andExpect(status().isOk());

    Mockito.verify(requestAuditLogService, Mockito.atLeast(2))
        .addUpdateRequestToLog(any(RequestAuditLog.class));
  }

  @Test
  @DisplayName("Test: confirm request logging filter does not save the get fail request.")
  public void testOssmosisRequestLoggingFilterGetFailResponse() throws Exception {

    LoginResponseDTO loginRequestDTO = LoginResponseDTO.builder()
        .loginId(1L).active(true)
        .loginGroup("test").loginEmail("<EMAIL>")
        .loginNameFirst("first").loginNameLast("last")
        .loginPhoneNumber("this value will cause the exception")
        .loginPrimaryEnterpriseId("000100101")
        .build();

    Mockito.when(loginService.processGetByEmail(anyString(), anyString()))
        .thenReturn(ApiResponse.create(loginRequestDTO));
    mockMvc.perform(MockMvcRequestBuilders.get("/logins/emailaa/{loginEmail}", "<EMAIL>"))
        .andExpect(status().isNotFound());

    Mockito.verify(requestAuditLogService, Mockito.never())
        .addUpdateRequestToLog(any(RequestAuditLog.class));
  }

  @Test
  @WithMockUser(authorities = LoginConstants.LOGINS_CREATE)
  @DisplayName("Test: confirm request logging filter saves the request for put and post not found response.")
  public void testOssmosisRequestLoggingFilterPostOrPutNotFoundResponse() throws Exception {
    LoginRequestDTO loginRequestDTO = LoginRequestDTO.builder()
        .loginId(1L).active(true)
        .loginGroup("test").loginEmail("<EMAIL>")
        .loginNameFirst("first").loginNameLast("last")
        .loginPhoneNumber("6102020023")
        .locked(false)
        .loginPrimaryEnterpriseId("0001001010")
        .build();

    Mockito.when(loginService.processCreateLogin(any(LoginRequestDTO.class), anyString()))
        .thenReturn(ApiResponse.create(
            loginMapper.toLoginResponseDTO(loginMapper.toLogin(loginRequestDTO))));

    Mockito.when(requestAuditLogService.addUpdateRequestToLog(any(RequestAuditLog.class)))
        .thenReturn(ApiResponse.create(RequestAuditLog.builder().build()));

    mockMvc.perform(MockMvcRequestBuilders.post("/logins-fail")
            .content((new ObjectMapper()).writeValueAsString(loginRequestDTO))
            .contentType(MediaType.APPLICATION_JSON)
            .characterEncoding(StandardCharsets.UTF_8))
        .andExpect(status().isNotFound());

    Mockito.verify(requestAuditLogService, Mockito.atLeast(2))
        .addUpdateRequestToLog(any(RequestAuditLog.class));
  }
}
