package net.evolveip.ossmosis.api.config.swagger;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import io.swagger.v3.oas.models.OpenAPI;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.ContextConfiguration;

@WebMvcTest
@ContextConfiguration(classes = {OssmosisSwaggerConfig.class})
public class OssmosisSwaggerConfigTests {

  @Autowired
  private OpenAPI openAPI;

  @Test
  @DisplayName("Test: confirm swagger docs config.")
  public void testAuditFilterConfig() {
    assertNotNull(openAPI);
    assertTrue(openAPI.getInfo().getTitle().contains("OSSmosis 6"));
    assertTrue(openAPI.getInfo().getDescription().contains("OSSmosis 6"));
    assertTrue(openAPI.getSecurity().get(0).containsKey("Bearer Authentication"));
    assertTrue(openAPI.getComponents().getSecuritySchemes().containsKey("Bearer Authentication"));
  }
}