package net.evolveip.ossmosis.api.features.login.dto;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.test.context.ActiveProfiles;


// The name of the test class does not follow the naming standard because somehow maven picks up
// this class as an instance for one of the prior tests.
@ActiveProfiles({"test", "docker"})
@DataJpaTest
public class TestLoginRequestDTO {

  private LoginRequestDTO loginRequestDTO;
  private Long loginId;
  private String loginEmail;
  private String loginNameFirst;
  private String loginNameLast;
  private Boolean active;
  private Boolean locked;
  private String loginGroup;
  private String loginPhoneNumber;
  private String loginPrimaryEnterpriseId;

  @BeforeEach
  public void setup() {
    loginId = 1L;
    loginEmail = "<EMAIL>";
    loginNameFirst = "NameFirst";
    loginNameLast = "NameLast";
    active = true;
    locked = false;
    loginGroup = "admin";
    loginPhoneNumber = "(*************";
    loginPrimaryEnterpriseId = "1000000009";
  }

  @DisplayName("Test: instantiate a loginRequestDTO")
  @Test
  public void testStateIsCorrect() {
    loginRequestDTO = LoginRequestDTO
        .builder()
        .loginId(loginId)
        .loginEmail(loginEmail)
        .loginNameFirst(loginNameFirst)
        .loginNameLast(loginNameLast)
        .active(active)
        .locked(locked)
        .loginGroup(loginGroup)
        .loginPhoneNumber(loginPhoneNumber)
        .loginPrimaryEnterpriseId(loginPrimaryEnterpriseId)
        .build();

    assertThat(loginRequestDTO.getLoginId()).isEqualTo(loginId);
    assertThat(loginRequestDTO.getLoginEmail()).isEqualTo(loginEmail);
    assertThat(loginRequestDTO.getLoginNameFirst()).isEqualTo(loginNameFirst);
    assertThat(loginRequestDTO.getLoginNameLast()).isEqualTo(loginNameLast);
    assertThat(loginRequestDTO.getActive()).isEqualTo(active);
    assertThat(loginRequestDTO.getLocked()).isEqualTo(locked);
    assertThat(loginRequestDTO.getLoginGroup()).isEqualTo(loginGroup);
    assertThat(loginRequestDTO.getLoginPhoneNumber()).isEqualTo(loginPhoneNumber);
    assertThat(loginRequestDTO.getLoginPrimaryEnterpriseId()).isEqualTo(loginPrimaryEnterpriseId);
  }

  @DisplayName("Test: equality of two loginRequestDTO objects instantiated with the same values")
  @Test
  void testEqualsTwoObjectsWithSameValues() {
    LoginRequestDTO loginRequestDTO1 = LoginRequestDTO
        .builder()
        .loginId(loginId)
        .loginEmail(loginEmail)
        .loginNameFirst(loginNameFirst)
        .loginNameLast(loginNameLast)
        .active(active)
        .locked(locked)
        .loginGroup(loginGroup)
        .loginPhoneNumber(loginPhoneNumber)
        .loginPrimaryEnterpriseId(loginPrimaryEnterpriseId)
        .build();

    LoginRequestDTO loginRequestDTO2 = LoginRequestDTO
        .builder()
        .loginId(loginId)
        .loginEmail(loginEmail)
        .loginNameFirst(loginNameFirst)
        .loginNameLast(loginNameLast)
        .active(active)
        .locked(locked)
        .loginGroup(loginGroup)
        .loginPhoneNumber(loginPhoneNumber)
        .loginPrimaryEnterpriseId(loginPrimaryEnterpriseId)
        .build();

    assertThat(loginRequestDTO1).isEqualTo(loginRequestDTO2);
  }

  @DisplayName("Test: inequality of two loginRequestDTO objects with two different ids")
  @Test
  public void testEqualsTwoObjectsWithDifferentId() {
    LoginRequestDTO loginRequestDTO1 = LoginRequestDTO
        .builder()
        .loginId(loginId)
        .loginEmail(loginEmail)
        .loginNameFirst(loginNameFirst)
        .loginNameLast(loginNameLast)
        .active(active)
        .locked(locked)
        .loginGroup(loginGroup)
        .loginPhoneNumber(loginPhoneNumber)
        .loginPrimaryEnterpriseId(loginPrimaryEnterpriseId)
        .build();

    LoginRequestDTO loginRequestDTO2 = LoginRequestDTO
        .builder()
        .loginId(2L)
        .loginEmail(loginEmail)
        .loginNameFirst(loginNameFirst)
        .loginNameLast(loginNameLast)
        .active(active)
        .locked(locked)
        .loginGroup(loginGroup)
        .loginPhoneNumber(loginPhoneNumber)
        .loginPrimaryEnterpriseId(loginPrimaryEnterpriseId)
        .build();

    assertThat(loginRequestDTO1).isNotEqualTo(loginRequestDTO2);
  }

  @DisplayName("Test: inequality of two loginRequestDTO objects with two different loginEmail")
  @Test
  void testEqualsTwoObjectsWithDifferentName() {
    LoginRequestDTO loginRequestDTO1 = LoginRequestDTO
        .builder()
        .loginId(loginId)
        .loginEmail("<EMAIL>")
        .loginNameFirst(loginNameFirst)
        .loginNameLast(loginNameLast)
        .active(active)
        .locked(locked)
        .loginGroup(loginGroup)
        .loginPhoneNumber(loginPhoneNumber)
        .loginPrimaryEnterpriseId(loginPrimaryEnterpriseId)
        .build();

    LoginRequestDTO loginRequestDTO2 = LoginRequestDTO
        .builder()
        .loginId(loginId)
        .loginEmail("<EMAIL>")
        .loginNameFirst(loginNameFirst)
        .loginNameLast(loginNameLast)
        .active(active)
        .locked(locked)
        .loginGroup(loginGroup)
        .loginPhoneNumber(loginPhoneNumber)
        .loginPrimaryEnterpriseId(loginPrimaryEnterpriseId)
        .build();

    assertThat(loginRequestDTO1).isNotEqualTo(loginRequestDTO2);
  }
}