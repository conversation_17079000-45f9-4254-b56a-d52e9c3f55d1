package net.evolveip.ossmosis.api.features.enterprise.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowOptions;
import io.temporal.common.SearchAttributes;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import net.evolveip.ossmosis.api.features.common.ValidationContext;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseRequestDTO;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseResponseDTO;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.enterprise.mapper.EnterpriseMapper;
import net.evolveip.ossmosis.api.features.enterprise.repository.EnterpriseRepository;
import net.evolveip.ossmosis.api.features.enterprise.util.EnterpriseSearchAttributeUtil;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformRequestDTO;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformResponseDTO;
import net.evolveip.ossmosis.api.features.platform.entity.Platform;
import net.evolveip.ossmosis.api.features.platform.service.PlatformService;
import net.evolveip.ossmosis.api.utils.exceptions.NotFoundException;
import net.evolveip.ossmosis.api.utils.service.AuthorizationService;
import net.evolveip.ossmosis.provisioning.features.enterprise.workflows.EnterpriseCreateWorkflow;
import net.evolveip.ossmosis.provisioning.features.enterprise.workflows.EnterpriseDeleteWorkflow;
import net.evolveip.ossmosis.provisioning.features.enterprise.workflows.EnterpriseModifyWorkflow;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.AccessDeniedException;

@ExtendWith(MockitoExtension.class)
public class EnterpriseServiceUnitTest {

  // Constants
  private static final String ENTERPRISE_ID = "ENT001";
  private static final String PARENT_ENTERPRISE_ID = "PARENT001";
  private static final String OLD_PARENT_ENTERPRISE_ID = "OLDPARENT001";
  private static final String BILLING_ID = "BILLING001";
  private static final String PARENT_BILLING_ID = "BILLING002";
  private static final String OLD_PARENT_BILLING_ID = "BILLING003";
  private static final String ENTERPRISE_NAME = "Test Enterprise";
  private static final String PARENT_ENTERPRISE_NAME = "Parent Enterprise";
  private static final String OLD_PARENT_ENTERPRISE_NAME = "Old Parent Enterprise";
  private static final String ACCOUNT_COUNTRY = "US";
  private static final String PLATFORM_NAME = "Test Platform";
  private static final String FILTER_VALUE = "test";
  private static final String DELETE_ERROR_MESSAGE = "Cannot delete";
  private static final int PLATFORM_ID = 1;
  private static final int PAGE_SIZE = 10;
  private static final int PAGE_NUMBER = 0;
  private static final int SECOND_PAGE = 1;
  private static final int LARGE_PAGE_SIZE = 50;

  @Mock
  private EnterpriseRepository enterpriseRepository;

  @Mock
  private EnterpriseMapper mapper;

  @Mock
  private PlatformService platformService;

  @Mock
  private AuthorizationService authorizationService;

  @Mock
  private EnterpriseValidationService validationService;

  @Mock
  private WorkflowClient workflowClient;

  @Mock
  private EnterpriseSearchAttributeUtil enterpriseSearchAttributeUtil;

  @InjectMocks
  private EnterpriseService enterpriseService;

  private String enterpriseId;
  private String parentEnterpriseId;
  private List<String> enterpriseIds;
  private Enterprise enterprise;
  private EnterpriseResponseDTO enterpriseResponseDTO;
  private EnterpriseRequestDTO enterpriseRequestDTO;
  private Platform platform;
  private PlatformRequestDTO platformRequestDTO;
  private PlatformResponseDTO platformResponseDTO;
  private Pageable pageable;

  @BeforeEach
  public void setUp() {
    // Set up test data
    enterpriseId = ENTERPRISE_ID;
    parentEnterpriseId = PARENT_ENTERPRISE_ID;
    enterpriseIds = List.of(enterpriseId);
    pageable = PageRequest.of(0, 10);

    // Create platform data
    platform = createPlatform();
    platformRequestDTO = createPlatformRequestDTO();
    platformResponseDTO = createPlatformResponseDTO();

    // Create enterprise data
    enterprise = createEnterprise();
    enterpriseResponseDTO = createEnterpriseResponseDTO();
    enterpriseRequestDTO = createEnterpriseRequestDTO();

    // Remove unnecessary stubbing from here
  }

  @Test
  public void testProcessGet_Success() {
    // Arrange
    String filterValue = FILTER_VALUE;
    long totalElements = 1;
    List<Enterprise> enterprises = Collections.singletonList(enterprise);
    Page<Enterprise> enterprisePage = new PageImpl<>(enterprises, pageable, totalElements);

    when(authorizationService.getEnterpriseAccessStringListForCurrentUser()).thenReturn(
        enterpriseIds);
    when(
        enterpriseRepository.findFilteredCountAllEnterprisesForUser(any(), anyString())).thenReturn(
        totalElements);
    when(enterpriseRepository.findAllByFilteredPage(any(), anyString(), any())).thenReturn(
        enterprisePage);
    when(mapper.toDTO(any(Enterprise.class))).thenReturn(enterpriseResponseDTO);

    // Act
    Page<EnterpriseResponseDTO> result = enterpriseService.processGet(filterValue, pageable);

    // Assert
    assertNotNull(result);
    assertEquals(1, result.getTotalElements());
    assertEquals(enterpriseResponseDTO, result.getContent().get(0));

    verify(enterpriseRepository).findFilteredCountAllEnterprisesForUser(enterpriseIds,
        filterValue.toLowerCase());
    verify(enterpriseRepository).findAllByFilteredPage(enterpriseIds, filterValue.toLowerCase(),
        pageable);
  }

  @Test
  public void testProcessGet_WithSecondPage_Success() {
    // Arrange
    String filterValue = FILTER_VALUE;
    long totalElements = PAGE_SIZE + 1; // More elements than fit on first page
    List<Enterprise> enterprises = Collections.singletonList(enterprise);
    Pageable secondPageable = PageRequest.of(SECOND_PAGE, PAGE_SIZE);
    Page<Enterprise> enterprisePage = new PageImpl<>(enterprises, secondPageable, totalElements);

    when(authorizationService.getEnterpriseAccessStringListForCurrentUser()).thenReturn(
        enterpriseIds);
    when(
        enterpriseRepository.findFilteredCountAllEnterprisesForUser(any(), anyString())).thenReturn(
        totalElements);
    when(enterpriseRepository.findAllByFilteredPage(any(), anyString(), any())).thenReturn(
        enterprisePage);
    when(mapper.toDTO(any(Enterprise.class))).thenReturn(enterpriseResponseDTO);

    // Act
    Page<EnterpriseResponseDTO> result = enterpriseService.processGet(filterValue, secondPageable);

    // Assert
    assertNotNull(result);
    assertEquals(totalElements, result.getTotalElements());
    assertEquals(enterpriseResponseDTO, result.getContent().get(0));
    assertEquals(SECOND_PAGE, result.getNumber());

    verify(enterpriseRepository).findFilteredCountAllEnterprisesForUser(enterpriseIds,
        filterValue.toLowerCase());
    verify(enterpriseRepository).findAllByFilteredPage(enterpriseIds, filterValue.toLowerCase(),
        secondPageable);
  }

  @Test
  public void testProcessGet_WithLargePageSize_Success() {
    // Arrange
    String filterValue = FILTER_VALUE;
    long totalElements = 5;

    // Create a list of 5 enterprises to match the totalElements count
    List<Enterprise> enterprises = new ArrayList<>();
    for (int i = 0; i < totalElements; i++) {
      Enterprise testEnterprise = Enterprise.builder()
          .enterpriseId(ENTERPRISE_ID + i)
          .billingId(BILLING_ID + i)
          .enterpriseName(ENTERPRISE_NAME + i)
          .accountCountry(ACCOUNT_COUNTRY)
          .platform(platform)
          .parentEnterprise(createParentEnterprise())
          .build();
      enterprises.add(testEnterprise);
    }

    Pageable largePageable = PageRequest.of(PAGE_NUMBER, LARGE_PAGE_SIZE);
    Page<Enterprise> enterprisePage = new PageImpl<>(enterprises, largePageable, totalElements);

    when(authorizationService.getEnterpriseAccessStringListForCurrentUser()).thenReturn(
        enterpriseIds);
    when(
        enterpriseRepository.findFilteredCountAllEnterprisesForUser(any(), anyString())).thenReturn(
        totalElements);
    when(enterpriseRepository.findAllByFilteredPage(any(), anyString(), any())).thenReturn(
        enterprisePage);
    when(mapper.toDTO(any(Enterprise.class))).thenReturn(enterpriseResponseDTO);

    // Act
    Page<EnterpriseResponseDTO> result = enterpriseService.processGet(filterValue, largePageable);

    // Assert
    assertNotNull(result);
    assertEquals(totalElements, result.getTotalElements());
    assertEquals(LARGE_PAGE_SIZE, result.getSize());
    assertEquals(enterprises.size(), result.getNumberOfElements());

    verify(enterpriseRepository).findFilteredCountAllEnterprisesForUser(enterpriseIds,
        filterValue.toLowerCase());
    verify(enterpriseRepository).findAllByFilteredPage(enterpriseIds, filterValue.toLowerCase(),
        largePageable);
  }

  @Test
  public void testProcessGet_WithSorting_Success() {
    // Arrange
    String filterValue = FILTER_VALUE;
    long totalElements = 1;
    List<Enterprise> enterprises = Collections.singletonList(enterprise);
    Pageable sortedPageable = PageRequest.of(PAGE_NUMBER, PAGE_SIZE,
        org.springframework.data.domain.Sort.by("enterpriseName").ascending());
    Page<Enterprise> enterprisePage = new PageImpl<>(enterprises, sortedPageable, totalElements);

    when(authorizationService.getEnterpriseAccessStringListForCurrentUser()).thenReturn(
        enterpriseIds);
    when(
        enterpriseRepository.findFilteredCountAllEnterprisesForUser(any(), anyString())).thenReturn(
        totalElements);
    when(enterpriseRepository.findAllByFilteredPage(any(), anyString(), any())).thenReturn(
        enterprisePage);
    when(mapper.toDTO(any(Enterprise.class))).thenReturn(enterpriseResponseDTO);

    // Act
    Page<EnterpriseResponseDTO> result = enterpriseService.processGet(filterValue, sortedPageable);

    // Assert
    assertNotNull(result);
    assertEquals(totalElements, result.getTotalElements());
    assertTrue(result.getSort().isSorted());

    verify(enterpriseRepository).findFilteredCountAllEnterprisesForUser(enterpriseIds,
        filterValue.toLowerCase());
    verify(enterpriseRepository).findAllByFilteredPage(enterpriseIds, filterValue.toLowerCase(),
        sortedPageable);
  }

  @Test
  public void testProcessGet_WithEmptyResult_Success() {
    // Arrange
    String filterValue = FILTER_VALUE;
    long totalElements = 0;
    List<Enterprise> enterprises = Collections.emptyList();
    Page<Enterprise> enterprisePage = new PageImpl<>(enterprises, pageable, totalElements);

    when(authorizationService.getEnterpriseAccessStringListForCurrentUser()).thenReturn(
        enterpriseIds);
    when(
        enterpriseRepository.findFilteredCountAllEnterprisesForUser(any(), anyString())).thenReturn(
        totalElements);
    when(enterpriseRepository.findAllByFilteredPage(any(), anyString(), any())).thenReturn(
        enterprisePage);

    // Act
    Page<EnterpriseResponseDTO> result = enterpriseService.processGet(filterValue, pageable);

    // Assert
    assertNotNull(result);
    assertEquals(0, result.getTotalElements());
    assertTrue(result.getContent().isEmpty());

    verify(enterpriseRepository).findFilteredCountAllEnterprisesForUser(enterpriseIds,
        filterValue.toLowerCase());
    verify(enterpriseRepository).findAllByFilteredPage(enterpriseIds, filterValue.toLowerCase(),
        pageable);
  }

  @Test
  public void testProcessGetList_Success() {
    // Arrange
    List<Enterprise> enterprises = Collections.singletonList(enterprise);

    when(authorizationService.getEnterpriseAccessStringListForCurrentUser()).thenReturn(
        enterpriseIds);
    when(enterpriseRepository.findAllEnterprisesForUser(any())).thenReturn(enterprises);
    when(mapper.toDTO(any(Enterprise.class))).thenReturn(enterpriseResponseDTO);

    // Act
    List<EnterpriseResponseDTO> result = enterpriseService.processGetList();

    // Assert
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals(enterpriseResponseDTO, result.get(0));

    verify(enterpriseRepository).findAllEnterprisesForUser(enterpriseIds);
  }

  @Test
  public void testProcessGetList_ThrowsNotFoundException_WhenNoEnterprisesFound() {
    // Arrange
    when(authorizationService.getEnterpriseAccessStringListForCurrentUser()).thenReturn(
        enterpriseIds);
    when(enterpriseRepository.findAllEnterprisesForUser(any())).thenReturn(new ArrayList<>());

    // Act & Assert
    assertThrows(NotFoundException.class, () -> enterpriseService.processGetList());

    verify(enterpriseRepository).findAllEnterprisesForUser(enterpriseIds);
  }

  @Test
  public void testProcessGetById_Success() {
    // Arrange
    when(enterpriseRepository.findEnterpriseByEnterpriseId(enterpriseId)).thenReturn(
        Optional.of(enterprise));
    when(mapper.toDTO(enterprise)).thenReturn(enterpriseResponseDTO);

    // Act
    EnterpriseResponseDTO result = enterpriseService.processGetById(enterpriseId);

    // Assert
    assertNotNull(result);
    assertEquals(enterpriseResponseDTO, result);

    verify(enterpriseRepository).findEnterpriseByEnterpriseId(enterpriseId);
  }

  @Test
  public void testProcessGetById_ThrowsNotFoundException_WhenEnterpriseNotFound() {
    // Arrange
    when(enterpriseRepository.findEnterpriseByEnterpriseId(enterpriseId)).thenReturn(
        Optional.empty());

    // Act & Assert
    assertThrows(NotFoundException.class, () -> enterpriseService.processGetById(enterpriseId));

    verify(enterpriseRepository).findEnterpriseByEnterpriseId(enterpriseId);
  }

  @Test
  public void testProcessCreate_Success() {
    // Arrange
    doNothing().when(platformService).validatePlatformId(anyInt());
    doNothing().when(validationService).validate(enterpriseRequestDTO, ValidationContext.CREATE);

    // Mock search attributes
    SearchAttributes searchAttributes = mock(SearchAttributes.class);
    when(enterpriseSearchAttributeUtil.buildEnterpriseSearchAttributes(
        enterpriseRequestDTO.getEnterpriseId(), 
        enterpriseRequestDTO.getParentEnterpriseId()))
        .thenReturn(searchAttributes);

    // Mock workflow stub
    EnterpriseCreateWorkflow workflowStub = mock(EnterpriseCreateWorkflow.class);
    when(workflowClient.newWorkflowStub(eq(EnterpriseCreateWorkflow.class), any(WorkflowOptions.class)))
        .thenReturn(workflowStub);

    // Act
    enterpriseService.processCreate(enterpriseRequestDTO);

    // Assert
    verify(platformService).validatePlatformId(platformRequestDTO.getPlatformId());
    verify(validationService).validate(enterpriseRequestDTO, ValidationContext.CREATE);
    verify(enterpriseSearchAttributeUtil).buildEnterpriseSearchAttributes(
        enterpriseRequestDTO.getEnterpriseId(), 
        enterpriseRequestDTO.getParentEnterpriseId());
    verify(workflowClient).newWorkflowStub(eq(EnterpriseCreateWorkflow.class), any(WorkflowOptions.class));
    // We can't directly verify WorkflowClient.start since it's a static method
  }

  @Test
  public void testProcessUpdate_Success() {
    // Arrange
    doNothing().when(platformService).validatePlatformId(anyInt());
    doNothing().when(validationService).validate(enterpriseRequestDTO, ValidationContext.UPDATE);

    // Mock search attributes
    SearchAttributes searchAttributes = mock(SearchAttributes.class);
    when(enterpriseSearchAttributeUtil.buildEnterpriseSearchAttributes(
        enterpriseRequestDTO.getEnterpriseId(), null))
        .thenReturn(searchAttributes);

    // Mock workflow stub
    EnterpriseModifyWorkflow workflowStub = mock(EnterpriseModifyWorkflow.class);
    when(workflowClient.newWorkflowStub(eq(EnterpriseModifyWorkflow.class), any(WorkflowOptions.class)))
        .thenReturn(workflowStub);

    // Act
    enterpriseService.processUpdate(enterpriseRequestDTO);

    // Assert
    verify(platformService).validatePlatformId(platformRequestDTO.getPlatformId());
    verify(validationService).validate(enterpriseRequestDTO, ValidationContext.UPDATE);
    verify(enterpriseSearchAttributeUtil).buildEnterpriseSearchAttributes(
        enterpriseRequestDTO.getEnterpriseId(), null);
    verify(workflowClient).newWorkflowStub(eq(EnterpriseModifyWorkflow.class), any(WorkflowOptions.class));
    // We can't directly verify WorkflowClient.start since it's a static method
  }

  @Test
  public void testProcessDelete_Success() {
    // Arrange
    doNothing().when(validationService).validateEnterpriseId(enterpriseId);
    doNothing().when(validationService).checkIfCurrentUserHasAccessToEnterpriseId(enterpriseId);

    // Mock repository checks
    when(enterpriseRepository.existsById(enterpriseId)).thenReturn(true);
    when(enterpriseRepository.doesEnterpriseHaveChildren(enterpriseId)).thenReturn(false);

    // Mock search attributes
    SearchAttributes searchAttributes = mock(SearchAttributes.class);
    when(enterpriseSearchAttributeUtil.buildEnterpriseSearchAttributes(enterpriseId, null))
        .thenReturn(searchAttributes);

    // Mock workflow stub
    EnterpriseDeleteWorkflow workflowStub = mock(EnterpriseDeleteWorkflow.class);
    when(workflowClient.newWorkflowStub(eq(EnterpriseDeleteWorkflow.class), any(WorkflowOptions.class)))
        .thenReturn(workflowStub);

    // Act
    enterpriseService.processDelete(enterpriseId);

    // Assert
    verify(validationService).validateEnterpriseId(enterpriseId);
    verify(validationService).checkIfCurrentUserHasAccessToEnterpriseId(enterpriseId);
    verify(enterpriseRepository).existsById(enterpriseId);
    verify(enterpriseRepository).doesEnterpriseHaveChildren(enterpriseId);
    verify(enterpriseSearchAttributeUtil).buildEnterpriseSearchAttributes(enterpriseId, null);
    verify(workflowClient).newWorkflowStub(eq(EnterpriseDeleteWorkflow.class), any(WorkflowOptions.class));
    // We can't directly verify WorkflowClient.start since it's a static method
  }

  @Test
  public void testProcessDelete_ThrowsAccessDeniedException_WhenDataIntegrityViolation() {
    // Arrange
    doNothing().when(validationService).validateEnterpriseId(enterpriseId);
    doNothing().when(validationService).checkIfCurrentUserHasAccessToEnterpriseId(enterpriseId);

    // Mock repository checks
    when(enterpriseRepository.existsById(enterpriseId)).thenReturn(true);
    when(enterpriseRepository.doesEnterpriseHaveChildren(enterpriseId)).thenReturn(false);

    // Mock search attributes
    SearchAttributes searchAttributes = mock(SearchAttributes.class);
    when(enterpriseSearchAttributeUtil.buildEnterpriseSearchAttributes(enterpriseId, null))
        .thenReturn(searchAttributes);

    // Mock DataIntegrityViolationException when starting workflow
    doThrow(new DataIntegrityViolationException(DELETE_ERROR_MESSAGE))
        .when(workflowClient).newWorkflowStub(eq(EnterpriseDeleteWorkflow.class), any(WorkflowOptions.class));

    // Act & Assert
    assertThrows(AccessDeniedException.class, () -> enterpriseService.processDelete(enterpriseId));

    verify(validationService).validateEnterpriseId(enterpriseId);
    verify(validationService).checkIfCurrentUserHasAccessToEnterpriseId(enterpriseId);
    verify(enterpriseRepository).existsById(enterpriseId);
    verify(enterpriseRepository).doesEnterpriseHaveChildren(enterpriseId);
  }

  // Helper methods for creating test data
  private Platform createPlatform() {
    return Platform.builder()
        .platformId(PLATFORM_ID)
        .platformName(PLATFORM_NAME)
        .build();
  }

  private PlatformRequestDTO createPlatformRequestDTO() {
    return PlatformRequestDTO.builder()
        .platformId(PLATFORM_ID)
        .platformName(PLATFORM_NAME)
        .build();
  }

  private PlatformResponseDTO createPlatformResponseDTO() {
    return PlatformResponseDTO.builder()
        .platformId(PLATFORM_ID)
        .platformName(PLATFORM_NAME)
        .dateCreated(ZonedDateTime.now())
        .dateUpdated(ZonedDateTime.now())
        .build();
  }

  private Enterprise createEnterprise() {
    Enterprise parentEnterprise = Enterprise.builder()
        .enterpriseId(PARENT_ENTERPRISE_ID)
        .billingId(PARENT_BILLING_ID)
        .enterpriseName(PARENT_ENTERPRISE_NAME)
        .accountCountry(ACCOUNT_COUNTRY)
        .platform(platform)
        .build();

    return Enterprise.builder()
        .enterpriseId(ENTERPRISE_ID)
        .billingId(BILLING_ID)
        .enterpriseName(ENTERPRISE_NAME)
        .accountCountry(ACCOUNT_COUNTRY)
        .platform(platform)
        .parentEnterprise(parentEnterprise)
        .build();
  }

  private EnterpriseResponseDTO createEnterpriseResponseDTO() {
    return EnterpriseResponseDTO.builder()
        .enterpriseId(ENTERPRISE_ID)
        .billingId(BILLING_ID)
        .enterpriseName(ENTERPRISE_NAME)
        .accountCountry(ACCOUNT_COUNTRY)
        .platform(platformResponseDTO)
        .parentEnterpriseId(PARENT_ENTERPRISE_ID)
        .dateCreated(ZonedDateTime.now())
        .dateUpdated(ZonedDateTime.now())
        .build();
  }

  private EnterpriseRequestDTO createEnterpriseRequestDTO() {
    return EnterpriseRequestDTO.builder()
        .enterpriseId(ENTERPRISE_ID)
        .billingId(BILLING_ID)
        .enterpriseName(ENTERPRISE_NAME)
        .accountCountry(ACCOUNT_COUNTRY)
        .platformId(platformRequestDTO.getPlatformId())
        .parentEnterpriseId(PARENT_ENTERPRISE_ID)
        .build();
  }

  private Enterprise createParentEnterprise() {
    return Enterprise.builder()
        .enterpriseId(PARENT_ENTERPRISE_ID)
        .billingId(PARENT_BILLING_ID)
        .enterpriseName(PARENT_ENTERPRISE_NAME)
        .accountCountry(ACCOUNT_COUNTRY)
        .platform(platform)
        .build();
  }
}
