package net.evolveip.ossmosis.api.config.audit;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.SecurityProperties;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.test.context.ContextConfiguration;


@WebMvcTest
@ContextConfiguration(classes = {AuditFilterConfig.class, AuditRequestFilter.class})
public class AuditFilterConfigTests {

  @Autowired
  private FilterRegistrationBean<AuditRequestFilter> auditRequestFilterConfigBean;

  @Test
  @DisplayName("Test: confirm auditRequestFilter config.")
  public void testAuditFilterConfig() {
    assertNotNull(auditRequestFilterConfigBean);
    assertEquals(SecurityProperties.DEFAULT_FILTER_ORDER - 1,
        auditRequestFilterConfigBean.getOrder());
  }
}
