package net.evolveip.ossmosis.api.features.enterprise.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import net.evolveip.ossmosis.api.features.common.ValidationContext;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseEmptyViewResponseDTO;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.enterprise.repository.EnterpriseRepository;
import net.evolveip.ossmosis.api.features.login.dto.LoginInfoResponseDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginResourcePermissionResponseDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginResponseDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginRoleResponseDTO;
import net.evolveip.ossmosis.api.features.platform.entity.Platform;
import net.evolveip.ossmosis.api.utils.exceptions.DuplicateFoundException;
import net.evolveip.ossmosis.api.utils.exceptions.NotFoundException;
import net.evolveip.ossmosis.api.utils.service.AuthorizationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.access.AccessDeniedException;

@ExtendWith(MockitoExtension.class)
public class EnterpriseValidationServiceUnitTests {

  private static final String TEST_ENTERPRISE_ID = "testEnterprise";
  private static final String TEST_PARENT_ENTERPRISE_ID = "parentEnterprise";
  private static final String TEST_BILLING_ID = "testBillingId";
  private static final String TEST_ENTERPRISE_NAME = "Test Enterprise";
  private static final Integer TEST_PLATFORM_ID = 1;
  private static final String TEST_PLATFORM_NAME = "Test Platform";

  // Additional string constants
  private static final String TEST_ACCOUNT_COUNTRY = "US";
  private static final String TEST_OTHER_ENTERPRISE_ID = "otherEnterprise";
  private static final String TEST_DIFFERENT_ID = "differentId";
  private static final String TEST_ORIGINAL_PARENT = "originalParent";
  private static final String TEST_NEW_PARENT = "newParent";
  private static final String TEST_INVALID_ID = "invalid%id";
  private static final String TEST_MESSAGE = "test message";
  private static final String TEST_ERROR_MESSAGE = "test error message";

  // Login info constants
  private static final String TEST_EMAIL = "<EMAIL>";
  private static final String TEST_FIRST_NAME = "Test";
  private static final String TEST_LAST_NAME = "User";
  private static final String TEST_LOGIN_GROUP = "TestGroup";
  private static final String TEST_PHONE_NUMBER = "**********";
  private static final String TEST_AUTH_READ = "AUTH_READ";
  private static final String TEST_AUTH_WRITE = "AUTH_WRITE";

  private EnterpriseValidationService enterpriseValidationService;
  @Mock
  private EnterpriseRepository enterpriseRepository;
  @Mock
  private AuthorizationService authorizationService;

  @BeforeEach
  public void setUp() {
    enterpriseValidationService = new EnterpriseValidationService(enterpriseRepository,
        authorizationService);
  }

  // Tests for isEnterpriseIdValid method
  @Test
  public void isEnterpriseIdValid_existingId_shouldReturnTrue() {
    when(enterpriseRepository.existsById(TEST_ENTERPRISE_ID)).thenReturn(true);

    boolean result = enterpriseValidationService.isEnterpriseIdValid(TEST_ENTERPRISE_ID);

    assertTrue(result);
  }

  @Test
  public void isEnterpriseIdValid_nonExistingId_shouldReturnFalse() {
    when(enterpriseRepository.existsById(TEST_ENTERPRISE_ID)).thenReturn(false);

    boolean result = enterpriseValidationService.isEnterpriseIdValid(TEST_ENTERPRISE_ID);

    assertFalse(result);
  }

  // Tests for validateEnterpriseId method
  @Test
  public void validateEnterpriseId_existingId_shouldNotThrowException() {
    when(enterpriseRepository.existsById(TEST_ENTERPRISE_ID)).thenReturn(true);

    assertDoesNotThrow(() -> enterpriseValidationService.validateEnterpriseId(TEST_ENTERPRISE_ID));
  }

  @Test
  public void validateEnterpriseId_nonExistingId_shouldThrowNotFoundException() {
    when(enterpriseRepository.existsById(TEST_ENTERPRISE_ID)).thenReturn(false);

    assertThrows(NotFoundException.class,
        () -> enterpriseValidationService.validateEnterpriseId(TEST_ENTERPRISE_ID));
  }

  @Test
  public void validateEnterpriseId_nullId_shouldThrowNotFoundException() {
    assertThrows(NotFoundException.class,
        () -> enterpriseValidationService.validateEnterpriseId(null));
  }

  @Test
  public void validateEnterpriseId_emptyId_shouldThrowNotFoundException() {
    assertThrows(NotFoundException.class,
        () -> enterpriseValidationService.validateEnterpriseId(""));
  }

  // Tests for isEnterpriseNameValid method
  @Test
  public void isEnterpriseNameValid_existingName_shouldReturnTrue() {
    when(enterpriseRepository.findEnterpriseByEnterpriseName(TEST_ENTERPRISE_NAME))
        .thenReturn(Optional.of(createTestEnterprise()));

    boolean result = enterpriseValidationService.isEnterpriseNameValid(TEST_ENTERPRISE_NAME);

    assertTrue(result);
  }

  @Test
  public void isEnterpriseNameValid_nonExistingName_shouldReturnFalse() {
    when(enterpriseRepository.findEnterpriseByEnterpriseName(TEST_ENTERPRISE_NAME))
        .thenReturn(Optional.empty());

    boolean result = enterpriseValidationService.isEnterpriseNameValid(TEST_ENTERPRISE_NAME);

    assertFalse(result);
  }

  // Tests for validateEnterpriseName method
  @Test
  public void validateEnterpriseName_nonExistingName_shouldThrowNotFoundException() {
    when(enterpriseRepository.existsById(TEST_ENTERPRISE_NAME)).thenReturn(false);

    assertThrows(NotFoundException.class,
        () -> enterpriseValidationService.validateEnterpriseName(TEST_ENTERPRISE_NAME));
  }

  @Test
  public void validateEnterpriseName_nullName_shouldThrowNotFoundException() {
    assertThrows(NotFoundException.class,
        () -> enterpriseValidationService.validateEnterpriseName(null));
  }

  @Test
  public void validateEnterpriseName_emptyName_shouldThrowNotFoundException() {
    assertThrows(NotFoundException.class,
        () -> enterpriseValidationService.validateEnterpriseName(""));
  }

  // Tests for checkIfCurrentUserHasAccessToEnterpriseId method
  @Test
  public void checkIfCurrentUserHasAccessToEnterpriseId_validAccess_shouldNotThrowException() {
    assertDoesNotThrow(() -> enterpriseValidationService
        .checkIfCurrentUserHasAccessToEnterpriseId(TEST_ENTERPRISE_ID));
  }

  // Tests for checkIfBillingIdAlreadyInUse method
  @Test
  public void checkIfBillingIdAlreadyInUse_createContext_newBillingId_shouldNotThrowException() {
    when(enterpriseRepository.countByBillingId(TEST_BILLING_ID)).thenReturn(0);

    assertDoesNotThrow(() -> enterpriseValidationService
        .checkIfBillingIdAlreadyInUse(TEST_ENTERPRISE_ID, TEST_BILLING_ID,
            ValidationContext.CREATE));
  }

  @Test
  public void checkIfBillingIdAlreadyInUse_createContext_existingBillingId_shouldThrowException() {
    when(enterpriseRepository.countByBillingId(TEST_BILLING_ID)).thenReturn(1);

    assertThrows(DuplicateFoundException.class, () -> enterpriseValidationService
        .checkIfBillingIdAlreadyInUse(TEST_ENTERPRISE_ID, TEST_BILLING_ID,
            ValidationContext.CREATE));
  }

  @Test
  public void checkIfBillingIdAlreadyInUse_updateContext_newBillingId_shouldNotThrowException() {
    when(enterpriseRepository.countByBillingId(TEST_BILLING_ID, TEST_ENTERPRISE_ID)).thenReturn(0);

    assertDoesNotThrow(() -> enterpriseValidationService
        .checkIfBillingIdAlreadyInUse(TEST_ENTERPRISE_ID, TEST_BILLING_ID,
            ValidationContext.UPDATE));
  }

  @Test
  public void checkIfBillingIdAlreadyInUse_updateContext_existingBillingId_shouldThrowException() {
    when(enterpriseRepository.countByBillingId(TEST_BILLING_ID, TEST_ENTERPRISE_ID)).thenReturn(1);

    assertThrows(DuplicateFoundException.class, () -> enterpriseValidationService
        .checkIfBillingIdAlreadyInUse(TEST_ENTERPRISE_ID, TEST_BILLING_ID,
            ValidationContext.UPDATE));
  }

  // Tests for checkIfEnterpriseIdExistsWithADifferentPlatformId method
  @Test
  public void checkIfEnterpriseIdExistsWithADifferentPlatformId_nonExistingId_shouldNotThrowException() {
    when(enterpriseRepository.findEnterpriseByEnterpriseId(TEST_ENTERPRISE_ID))
        .thenReturn(Optional.empty());

    assertDoesNotThrow(() -> enterpriseValidationService
        .checkIfEnterpriseIdExistsWithADifferentPlatformId(TEST_ENTERPRISE_ID, TEST_PLATFORM_ID));
  }

  @Test
  public void checkIfEnterpriseIdExistsWithADifferentPlatformId_existingIdSamePlatform_shouldNotThrowException() {
    Enterprise enterprise = createTestEnterprise();
    when(enterpriseRepository.findEnterpriseByEnterpriseId(TEST_ENTERPRISE_ID))
        .thenReturn(Optional.of(enterprise));

    assertDoesNotThrow(() -> enterpriseValidationService
        .checkIfEnterpriseIdExistsWithADifferentPlatformId(TEST_ENTERPRISE_ID, TEST_PLATFORM_ID));
  }

  @Test
  public void checkIfEnterpriseIdExistsWithADifferentPlatformId_existingIdDifferentPlatform_shouldThrowException() {
    Enterprise enterprise = createTestEnterprise();
    when(enterpriseRepository.findEnterpriseByEnterpriseId(TEST_ENTERPRISE_ID))
        .thenReturn(Optional.of(enterprise));

    assertThrows(DuplicateFoundException.class, () -> enterpriseValidationService
        .checkIfEnterpriseIdExistsWithADifferentPlatformId(TEST_ENTERPRISE_ID, 2));
  }

  // Tests for checkIfEnterpriseNameExistsWithADifferentEnterpriseId method
  @Test
  public void checkIfEnterpriseNameExistsWithADifferentEnterpriseId_nonExistingName_shouldNotThrowException() {
    when(enterpriseRepository.findEnterpriseByEnterpriseName(TEST_ENTERPRISE_NAME))
        .thenReturn(Optional.empty());

    assertDoesNotThrow(() -> enterpriseValidationService
        .checkIfEnterpriseNameExistsWithADifferentEnterpriseId(TEST_ENTERPRISE_ID,
            TEST_ENTERPRISE_NAME));
  }

  @Test
  public void checkIfEnterpriseNameExistsWithADifferentEnterpriseId_existingNameSameId_shouldNotThrowException() {
    Enterprise enterprise = createTestEnterprise();
    when(enterpriseRepository.findEnterpriseByEnterpriseName(TEST_ENTERPRISE_NAME))
        .thenReturn(Optional.of(enterprise));

    assertDoesNotThrow(() -> enterpriseValidationService
        .checkIfEnterpriseNameExistsWithADifferentEnterpriseId(TEST_ENTERPRISE_ID,
            TEST_ENTERPRISE_NAME));
  }

  @Test
  public void checkIfEnterpriseNameExistsWithADifferentEnterpriseId_existingNameDifferentId_shouldThrowException() {
    Enterprise enterprise = createTestEnterprise();
    enterprise.setEnterpriseId(TEST_DIFFERENT_ID);
    when(enterpriseRepository.findEnterpriseByEnterpriseName(TEST_ENTERPRISE_NAME))
        .thenReturn(Optional.of(enterprise));

    assertThrows(DuplicateFoundException.class, () -> enterpriseValidationService
        .checkIfEnterpriseNameExistsWithADifferentEnterpriseId(TEST_ENTERPRISE_ID,
            TEST_ENTERPRISE_NAME));
  }

  // Tests for userCanAssignParentIdForEnterprise method
  @Test
  public void userCanAssignParentIdForEnterprise_parentMatchesCurrentEnterprise_shouldNotThrowException() {
    // Create a list with enterprises that have access
    List<EnterpriseEmptyViewResponseDTO> enterprises = new ArrayList<>();
    enterprises.add(createTestEnterpriseEmptyDTO(TEST_PARENT_ENTERPRISE_ID, null));

    when(authorizationService.getEnterpriseAccessListForLoggedInUser()).thenReturn(enterprises);

    assertDoesNotThrow(() -> enterpriseValidationService
        .userCanAssignParentIdForEnterprise(TEST_ENTERPRISE_ID, TEST_PARENT_ENTERPRISE_ID));
  }

  @Test
  public void userCanAssignParentIdForEnterprise_noAccess_shouldThrowException() {
    when(authorizationService.getEnterpriseAccessListForLoggedInUser()).thenReturn(
        Collections.emptyList());

    assertThrows(AccessDeniedException.class, () -> enterpriseValidationService
        .userCanAssignParentIdForEnterprise(TEST_ENTERPRISE_ID, TEST_PARENT_ENTERPRISE_ID));
  }

  // Tests for checkIfCanMoveEnterpriseToTopLevel method
  @Test
  public void checkIfCanMoveEnterpriseToTopLevel_notPrimaryEnterprise_shouldNotThrowException() {
    LoginInfoResponseDTO loginInfo = createTestLoginInfoDTO(TEST_OTHER_ENTERPRISE_ID);
    when(authorizationService.getLoggedInUserInfo()).thenReturn(loginInfo);

    assertDoesNotThrow(() -> enterpriseValidationService
        .checkIfCanMoveEnterpriseToTopLevel(TEST_ENTERPRISE_ID, TEST_PARENT_ENTERPRISE_ID));
  }

  @Test
  public void checkIfCanMoveEnterpriseToTopLevel_primaryEnterpriseParentMatches_shouldNotThrowException() {
    LoginInfoResponseDTO loginInfo = createTestLoginInfoDTO(TEST_ENTERPRISE_ID);
    when(authorizationService.getLoggedInUserInfo()).thenReturn(loginInfo);

    EnterpriseEmptyViewResponseDTO enterpriseDTO = createTestEnterpriseEmptyDTO(null,
        TEST_PARENT_ENTERPRISE_ID);
    when(authorizationService.getCurrentUserEnterprise()).thenReturn(enterpriseDTO);

    assertDoesNotThrow(() -> enterpriseValidationService
        .checkIfCanMoveEnterpriseToTopLevel(TEST_ENTERPRISE_ID, TEST_PARENT_ENTERPRISE_ID));
  }

  @Test
  public void checkIfCanMoveEnterpriseToTopLevel_moveTopLevelEnterprise_shouldThrowException() {
    LoginInfoResponseDTO loginInfo = createTestLoginInfoDTO(TEST_ENTERPRISE_ID);
    when(authorizationService.getLoggedInUserInfo()).thenReturn(loginInfo);

    EnterpriseEmptyViewResponseDTO enterpriseDTO = createTestEnterpriseEmptyDTO(null,
        TEST_ORIGINAL_PARENT);
    when(authorizationService.getCurrentUserEnterprise()).thenReturn(enterpriseDTO);

    assertThrows(AccessDeniedException.class, () -> enterpriseValidationService
        .checkIfCanMoveEnterpriseToTopLevel(TEST_ENTERPRISE_ID, TEST_NEW_PARENT));
  }

  // Tests for checkIfEnterpriseIdAlreadyAChildEnterpriseId method
  @Test
  public void checkIfEnterpriseIdAlreadyAChildEnterpriseId_notChild_shouldNotThrowException() {
    when(enterpriseRepository.isParentEnterpriseAlreadyChild(TEST_ENTERPRISE_ID,
        TEST_PARENT_ENTERPRISE_ID))
        .thenReturn(false);

    assertDoesNotThrow(() -> enterpriseValidationService
        .checkIfEnterpriseIdAlreadyAChildEnterpriseId(TEST_ENTERPRISE_ID,
            TEST_PARENT_ENTERPRISE_ID));
  }

  @Test
  public void checkIfEnterpriseIdAlreadyAChildEnterpriseId_isChild_shouldThrowException() {
    when(enterpriseRepository.isParentEnterpriseAlreadyChild(TEST_ENTERPRISE_ID,
        TEST_PARENT_ENTERPRISE_ID))
        .thenReturn(true);

    assertThrows(AccessDeniedException.class, () -> enterpriseValidationService
        .checkIfEnterpriseIdAlreadyAChildEnterpriseId(TEST_ENTERPRISE_ID,
            TEST_PARENT_ENTERPRISE_ID));
  }

  // Tests for checkIfEnterpriseIdOrBillingIdFormatIsValid method
  @Test
  public void checkIfEnterpriseIdOrBillingIdFormatIsValid_validId_shouldNotThrowException() {
    assertDoesNotThrow(() -> enterpriseValidationService
        .checkIfEnterpriseIdOrBillingIdFormatIsValid(TEST_ENTERPRISE_ID, TEST_MESSAGE));
  }

  @Test
  public void checkIfEnterpriseIdOrBillingIdFormatIsValid_invalidId_shouldThrowException() {
    assertThrows(IllegalArgumentException.class, () -> enterpriseValidationService
        .checkIfEnterpriseIdOrBillingIdFormatIsValid(TEST_INVALID_ID, TEST_ERROR_MESSAGE));
  }

  // Helper methods to create test objects
  private Enterprise createTestEnterprise() {
    Platform platform = Platform.builder()
        .platformId(TEST_PLATFORM_ID)
        .platformName(TEST_PLATFORM_NAME)
        .build();

    return Enterprise.builder()
        .enterpriseId(TEST_ENTERPRISE_ID)
        .billingId(TEST_BILLING_ID)
        .enterpriseName(TEST_ENTERPRISE_NAME)
        .accountCountry(TEST_ACCOUNT_COUNTRY)
        .platform(platform)
        .build();
  }

  private EnterpriseEmptyViewResponseDTO createTestEnterpriseEmptyDTO(String enterpriseId,
      String parentEnterpriseId) {
    EnterpriseEmptyViewResponseDTO dto = new EnterpriseEmptyViewResponseDTO();
    dto.setEnterpriseId(enterpriseId != null ? enterpriseId : TEST_ENTERPRISE_ID);
    dto.setBillingId(TEST_BILLING_ID);
    dto.setEnterpriseName(TEST_ENTERPRISE_NAME);
    dto.setAccountCountry(TEST_ACCOUNT_COUNTRY);
    dto.setPlatformId(TEST_PLATFORM_ID);
    dto.setParentEnterpriseId(parentEnterpriseId);
    return dto;
  }

  private LoginInfoResponseDTO createTestLoginInfoDTO(String primaryEnterpriseId) {
    return LoginInfoResponseDTO.builder()
        .loginResponseDTO(createTestLoginResponseDTO(primaryEnterpriseId))
        .authorities(Arrays.asList(TEST_AUTH_READ, TEST_AUTH_WRITE))
        .enterprises(List.of(createTestEnterpriseEmptyDTO(null, null)))
        .build();
  }

  private LoginResponseDTO createTestLoginResponseDTO(String primaryEnterpriseId) {
    return LoginResponseDTO.builder()
        .loginId(1L)
        .loginEmail(TEST_EMAIL)
        .loginNameFirst(TEST_FIRST_NAME)
        .loginNameLast(TEST_LAST_NAME)
        .active(true)
        .locked(false)
        .loginGroup(TEST_LOGIN_GROUP)
        .loginPhoneNumber(TEST_PHONE_NUMBER)
        .loginPrimaryEnterpriseId(primaryEnterpriseId)
        .roles(Collections.singletonList(createTestLoginRoleResponseDTO()))
        .dateCreated(ZonedDateTime.now())
        .dateUpdated(ZonedDateTime.now())
        .userPermissionList(
            Collections.singletonList(createTestLoginResourcePermissionResponseDTO()))
        .build();
  }

  private LoginRoleResponseDTO createTestLoginRoleResponseDTO() {
    return LoginRoleResponseDTO.builder()
        .loginId(1L)
        .roleId(1)
        .dateCreated(ZonedDateTime.now())
        .dateUpdated(ZonedDateTime.now())
        .build();
  }

  private LoginResourcePermissionResponseDTO createTestLoginResourcePermissionResponseDTO() {
    return LoginResourcePermissionResponseDTO.builder()
        .resourceId(1)
        .permissionId(1)
        .build();
  }
}