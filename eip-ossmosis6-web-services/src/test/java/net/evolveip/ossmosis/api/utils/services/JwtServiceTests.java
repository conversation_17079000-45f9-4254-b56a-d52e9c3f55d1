package net.evolveip.ossmosis.api.utils.services;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import net.evolveip.ossmosis.api.utils.service.JwtService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtException;
import org.springframework.test.context.ContextConfiguration;

@WebMvcTest()
@ContextConfiguration(classes = {
    JwtService.class, JwtDecoder.class
})
public class JwtServiceTests {

  @MockBean
  private JwtDecoder jwtDecoder;

  @Autowired
  private JwtService jwtService;

  private String testEmail;

  private Jwt jwtSource;

  @BeforeEach
  public void setup() {
    testEmail = "<EMAIL>";
    Map<String, Object> jwtHeaders = new HashMap<>();
    jwtHeaders.put("header", "useless");

    Map<String, Object> claims = new HashMap<>();
    claims.put("scope", "");
    claims.put("email", testEmail);
    claims.put("sub", "test");
    claims.put("exp", Instant.now().plusSeconds(60000));

    jwtSource = new Jwt("test-token-val", Instant.now(), Instant.now().plusSeconds(60000L),
        jwtHeaders, claims);
  }

  @Test
  @DisplayName("Test: Confirm jwtService extracting info methods work")
  public void JwtServiceTest() {

    Mockito.when(jwtDecoder.decode(anyString())).thenReturn(jwtSource);

    assertEquals(testEmail, jwtService.extractUserEmail(jwtSource.getTokenValue()));
    assertEquals("test", jwtService.extractUserName(jwtSource.getTokenValue()));
    assertTrue(jwtService.isTokenValid(jwtSource.getTokenValue(),
        new User(testEmail, "test", new ArrayList<>())));

    Mockito.when(jwtDecoder.decode(anyString())).thenThrow(new JwtException("error"));
    assertNull(jwtService.extractUserName(jwtSource.getTokenValue()));
  }

  @Test
  @DisplayName("Test: Confirm jwtService extracting info methods work with a parsing excepption")
  public void JwtServiceTestExceptionParsingToken() {
    Mockito.when(jwtDecoder.decode(anyString())).thenThrow(new JwtException("error"));
    assertNull(jwtService.extractUserName(jwtSource.getTokenValue()));
  }
}
