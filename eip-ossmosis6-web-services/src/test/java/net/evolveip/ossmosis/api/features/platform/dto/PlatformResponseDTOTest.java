package net.evolveip.ossmosis.api.features.platform.dto;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.test.context.ActiveProfiles;

@ActiveProfiles("test")
@DataJpaTest
public class PlatformResponseDTOTest {

  private Integer samplePlatformId;
  private String samplePlatformName;

  @BeforeEach
  void setup() {
    samplePlatformId = 1;
    samplePlatformName = "TestPlatformName";
  }

  @Test
  void testStateIsCorrect() {
    PlatformResponseDTO underTest = PlatformResponseDTO
        .builder()
        .platformId(samplePlatformId)
        .platformName(samplePlatformName)
        .build();

    assertThat(underTest.getPlatformId()).isEqualTo(samplePlatformId);
    assertThat(underTest.getPlatformName()).isEqualTo(samplePlatformName);
  }

  @Test
  void testEqualsTwoObjectsWithSameValues() {
    PlatformResponseDTO underTest1 = PlatformResponseDTO
        .builder()
        .platformId(samplePlatformId)
        .platformName(samplePlatformName)
        .build();

    PlatformResponseDTO underTest2 = PlatformResponseDTO
        .builder()
        .platformId(samplePlatformId)
        .platformName(samplePlatformName)
        .build();

    assertThat(underTest1).isEqualTo(underTest2);
  }

  @Test
  void testEqualsTwoObjectsWithDifferentId() {
    PlatformResponseDTO underTest1 = PlatformResponseDTO
        .builder()
        .platformId(samplePlatformId)
        .platformName(samplePlatformName)
        .build();

    PlatformResponseDTO underTest2 = PlatformResponseDTO
        .builder()
        .platformId(2)
        .platformName(samplePlatformName)
        .build();

    assertThat(underTest1).isNotEqualTo(underTest2);
  }

  @Test
  void testEqualsTwoObjectsWithDifferentName() {
    PlatformResponseDTO underTest1 = PlatformResponseDTO
        .builder()
        .platformId(samplePlatformId)
        .platformName(samplePlatformName)
        .build();

    PlatformResponseDTO underTest2 = PlatformResponseDTO
        .builder()
        .platformId(samplePlatformId)
        .platformName("AnotherPlatformName")
        .build();

    assertThat(underTest1).isNotEqualTo(underTest2);
  }
}