package net.evolveip.ossmosis.api.features.enterprise.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Optional;
import net.evolveip.ossmosis.api.features.enterprise.constant.EnterpriseConstants;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.enterprise.repository.EnterpriseRepository;
import net.evolveip.ossmosis.api.features.platform.entity.Platform;
import net.evolveip.ossmosis.api.features.platform.service.RootPlatformService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class RootEnterpriseServiceUnitTest {

  private static final String ROOT_ENTERPRISE_ID = EnterpriseConstants.ROOT_ENTERPRISE_ID;
  private static final String ROOT_ENTERPRISE_NAME = EnterpriseConstants.ROOT_ENTERPRISE_NAME;
  private static final String ACCOUNT_COUNTRY = "USA";
  @Mock
  private EnterpriseRepository enterpriseRepository;
  @Mock
  private RootPlatformService rootPlatformService;
  @InjectMocks
  private RootEnterpriseService rootEnterpriseService;
  private Platform rootPlatform;
  private Enterprise rootEnterprise;

  @BeforeEach
  void setUp() {
    // Setup root platform
    rootPlatform = createRootPlatform();

    // Setup root enterprise
    rootEnterprise = createRootEnterprise(rootPlatform);
  }

  @Test
  void createRootEnterprise_whenRootEnterpriseDoesNotExist_shouldCreateRootEnterprise() {
    // Given
    when(rootPlatformService.getRootPlatform()).thenReturn(rootPlatform);
    when(enterpriseRepository.findEnterpriseByEnterpriseId(ROOT_ENTERPRISE_ID)).thenReturn(
        Optional.empty());
    when(enterpriseRepository.save(any(Enterprise.class))).thenReturn(rootEnterprise);

    // When
    rootEnterpriseService.createRootEnterprise();

    // Then
    verify(rootPlatformService, times(1)).getRootPlatform();
    verify(enterpriseRepository, times(1)).findEnterpriseByEnterpriseId(ROOT_ENTERPRISE_ID);
    verify(enterpriseRepository, times(1)).save(any(Enterprise.class));
    verify(enterpriseRepository, times(1)).updateTreeClosure(ROOT_ENTERPRISE_ID, null);
  }

  @Test
  void createRootEnterprise_whenRootEnterpriseExists_shouldNotCreateRootEnterprise() {
    // Given
    when(rootPlatformService.getRootPlatform()).thenReturn(rootPlatform);
    when(enterpriseRepository.findEnterpriseByEnterpriseId(ROOT_ENTERPRISE_ID))
        .thenReturn(Optional.of(rootEnterprise));

    // When
    rootEnterpriseService.createRootEnterprise();

    // Then
    verify(rootPlatformService, times(1)).getRootPlatform();
    verify(enterpriseRepository, times(1)).findEnterpriseByEnterpriseId(ROOT_ENTERPRISE_ID);
    verify(enterpriseRepository, times(0)).save(any(Enterprise.class));
    verify(enterpriseRepository, times(0)).updateTreeClosure(any(), any());
  }

  @Test
  void getRootEnterprise_whenRootEnterpriseExists_shouldReturnRootEnterprise() {
    // Given
    when(enterpriseRepository.findEnterpriseByEnterpriseId(ROOT_ENTERPRISE_ID))
        .thenReturn(Optional.of(rootEnterprise));

    // When
    Enterprise result = rootEnterpriseService.getRootEnterprise();

    // Then
    verify(enterpriseRepository, times(1)).findEnterpriseByEnterpriseId(ROOT_ENTERPRISE_ID);
    assertEquals(rootEnterprise, result);
    assertEquals(ROOT_ENTERPRISE_ID, result.getEnterpriseId());
    assertEquals(ROOT_ENTERPRISE_NAME, result.getEnterpriseName());
    assertEquals(ACCOUNT_COUNTRY, result.getAccountCountry());
    assertEquals(rootPlatform, result.getPlatform());
  }

  @Test
  void getRootEnterprise_whenRootEnterpriseDoesNotExist_shouldThrowException() {
    // Given
    when(enterpriseRepository.findEnterpriseByEnterpriseId(ROOT_ENTERPRISE_ID)).thenReturn(
        Optional.empty());

    // When & Then
    RuntimeException exception = assertThrows(RuntimeException.class,
        () -> rootEnterpriseService.getRootEnterprise());
    assertEquals("Root enterprise not found", exception.getMessage());
  }

  // Helper methods to create test entities
  private Platform createRootPlatform() {
    return Platform.builder()
        .platformId(1)
        .platformName("Root Platform")
        .build();
  }

  private Enterprise createRootEnterprise(Platform platform) {
    return Enterprise.builder()
        .enterpriseId(ROOT_ENTERPRISE_ID)
        .enterpriseName(ROOT_ENTERPRISE_NAME)
        .billingId(ROOT_ENTERPRISE_ID)
        .accountCountry(ACCOUNT_COUNTRY)
        .platform(platform)
        .parentEnterprise(null)
        .build();
  }
}