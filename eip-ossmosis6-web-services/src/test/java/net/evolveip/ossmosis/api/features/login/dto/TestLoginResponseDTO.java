package net.evolveip.ossmosis.api.features.login.dto;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.test.context.ActiveProfiles;


// The name of the test class does not follow the naming standard because somehow maven picks up
// this class as an instance for one of the prior tests.
@ActiveProfiles({"test", "docker"})
@DataJpaTest
public class TestLoginResponseDTO {

  private LoginResponseDTO loginResponseDTO;
  private Long loginId;
  private String loginEmail;
  private String loginNameFirst;
  private String loginNameLast;
  private Boolean active;
  private Boolean locked;
  private String loginGroup;
  private String loginPhoneNumber;
  private String loginPrimaryEnterpriseId;

  @BeforeEach
  public void setup() {
    loginId = 1L;
    loginEmail = "<EMAIL>";
    loginNameFirst = "NameFirst";
    loginNameLast = "NameLast";
    active = true;
    locked = false;
    loginGroup = "admin";
    loginPhoneNumber = "(*************";
    loginPrimaryEnterpriseId = "1000000009";
  }

  @DisplayName("Test: instantiate a loginRequestDTO")
  @Test
  public void testStateIsCorrect() {
    loginResponseDTO = LoginResponseDTO
        .builder()
        .loginId(loginId)
        .loginEmail(loginEmail)
        .loginNameFirst(loginNameFirst)
        .loginNameLast(loginNameLast)
        .active(active)
        .locked(locked)
        .loginGroup(loginGroup)
        .loginPhoneNumber(loginPhoneNumber)
        .loginPrimaryEnterpriseId(loginPrimaryEnterpriseId)
        .build();

    assertThat(loginResponseDTO.getLoginId()).isEqualTo(loginId);
    assertThat(loginResponseDTO.getLoginEmail()).isEqualTo(loginEmail);
    assertThat(loginResponseDTO.getLoginNameFirst()).isEqualTo(loginNameFirst);
    assertThat(loginResponseDTO.getLoginNameLast()).isEqualTo(loginNameLast);
    assertThat(loginResponseDTO.getActive()).isEqualTo(active);
    assertThat(loginResponseDTO.getLocked()).isEqualTo(locked);
    assertThat(loginResponseDTO.getLoginGroup()).isEqualTo(loginGroup);
    assertThat(loginResponseDTO.getLoginPhoneNumber()).isEqualTo(loginPhoneNumber);
    assertThat(loginResponseDTO.getLoginPrimaryEnterpriseId()).isEqualTo(loginPrimaryEnterpriseId);
  }

  @DisplayName("Test: equality of two loginRequestDTO objects instantiated with the same values")
  @Test
  void testEqualsTwoObjectsWithSameValues() {
    LoginResponseDTO loginResponseDTO1 = LoginResponseDTO
        .builder()
        .loginId(loginId)
        .loginEmail(loginEmail)
        .loginNameFirst(loginNameFirst)
        .loginNameLast(loginNameLast)
        .active(active)
        .locked(locked)
        .loginGroup(loginGroup)
        .loginPhoneNumber(loginPhoneNumber)
        .loginPrimaryEnterpriseId(loginPrimaryEnterpriseId)
        .build();

    LoginResponseDTO loginResponseDTO2 = LoginResponseDTO
        .builder()
        .loginId(loginId)
        .loginEmail(loginEmail)
        .loginNameFirst(loginNameFirst)
        .loginNameLast(loginNameLast)
        .active(active)
        .locked(locked)
        .loginGroup(loginGroup)
        .loginPhoneNumber(loginPhoneNumber)
        .loginPrimaryEnterpriseId(loginPrimaryEnterpriseId)
        .build();

    assertThat(loginResponseDTO1).isEqualTo(loginResponseDTO2);
  }

  @DisplayName("Test: inequality of two loginRequestDTO objects with two different ids")
  @Test
  public void testEqualsTwoObjectsWithDifferentId() {
    LoginResponseDTO loginResponseDTO1 = LoginResponseDTO
        .builder()
        .loginId(loginId)
        .loginEmail(loginEmail)
        .loginNameFirst(loginNameFirst)
        .loginNameLast(loginNameLast)
        .active(active)
        .locked(locked)
        .loginGroup(loginGroup)
        .loginPhoneNumber(loginPhoneNumber)
        .loginPrimaryEnterpriseId(loginPrimaryEnterpriseId)
        .build();

    LoginResponseDTO loginResponseDTO2 = LoginResponseDTO
        .builder()
        .loginId(2L)
        .loginEmail(loginEmail)
        .loginNameFirst(loginNameFirst)
        .loginNameLast(loginNameLast)
        .active(active)
        .locked(locked)
        .loginGroup(loginGroup)
        .loginPhoneNumber(loginPhoneNumber)
        .loginPrimaryEnterpriseId(loginPrimaryEnterpriseId)
        .build();

    assertThat(loginResponseDTO1).isNotEqualTo(loginResponseDTO2);
  }

  @DisplayName("Test: inequality of two loginRequestDTO objects with two different loginEmail")
  @Test
  void testEqualsTwoObjectsWithDifferentName() {
    LoginResponseDTO loginResponseDTO1 = LoginResponseDTO
        .builder()
        .loginId(loginId)
        .loginEmail("<EMAIL>")
        .loginNameFirst(loginNameFirst)
        .loginNameLast(loginNameLast)
        .active(active)
        .locked(locked)
        .loginGroup(loginGroup)
        .loginPhoneNumber(loginPhoneNumber)
        .loginPrimaryEnterpriseId(loginPrimaryEnterpriseId)
        .build();

    LoginResponseDTO loginResponseDTO2 = LoginResponseDTO
        .builder()
        .loginId(loginId)
        .loginEmail("<EMAIL>")
        .loginNameFirst(loginNameFirst)
        .loginNameLast(loginNameLast)
        .active(active)
        .locked(locked)
        .loginGroup(loginGroup)
        .loginPhoneNumber(loginPhoneNumber)
        .loginPrimaryEnterpriseId(loginPrimaryEnterpriseId)
        .build();

    assertThat(loginResponseDTO1).isNotEqualTo(loginResponseDTO2);
  }
}