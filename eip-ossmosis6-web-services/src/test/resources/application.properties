# Spring General
spring.config.import=optional:file:test.properties
spring.profiles.active=${ENVIRONMENT}
server.port=${SERVER_PORT:8090}
server.servlet.context-path=/ossmosis6/api/v1
spring.application.name=ossmosis6
spring.cors.allowed-headers=Authorization,Content-Type
spring.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.main.allow-circular-references=false
spring.jpa.hibernate.naming.implicit-strategy=net.evolveip.ossmosis.api.config.dao.OssmosisImplicitNamingStrategy
spring.cors.max-age=${CORS_MAX_AGE}
spring.cors.allowed-origins=${CORS_ALLOWED_ORIGINS}
# Spring Docs
springdoc.default-produces-media-type=application/json
springdoc.default-consumes-media-type=application/json
ossmosis.springdoc.allowed-paths=/v3/api-docs/**,/swagger-ui/*
spring.datasource.url=jdbc:h2:mem:db;DB_CLOSE_DELAY=-1;MODE=PostgreSQL
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.sql.init.platform=h2
spring.sql.init.mode=always
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.h2.console.enabled=true
spring.jpa.defer-datasource-initialization=false
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.id.new_generator_mappings=true
#spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
#spring.sql.init.schema-locations=classpath:schema.sql
#spring.sql.init.data-locations=classpath:data.sql