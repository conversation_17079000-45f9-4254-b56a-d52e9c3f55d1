DROP FUNCTION IF EXISTS partnerprovider.fn_enterprise_update_tree_closure(character varying, character varying);

CREATE OR REPLACE FUNCTION partnerprovider.fn_enterprise_update_tree_closure(
    "childEnterpriseId" character varying,
    "parentEnterpriseId" character varying)
    RETURNS boolean
    LANGUAGE plpgsql
AS
$BODY$
BEGIN

    IF not exists(select 1
                  from partnerprovider.enterprise_tree_closure
                  where ancestor_enterprise_id = $1) then
        --new enterprise insert
        insert into partnerprovider.enterprise_tree_closure(ancestor_enterprise_id, descendant_enterprise_id)
        select distinct t.ancestor_enterprise_id, $1
        from partnerprovider.enterprise_tree_closure t
        where t.descendant_enterprise_id = $2
        union all
        select $1, $1;
    else
        --update position case
        DELETE
        FROM partnerprovider.enterprise_tree_closure
        WHERE descendant_enterprise_id in (SELECT descendant_enterprise_id
                                           from partnerprovider.enterprise_tree_closure
                                           where ancestor_enterprise_id = $1)
          AND ancestor_enterprise_id in (SELECT ancestor_enterprise_id
                                         from partnerprovider.enterprise_tree_closure
                                         where descendant_enterprise_id = $1
                                           AND ancestor_enterprise_id <> descendant_enterprise_id);

        INSERT INTO partnerprovider.enterprise_tree_closure (ancestor_enterprise_id, descendant_enterprise_id)
        SELECT main.ancestor_enterprise_id, sub.descendant_enterprise_id
        from partnerprovider.enterprise_tree_closure as main
                 cross join partnerprovider.enterprise_tree_closure as sub
        where main.descendant_enterprise_id = $2
          AND sub.ancestor_enterprise_id = $1;
    end if;

    return true;

END
$BODY$;

ALTER FUNCTION partnerprovider.fn_enterprise_update_tree_closure(character varying, character varying)
    OWNER TO ossmosis6_owner;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_enterprise_update_tree_closure
    (character varying, character varying) TO PUBLIC;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_enterprise_update_tree_closure
    (character varying, character varying) TO ossmosis6_owner WITH GRANT OPTION;

