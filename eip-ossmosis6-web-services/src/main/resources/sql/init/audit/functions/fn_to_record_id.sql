DROP FUNCTION IF EXISTS audit.fn_to_record_id(text[], jsonb);

CREATE OR REPLACE FUNCTION audit.fn_to_record_id(
    pkey_cols text[],
    rec jsonb)
    RETURNS text
    LANGUAGE 'sql'
    COST 100
    STABLE PARALLEL UNSAFE
AS
$BODY$
select case
           when rec is null then null
           -- if no primary key exists, use a random uuid
           when pkey_cols = array []::text[] then ''
           else (select (($2 ->> key_)::text)
                 from unnest($1) x(key_))
           end
$BODY$;

ALTER FUNCTION audit.fn_to_record_id(text[], jsonb)
    OWNER TO ossmosis6_owner;

GRANT EXECUTE ON FUNCTION audit.fn_to_record_id(text[], jsonb) TO PUBLIC;

GRANT EXECUTE ON FUNCTION audit.fn_to_record_id(text[], jsonb) TO ossmosis6_owner WITH GRANT OPTION;
