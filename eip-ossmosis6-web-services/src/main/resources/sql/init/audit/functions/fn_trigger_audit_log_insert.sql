CREATE OR REPLACE FUNCTION audit.fn_trigger_audit_log_insert()
    R<PERSON><PERSON><PERSON> trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF SECURITY DEFINER
AS
$BODY$
declare
    pkey_cols        text[] = audit.fn_get_primary_key_columns(TG_RELID);
    record_jsonb     jsonb  = to_jsonb(new);
    record_id        text   = audit.fn_to_record_id(pkey_cols, record_jsonb);
    old_record_jsonb jsonb  = to_jsonb(old);
    old_record_id    text   = audit.fn_to_record_id(pkey_cols, old_record_jsonb);
begin

    insert into audit.record_audit_log(record_id,
                                       old_record_id,
                                       op,
                                       table_oid,
                                       table_schema,
                                       table_name,
                                       record,
                                       old_record,
                                       request_audit_log_id)
    select record_id,
           old_record_id,
           TG_OP,
           TG_RELID,
           TG_TABLE_SCHEMA,
           TG_TABLE_NAME,
           record_jsonb,
           old_record_jsonb,
           (SELECT *
            FROM audit.fn_get_request_audit_log_id_by_request_id(audit.fn_get_audit_request_id()));

    return coalesce(new, old);
end;
$BODY$;

ALTER FUNCTION audit.fn_trigger_audit_log_insert()
    OWNER TO ossmosis6_owner;

GRANT EXECUTE ON FUNCTION audit.fn_trigger_audit_log_insert() TO PUBLIC;

GRANT EXECUTE ON FUNCTION audit.fn_trigger_audit_log_insert() TO ossmosis6_owner WITH GRANT OPTION;
