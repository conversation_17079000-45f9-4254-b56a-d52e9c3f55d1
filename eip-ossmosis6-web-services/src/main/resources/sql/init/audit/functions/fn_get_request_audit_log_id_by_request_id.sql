DROP FUNCTION IF EXISTS audit.fn_get_request_audit_log_id_by_request_id(uuid);

CREATE OR REPLACE FUNCTION audit.fn_get_request_audit_log_id_by_request_id(
    request_id uuid)
    RETURNS bigint
    LANGUAGE 'sql'
    COST 100
    STABLE PARALLEL UNSAFE
AS
$BODY$
select request_audit_log_id
from audit.request_audit_log rl
where rl.request_id = $1;
$BODY$;

ALTER FUNCTION audit.fn_get_request_audit_log_id_by_request_id(uuid)
    OWNER TO ossmosis6_owner;

GRANT EXECUTE ON FUNCTION audit.fn_get_request_audit_log_id_by_request_id(uuid) TO PUBLIC;

GRANT EXECUTE ON FUNCTION audit.fn_get_request_audit_log_id_by_request_id(uuid) TO ossmosis6_owner WITH GRANT OPTION;
