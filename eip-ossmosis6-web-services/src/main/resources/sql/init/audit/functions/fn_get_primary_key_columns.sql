DROP FUNCTION IF EXISTS audit.fn_get_primary_key_columns(oid);

CREATE OR REPLACE FUNCTION audit.fn_get_primary_key_columns(
    entity_oid oid)
    RETURNS text[]
    LANGUAGE 'sql'
    COST 100
    STABLE SECURITY DEFINER PARALLEL UNSAFE
AS
$BODY$
    -- Looks up the names of a table's primary key columns
select coalesce(
               array_agg(pa.attname::text order by pa.attnum),
               array []::text[]
           ) column_names
from pg_index pi
         join pg_attribute pa
              on pi.indrelid = pa.attrelid
                  and pa.attnum = any (pi.indkey)
where indrelid = $1
  and indisprimary
$BODY$;

ALTER FUNCTION audit.fn_get_primary_key_columns(oid)
    OWNER TO ossmosis6_owner;

GRANT EXECUTE ON FUNCTION audit.fn_get_primary_key_columns(oid) TO PUBLIC;

GRANT EXECUTE ON FUNCTION audit.fn_get_primary_key_columns(oid) TO ossmosis6_owner WITH GRANT OPTION;
