DROP FUNCTION IF EXISTS audit.fn_disable_auditing(regclass);

CREATE OR REPLACE FUNCTION audit.fn_disable_auditing(
    regclass)
    RETURNS void
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE SECURITY DEFINER PARALLEL UNSAFE
AS
$BODY$
declare
    statement_row text = format(
            'drop trigger if exists audit_i_u_d on %s;',
            $1
        );
begin
    execute statement_row;
end;
$BODY$;

ALTER FUNCTION audit.fn_disable_auditing(regclass)
    OWNER TO ossmosis6_owner;

GRANT EXECUTE ON FUNCTION audit.fn_disable_auditing(regclass) TO PUBLIC;

GRANT EXECUTE ON FUNCTION audit.fn_disable_auditing(regclass) TO ossmosis6_owner WITH GRANT OPTION;
