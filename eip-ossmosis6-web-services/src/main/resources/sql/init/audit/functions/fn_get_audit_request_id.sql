DROP FUNCTION IF EXISTS audit.fn_get_audit_request_id();

CREATE OR REPLACE FUNCTION audit.fn_get_audit_request_id()
    RETURNS uuid
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE SECURITY DEFINER PARALLEL UNSAFE
AS
$BODY$
DECLARE
    default_audit_request_id text = '00000000-0000-0000-0000-000000000000';
BEGIN
    return ((SELECT current_setting('audit.request_id'))::uuid);
EXCEPTION
    when undefined_object then
        return (default_audit_request_id::uuid);
END
$BODY$;

ALTER FUNCTION audit.fn_get_audit_request_id()
    OWNER TO ossmosis6_owner;

GRANT EXECUTE ON FUNCTION audit.fn_get_audit_request_id() TO PUBLIC;

GRANT EXECUTE ON FUNCTION audit.fn_get_audit_request_id() TO ossmosis6_owner WITH GRANT OPTION;
