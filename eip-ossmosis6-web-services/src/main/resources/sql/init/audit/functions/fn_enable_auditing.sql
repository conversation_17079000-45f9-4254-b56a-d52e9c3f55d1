DROP FUNCTION IF EXISTS audit.fn_enable_auditing(regclass);

CREATE OR REPLACE FUNCTION audit.fn_enable_auditing(
    regclass)
    RETURNS void
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE SECURITY DEFINER PARALLEL UNSAFE
AS
$BODY$
declare
    statement_row       text              = format('
        create trigger audit_i_u_d
            before insert or update or delete
            on %s
            for each row
            execute procedure audit.fn_trigger_audit_log_insert();',
                                                   $1
        );
    pkey_cols           text[]            = audit.fn_get_primary_key_columns($1);
begin
    if pkey_cols = array []::text[] then
        raise exception 'Table % can not be audited because it has no primary key', $1;
    end if;

    if not exists(select 1 from pg_trigger where tgrelid = $1 and tgname = 'audit_i_u_d') then
        execute statement_row;
    end if;
end;
$BODY$;

ALTER FUNCTION audit.fn_enable_auditing(regclass)
    OWNER TO ossmosis6_owner;

GRANT EXECUTE ON FUNCTION audit.fn_enable_auditing(regclass) TO PUBLIC;

GRANT EXECUTE ON FUNCTION audit.fn_enable_auditing(regclass) TO ossmosis6_owner WITH GRANT OPTION;
