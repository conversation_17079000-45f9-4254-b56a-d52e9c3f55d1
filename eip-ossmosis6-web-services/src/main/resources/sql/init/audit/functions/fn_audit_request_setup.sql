DROP FUNCTION IF EXISTS audit.fn_audit_request_setup(uuid);

CREATE OR REPLACE FUNCTION audit.fn_audit_request_setup(
    uuid)
    RETURNS void
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE SECURITY DEFINER PARALLEL UNSAFE
AS
$BODY$
BEGIN
    IF $1 is null then
        raise exception 'Request cannot be audited';
    END if;

    execute 'SET SESSION "audit.request_id" = ' || quote_nullable(CAST($1 as text));
END
$BODY$;

ALTER FUNCTION audit.fn_audit_request_setup(uuid)
    OWNER TO ossmosis6_owner;

GRANT EXECUTE ON FUNCTION audit.fn_audit_request_setup(uuid) TO PUBLIC;

GRANT EXECUTE ON FUNCTION audit.fn_audit_request_setup(uuid) TO ossmosis6_owner WITH GRANT OPTION;
