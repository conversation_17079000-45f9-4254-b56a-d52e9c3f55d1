DROP FUNCTION IF EXISTS partnerprovider.fn_auth_get_enterprise_access_list_for_login(bigint);

CREATE OR REPLACE FUNCTION partnerprovider.fn_auth_get_enterprise_access_list_for_login(
    "loginId" bigint)
    RETURNS SETOF partnerprovider.enterprise
    LANGUAGE plpgsql
AS
$BODY$
BEGIN
    RETURN QUERY SELECT e2.*
                 from partnerprovider.enterprise e
                          join partnerprovider.login l
                               on e.enterprise_id = l.login_primary_enterprise_id
                          join partnerprovider.enterprise_tree_closure etc
                               on etc.ancestor_enterprise_id = e.enterprise_id
                          join partnerprovider.enterprise e2
                               on etc.descendant_enterprise_id = e2.enterprise_id
                 where l.login_id = $1
                 UNION
                 SELECT e.*
                 from partnerprovider.enterprise e
                          join partnerprovider.login l
                               on e.enterprise_id = l.login_primary_enterprise_id
                 where l.login_id = $1;
END
$BODY$;

ALTER FUNCTION partnerprovider.fn_auth_get_enterprise_access_list_for_login(bigint)
    OWNER TO ossmosis6_owner;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_auth_get_enterprise_access_list_for_login
    (bigint) TO PUBLIC;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_auth_get_enterprise_access_list_for_login
    (bigint) TO ossmosis6_owner WITH GRANT OPTION;

