DROP FUNCTION IF EXISTS partnerprovider.fn_roles_create(character varying);

CREATE OR REPLACE FUNCTION partnerprovider.fn_roles_create(
    "rolesJson" character varying)
    RETURNS SETOF partnerprovider.role
    LANGUAGE plpgsql
    COST 100
    VOLATILE PARALLEL UNSAFE
AS
$BODY$
DECLARE
    duplicateRoles         character varying = '';
    duplicateRolesIncoming character varying = '';
    invalidIds             int;
    errorMessage           character varying = '';
BEGIN

    with rj as (SELECT *
                FROM json_to_recordset($1::json) as x("roleId" bigint,
                                                      "roleName" character varying(100))),
         duplicateRolesQuery as (select string_agg(ro.role_name, ', ')
                                 from partnerprovider.role ro
                                          JOIN rj on ro.role_name = rj."roleName"),
         duplicateRolesIncomingQuery as (SELECT string_agg(counts."roleName", ', ')
                                         FROM (select rj."roleName",
                                                      COUNT(*) as NameCount
                                               from rj
                                               group by rj."roleName") as counts
                                         where counts.NameCount > 1),
         invalidIdsQuery as (SELECT COUNT(*) FROM rj where "roleId" > 0)

    SELECT drq.*, ii.*, dei.*
    into duplicateRoles, invalidIds, duplicateRolesIncoming
    FROM duplicateRolesQuery drq,
         invalidIdsQuery ii,
         duplicateRolesIncomingQuery dei;

    IF LENGTH(duplicateRoles) > 0 THEN
        errorMessage = errorMessage || 'Duplicate role(s): ' || duplicateRoles || '. ';
    END IF;

    IF LENGTH(duplicateRolesIncoming) > 0 THEN
        errorMessage = errorMessage || 'Duplicate role(s) in request: ' ||
                       duplicateRolesIncoming || '. ';
    END IF;

    IF invalidIds > 0 THEN
        errorMessage = errorMessage || 'Role ids must be less then one. ';
    END IF;


    IF length(errorMessage) > 0 THEN
        RAISE QUERY_CANCELED USING MESSAGE = errorMessage;
    END IF;

    RETURN QUERY
        with rj as (SELECT *
                    FROM json_to_recordset($1::json) as x("roleID" bigint,
                                                          "roleName" character varying(100),
                                                          "enterpriseId" character varying
                        ))
            INSERT INTO partnerprovider.role
                (role_name, enterprise_id, date_created, date_updated)
                SELECT rj."roleName",
                       rj."enterpriseId",
                       CURRENT_TIMESTAMP,
                       CURRENT_TIMESTAMP
                FROM rj
                RETURNING *;

END
$BODY$;

ALTER FUNCTION partnerprovider.fn_roles_create(character varying)
    OWNER TO ossmosis6_owner;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_roles_create(character varying) TO PUBLIC;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_roles_create(character varying) TO ossmosis6_owner WITH GRANT OPTION;

