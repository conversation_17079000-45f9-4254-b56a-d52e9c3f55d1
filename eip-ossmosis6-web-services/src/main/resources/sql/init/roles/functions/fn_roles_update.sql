DROP FUNCTION IF EXISTS partnerprovider.fn_roles_update(character varying);

CREATE OR REPLACE FUNCTION partnerprovider.fn_roles_update(
    "rolesJson" character varying)
    RETURNS SETOF partnerprovider.role
    LANGUAGE plpgsql
    COST 100
    VOLATILE PARALLEL UNSAFE
AS
$BODY$
DECLARE
    duplicateRoles         character varying = '';
    duplicateRolesIncoming character varying = '';
    errorMessage           character varying = '';
BEGIN

    with rj as (SELECT *
                FROM json_to_recordset($1::json) as x("roleId" bigint,
                                                      "roleName" character varying(100))),
         duplicateRolesQuery as (select string_agg(ro.role_name, ', ')
                                 from partnerprovider.role ro
                                          JOIN rj
                                               on ro.role_name = rj."roleName" and ro.role_id <> rj."roleId"),
         duplicateRolesIncomingQuery as (SELECT string_agg(counts."roleName", ', ')
                                         FROM (select rj."roleName",
                                                      COUNT(*) as NameCount
                                               from rj
                                               group by rj."roleName") as counts
                                         where counts.NameCount > 1)

    SELECT drq.*, dei.*
    into duplicateRoles, duplicateRolesIncoming
    FROM duplicateRolesQuery drq,
         duplicateRolesIncomingQuery dei;

    IF LENGTH(duplicateRoles) > 0 THEN
        errorMessage = errorMessage || 'Duplicate role(s): ' || duplicateRoles || '. ';
    END IF;

    IF LENGTH(duplicateRolesIncoming) > 0 THEN
        errorMessage = errorMessage || 'Duplicate role(s) in request: ' ||
                       duplicateRolesIncoming || '. ';
    END IF;

    IF length(errorMessage) > 0 THEN
        RAISE QUERY_CANCELED USING MESSAGE = errorMessage;
    END IF;

    RETURN QUERY
        with rj as (SELECT *
                    FROM json_to_recordset($1::json) as x("roleId" bigint,
                                                          "roleName" character varying(100)
                        ))
            UPDATE partnerprovider.role ro
                set role_name = rj."roleName",
                    date_updated = CURRENT_TIMESTAMP
                FROM rj where ro.role_id = rj."roleId"
                RETURNING ro.*;

END
$BODY$;

ALTER FUNCTION partnerprovider.fn_roles_update(character varying)
    OWNER TO ossmosis6_owner;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_roles_update(character varying) TO PUBLIC;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_roles_update(character varying) TO ossmosis6_owner WITH GRANT OPTION;

