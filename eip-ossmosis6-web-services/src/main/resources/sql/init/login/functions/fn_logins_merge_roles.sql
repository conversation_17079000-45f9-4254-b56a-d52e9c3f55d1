DROP FUNCTION IF EXISTS partnerprovider.fn_logins_merge_roles(bigint, character varying);

CREATE OR REPLACE FUNCTION partnerprovider.fn_logins_merge_roles(
    "loginId" bigint,
    "roleJson" character varying)
    RETURNS boolean
    LANGUAGE plpgsql
    COST 100
    VOLATILE PARALLEL UNSAFE
AS
$BODY$
DECLARE
    rolesFound         bigint;
    loginIdFound       bigint;
    incomingRolesFound bigint;
    errorMessage       character varying = '';
BEGIN

    with pJson as (SELECT *
                   FROM json_to_recordset($2::json) as x("roleId" bigint)),
         rolesFoundQuery as (select COUNT(*)
                             from partnerprovider.role r
                                      JOIN pJson on r.role_id = pJson."roleId"),
         incomingRolesFoundQuery as (SELECT COUNT(*) FROM pJson),
         incomingLoginQuery as (select login_id from partnerprovider.login where login_id = $1)

    SELECT rfq.*, ilq.*, irf.*
    into rolesFound, loginIdFound, incomingRolesFound
    FROM rolesFoundQuery rfq,
         incomingLoginQuery ilq,
         incomingRolesFoundQuery irf;


    IF loginIdFound <> $1 THEN
        errorMessage = 'Invalid loginId. ';
    END IF;

    IF rolesFound <> incomingRolesFound THEN
        errorMessage = errorMessage || 'Invalid roleId(s). ';
    END IF;

    IF length(errorMessage) > 0 THEN
        RAISE QUERY_CANCELED USING MESSAGE = errorMessage;
    END IF;


    with pJson as (SELECT *
                   FROM json_to_recordset($2::json) as x("roleId" bigint))
    INSERT
    INTO partnerprovider.login_role (login_id, role_id, date_created, date_updated)
    SELECT $1, pJson."roleId", CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    from pJson
    ON CONFLICT DO NOTHING;

    with pJson as (SELECT *
                   FROM json_to_recordset($2::json) as x("roleId" bigint))
    DELETE
    FROM partnerprovider.login_role
    WHERE login_id = $1
      AND NOT EXISTS (SELECT 1
                      FROM pJson
                      WHERE $1 = partnerprovider.login_role.login_id
                        AND pJson."roleId" =
                            partnerprovider.login_role.role_id);

    return true;
END
$BODY$;

ALTER FUNCTION partnerprovider.fn_logins_merge_roles(bigint, character varying)
    OWNER TO ossmosis6_owner;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_logins_merge_roles(bigint, character varying) TO PUBLIC;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_logins_merge_roles(bigint, character varying) TO ossmosis6_owner WITH GRANT OPTION;

