DROP FUNCTION IF EXISTS partnerprovider.fn_logins_merge_permissions(bigint, character varying);

CREATE OR REPLACE FUNCTION partnerprovider.fn_logins_merge_permissions(
    "loginId" bigint,
    "permissionJson" character varying)
    RETURNS boolean
    LANGUAGE plpgsql
    COST 100
    VOLATILE PARALLEL UNSAFE
AS
$BODY$
DECLARE
    permissionsFound                 bigint;
    resourcesFound                   bigint;
    loginIdFound                     bigint;
    incomingResourcePermissionsFound bigint;
    errorMessage                     character varying = '';
BEGIN

    with pJson as (SELECT *
                   FROM json_to_recordset($2::json) as x("permissionId" bigint,
                                                         "resourceId" bigint)),
         permissionFoundQuery as (select COUNT(*)
                                  from partnerprovider.resource_permission rp
                                           JOIN pJson on rp.permission_id = pJson."permissionId" and
                                                         rp.resource_id = pJson."resourceId"),
         resourcesFoundQuery as (select COUNT(*)
                                 from partnerprovider.resource ro
                                          JOIN pJson on ro.resource_id = pJson."resourceId"),
         incomingResourcePermissionsFoundQuery as (SELECT COUNT(*) FROM pJson),
         incomingLoginQuery as (select login_id from partnerprovider.login where login_id = $1)

    SELECT iPQ.*, rfq.*, ilq.*, irpfq.*
    into permissionsFound, resourcesFound, loginIdFound, incomingResourcePermissionsFound
    FROM permissionFoundQuery iPQ,
         resourcesFoundQuery rfq,
         incomingLoginQuery ilq,
         incomingResourcePermissionsFoundQuery irpfq;


    IF loginIdFound <> $1 THEN
        errorMessage = 'Invalid loginId. ';
    END IF;

    IF permissionsFound <> incomingResourcePermissionsFound THEN
        errorMessage = errorMessage || 'Invalid permissionId(s). ';
    END IF;

    IF resourcesFound <> incomingResourcePermissionsFound THEN
        errorMessage = errorMessage || 'Invalid resourceId(s). ';
    END IF;

    IF length(errorMessage) > 0 THEN
        RAISE QUERY_CANCELED USING MESSAGE = errorMessage;
    END IF;


    with pJson as (SELECT *
                   FROM json_to_recordset($2::json) as x("permissionId" bigint,
                                                         "resourceId" bigint))
    INSERT
    INTO partnerprovider.login_resource_permission (login_id, resource_id, permission_id)
    SELECT $1, pJson."resourceId", pJson."permissionId"
    from pJson
    ON CONFLICT DO NOTHING;

    with pJson as (SELECT *
                   FROM json_to_recordset($2::json) as x("permissionId" bigint,
                                                         "resourceId" bigint))
    DELETE
    FROM partnerprovider.login_resource_permission
    WHERE login_id = $1
      AND NOT EXISTS (SELECT 1
                      FROM pJson
                      WHERE $1 = partnerprovider.login_resource_permission.login_id
                        AND pJson."resourceId" =
                            partnerprovider.login_resource_permission.resource_id
                        AND pJson."permissionId" =
                            partnerprovider.login_resource_permission.permission_id);

    return true;
END
$BODY$;

ALTER FUNCTION partnerprovider.fn_logins_merge_permissions(bigint, character varying)
    OWNER TO ossmosis6_owner;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_logins_merge_permissions(bigint, character varying) TO PUBLIC;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_logins_merge_permissions(bigint, character varying) TO ossmosis6_owner WITH GRANT OPTION;

