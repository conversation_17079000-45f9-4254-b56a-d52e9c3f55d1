DROP FUNCTION IF EXISTS partnerprovider.fn_logins_update(character varying, bigint);

CREATE OR REPLACE FUNCTION partnerprovider.fn_logins_update(
    "loginJson" character varying,
    "userId" bigint)
    RETURNS SETOF partnerprovider.login
    LANGUAGE plpgsql
    COST 100
    VOLATILE PARALLEL UNSAFE
AS
$BODY$
DECLARE
    duplicateEmails         character varying = '';
    duplicateEmailsIncoming character varying = '';
    totalIds                int;
    totalIdsFound           int;
    invalidEnterprises      character varying = '';
    errorMessage            character varying = '';
BEGIN

    with lj as (SELECT *
                FROM json_to_recordset($1::json) as x("loginId" bigint,
                                                      "loginEmail" character varying(100),
                                                      "loginPrimaryEnterpriseId" character varying(25))),
         allowedEnterpriseList as (select *
                                   from partnerprovider.fn_auth_get_enterprise_access_list_for_login($2)),
         duplicateEmailsQuery as (select string_agg(lo.login_email, ', ')
                                  from partnerprovider.login lo
                                           JOIN lj on lo.login_email = lj."loginEmail"
                                      and lo.login_id <> lj."loginId"),
         duplicateEmailsIncomingQuery as (SELECT string_agg(counts."loginEmail", ', ')
                                          FROM (select lj."loginEmail",
                                                       COUNT(*) as EmailCount
                                                from lj
                                                group by lj."loginEmail") as counts
                                          where counts.EmailCount > 1),
         totalIdsQuery as (SELECT COUNT(*) FROM lj where "loginId" > 0),
         totalIdsFoundQuery as (SELECT COUNT(*)
                                FROM partnerprovider.login l
                                         join lj on lj."loginId" = l.login_id
                                         join allowedEnterpriseList ael
                                              on ael.enterprise_id = l.login_primary_enterprise_id),
         invalidEnterprisesQuery as (SELECT string_agg(lj."loginPrimaryEnterpriseId", ', ')
                                     FROM lj
                                     where lj."loginPrimaryEnterpriseId" not in
                                           (SELECT DISTINCT enterprise_id
                                            from allowedEnterpriseList))
    SELECT de.*, ti.*, ie.*, tif.*, dei.*
    into duplicateEmails, totalIds, invalidEnterprises, totalIdsFound, duplicateEmailsIncoming
    FROM duplicateEmailsQuery de,
         totalIdsQuery ti,
         invalidEnterprisesQuery ie,
         totalIdsFoundQuery tif,
         duplicateEmailsIncomingQuery dei;

    IF LENGTH(duplicateEmails) > 0 THEN
        errorMessage = errorMessage || 'Duplicate email(s): ' || duplicateEmails || '. ';
    END IF;

    IF LENGTH(duplicateEmailsIncoming) > 0 THEN
        errorMessage = errorMessage || 'Duplicate email(s) in request: ' ||
                       duplicateEmailsIncoming || '. ';
    END IF;

    IF totalIds <> totalIdsFound THEN
        errorMessage = errorMessage || 'Invalid loginId(s). ';
    END IF;

    IF LENGTH(invalidEnterprises) > 0 THEN
        errorMessage = errorMessage || 'Invalid enterprise(s): ' || invalidEnterprises || '. ';
    END IF;


    IF length(errorMessage) > 0 THEN
        RAISE QUERY_CANCELED USING MESSAGE = errorMessage;
    END IF;

    RETURN QUERY
        with lj as (SELECT *
                    FROM json_to_recordset($1::json) as x("loginId" bigint,
                                                          "loginEmail" character varying(100),
                                                          "loginNameFirst" character varying(100),
                                                          "loginNameLast" character varying(100),
                                                          "active" boolean, "locked" boolean,
                                                          "loginGroup" character varying(30),
                                                          "loginPhoneNumber" character varying(16),
                                                          "loginPrimaryEnterpriseId" character varying(25)))
            UPDATE partnerprovider.login lo
                SET login_email = lj."loginEmail", login_name_first = lj."loginNameFirst",
                    login_name_last = lj."loginNameLast", active = lj."active",
                    locked = lj."locked",
                    login_group = lj."loginGroup", login_phone_number = lj."loginPhoneNumber",
                    login_primary_enterprise_id = lj."loginPrimaryEnterpriseId",
                    date_updated = CURRENT_TIMESTAMP
                FROM lj
                where lo.login_id = lj."loginId"
                RETURNING lo.*;
END
$BODY$;

ALTER FUNCTION partnerprovider.fn_logins_update(character varying, bigint)
    OWNER TO ossmosis6_owner;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_logins_update(character varying, bigint) TO PUBLIC;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_logins_update(character varying, bigint) TO ossmosis6_owner WITH GRANT OPTION;

