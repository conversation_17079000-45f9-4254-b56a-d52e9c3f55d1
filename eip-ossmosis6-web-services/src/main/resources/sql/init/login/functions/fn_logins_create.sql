DROP FUNCTION IF EXISTS partnerprovider.fn_logins_create(character varying, bigint);

CREATE OR REPLACE FUNCTION partnerprovider.fn_logins_create(
    "loginJson" character varying,
    "userId" bigint)
    RETURNS SETOF partnerprovider.login
    LANGUAGE plpgsql
    COST 100
    VOLATILE PARALLEL UNSAFE
AS
$BODY$
DECLARE
    duplicateEmails         character varying = '';
    duplicateEmailsIncoming character varying = '';
    invalidIds              int;
    invalidEnterprises      character varying = '';
    errorMessage            character varying = '';
BEGIN

    with lj as (SELECT *
                FROM json_to_recordset($1::json) as x("loginId" bigint,
                                                      "loginEmail" character varying(100),
                                                      "loginPrimaryEnterpriseId" character varying(25),
                                                      "roleId" integer)),
         allowedEnterpriseList as (select *
                                   from partnerprovider.fn_auth_get_enterprise_access_list_for_login($2)),
         duplicateEmailsQuery as (select string_agg(lo.login_email, ', ')
                                  from partnerprovider.login lo
                                           JOIN lj on lo.login_email = lj."loginEmail"),
         duplicateEmailsIncomingQuery as (SELECT string_agg(counts."loginEmail", ', ')
                                          FROM (select lj."loginEmail",
                                                       COUNT(*) as EmailCount
                                                from lj
                                                group by lj."loginEmail") as counts
                                          where counts.EmailCount > 1),
         invalidIdsQuery as (SELECT COUNT(*) FROM lj where "loginId" > 0),
         invalidEnterprisesQuery as (SELECT string_agg(lj."loginPrimaryEnterpriseId", ', ')
                                     FROM lj
                                     where lj."loginPrimaryEnterpriseId" not in
                                           (SELECT DISTINCT enterprise_id
                                            from allowedEnterpriseList))
    SELECT de.*, ii.*, ie.*, dei.*
    into duplicateEmails, invalidIds, invalidEnterprises, duplicateEmailsIncoming
    FROM duplicateEmailsQuery de,
         invalidIdsQuery ii,
         invalidEnterprisesQuery ie,
         duplicateEmailsIncomingQuery dei;

    IF LENGTH(duplicateEmails) > 0 THEN
        errorMessage = errorMessage || 'Duplicate email(s): ' || duplicateEmails || '. ';
    END IF;

    IF LENGTH(duplicateEmailsIncoming) > 0 THEN
        errorMessage = errorMessage || 'Duplicate email(s) in request: ' ||
                       duplicateEmailsIncoming || '. ';
    END IF;

    IF invalidIds > 0 THEN
        errorMessage = errorMessage || 'Login ids must be less then one. ';
    END IF;

    IF LENGTH(invalidEnterprises) > 0 THEN
        errorMessage = errorMessage || 'Invalid enterprise(s): ' || invalidEnterprises || '. ';
    END IF;


    IF length(errorMessage) > 0 THEN
        RAISE QUERY_CANCELED USING MESSAGE = errorMessage;
    END IF;

    RETURN QUERY
        with lj as (SELECT *
                    FROM json_to_recordset($1::json) as x("loginId" bigint,
                                                          "loginEmail" character varying(100),
                                                          "loginNameFirst" character varying(100),
                                                          "loginNameLast" character varying(100),
                                                          "active" boolean, "locked" boolean,
                                                          "loginGroup" character varying(30),
                                                          "loginPhoneNumber" character varying(16),
                                                          "loginPrimaryEnterpriseId" character varying(25)))
            INSERT INTO partnerprovider.login
                (login_email, login_name_first, login_name_last, active, locked,
                 login_group, login_phone_number,
                 login_primary_enterprise_id,
                 date_created, date_updated)
                SELECT lj."loginEmail",
                       lj."loginNameFirst",
                       lj."loginNameLast",
                       lj."active",
                       lj."locked",
                       lj."loginGroup",
                       lj."loginPhoneNumber",
                       lj."loginPrimaryEnterpriseId",
                       CURRENT_TIMESTAMP,
                       CURRENT_TIMESTAMP
                FROM lj
                RETURNING *;

END
$BODY$;

ALTER FUNCTION partnerprovider.fn_logins_create(character varying, bigint)
    OWNER TO ossmosis6_owner;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_logins_create(character varying, bigint) TO PUBLIC;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_logins_create(character varying, bigint) TO ossmosis6_owner WITH GRANT OPTION;

