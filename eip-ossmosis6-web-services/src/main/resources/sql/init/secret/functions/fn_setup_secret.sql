DROP FUNCTION IF EXISTS partnerprovider.fn_setup_secret(text, text);

CREATE OR REPLACE FUNCTION partnerprovider.fn_setup_secret(
    text, text)
    RETURNS void
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE SECURITY DEFINER PARALLEL UNSAFE
AS
$BODY$
BEGIN
    IF $1 is null OR $2 is null then
        raise exception 'Error setting variable';
    END if;

    execute 'SET LOCAL ' || $1 || ' = ''' || $2 || '''';
END
$BODY$;

ALTER FUNCTION partnerprovider.fn_setup_secret(text, text)
    OWNER TO ossmosis6_owner;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_setup_secret(text, text) TO PUBLIC;

GRANT EXECUTE ON FUNCTION partnerprovider.fn_setup_secret(text, text) TO ossmosis6_owner WITH GRANT OPTION;
