\c ossmosis6_repo;
CREATE SCHEMA IF NOT EXISTS partnerprovider
    AUTHORIZATION ossmosis6_owner;

GRANT
    ALL
    ON SCHEMA partnerprovider TO ossmosis6_owner WITH GRANT OPTION;

ALTER
    DEFAULT PRIVILEGES FOR ROLE ossmosis6_owner IN SCHEMA partnerprovider
    GRANT ALL ON TABLES TO ossmosis6_owner WITH GRANT OPTION;

ALTER
    DEFAULT PRIVILEGES FOR ROLE ossmosis6_owner IN SCHEMA partnerprovider
    GRANT ALL ON SEQUENCES TO ossmosis6_owner WITH GRANT OPTION;

ALTER
    DEFAULT PRIVILEGES FOR ROLE ossmosis6_owner IN SCHEMA partnerprovider
    GRANT EXECUTE ON FUNCTIONS TO ossmosis6_owner WITH GRANT OPTION;

ALTER
    DEFAULT PRIVILEGES FOR ROLE ossmosis6_owner IN SCHEMA partnerprovider
    GRANT USAGE ON TYPES TO ossmosis6_owner WITH GRANT OPTION;

ALTER
    DEFAULT PRIVILEGES FOR ROLE ossmosis6_owner IN SCHEMA partnerprovider
    GRANT TRIGGER ON TABLES TO ossmosis6_owner WITH GRANT OPTION;

CREATE SCHEMA IF NOT EXISTS audit
    AUTHORIZATION ossmosis6_owner;

GRANT
    ALL
    ON SCHEMA audit TO ossmosis6_owner WITH GRANT OPTION;

ALTER
    DEFAULT PRIVILEGES FOR ROLE ossmosis6_owner IN SCHEMA audit
    GRANT ALL ON TABLES TO ossmosis6_owner WITH GRANT OPTION;

ALTER
    DEFAULT PRIVILEGES FOR ROLE ossmosis6_owner IN SCHEMA audit
    GRANT ALL ON SEQUENCES TO ossmosis6_owner WITH GRANT OPTION;

ALTER
    DEFAULT PRIVILEGES FOR ROLE ossmosis6_owner IN SCHEMA audit
    GRANT EXECUTE ON FUNCTIONS TO ossmosis6_owner WITH GRANT OPTION;

ALTER
    DEFAULT PRIVILEGES FOR ROLE ossmosis6_owner IN SCHEMA audit
    GRANT USAGE ON TYPES TO ossmosis6_owner WITH GRANT OPTION;

ALTER
    DEFAULT PRIVILEGES FOR ROLE ossmosis6_owner IN SCHEMA audit
    GRANT TRIGGER ON TABLES TO ossmosis6_owner WITH GRANT OPTION;