# Spring General
spring.config.import=optional:file:.env.properties
spring.profiles.active=${ENVIRONMENT:local}
server.port=${SERVER_PORT:8080}
server.servlet.context-path=/ossmosis6/api/v1
spring.application.name=ossmosis6
spring.cors.allowed-headers=Authorization,Content-Type
spring.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.main.allow-circular-references=false
spring.jpa.hibernate.naming.implicit-strategy=net.evolveip.ossmosis.api.config.dao.OssmosisImplicitNamingStrategy
spring.cors.max-age=${CORS_MAX_AGE:3600}
spring.cors.allowed-origins=${CORS_ALLOWED_ORIGINS:http://localhost:3000}
# Spring Docs
springdoc.default-produces-media-type=application/json
springdoc.default-consumes-media-type=application/json
ossmosis.springdoc.allowed-paths=/v3/api-docs/**,/swagger-ui/*
# Ossmosis General
ossmosis.logging.requestfilter.logLengthLimit=1024
ossmosis.logging.audit-methods=POST,PUT,DELETE
# Logging
logging.level.root=${LOG_LEVEL:INFO}
logging.level.org.springframework.web=${LOG_LEVEL:INFO}
logging.level.org.hibernate=error
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} %clr(%X{ossRequestUUID}){blue} %clr(%-5level) %magenta([%thread]) %logger.%M - %msg%n
# Spring Security
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://{tenant}.${OAUTH2_JWT_URI_DOMAIN}/apps/pkce/{app_name}/jwks.json
# Datasource Pool
spring.datasource.hikari.maximum-pool-size=${HIKARI_MAX_POOL_SIZE:100}
spring.datasource.hikari.minimum-idle=${HIKARI_MIN_IDLE:50}
spring.datasource.hikari.connectionTimeout=${HIKARI_CONNECTION_TIMEOUT:30000}
spring.datasource.hikari.idleTimeout=${HIKARI_IDLE_TIMEOUT:600000}
spring.datasource.hikari.maxLifetime=${HIKARI_MAX_LIFETIME:1800000}
# PartnerProv DataSource
spring.datasource.url=jdbc:postgresql://${DBHOST}:${DBPORT:5432}/${DBSCHEMA:ossmosis6_repo}
spring.datasource.username=${DBUSER}
spring.datasource.password=${DBPASS}
spring.datasource.column.encryption.key=${DBENCSECRET}
# Snowflake Data Source
spring.snowflake.datasource.url=jdbc:snowflake://${SNOW_HOST}/?warehouse=${SNOW_WAREHOUSE:COMPUTE_WH}&db=${SNOW_DB:EVOLVEIP}&schema=${SNOW_SCHEMA:PUBLIC}&TIMEZONE=${SNOW_TIMEZONE:UTC}&CLIENT_RESULT_COLUMN_CASE_INSENSITIVE=${SNOW_CLIENT_RESULT_COLUMN_CASE_INSENSITIVE:true}&CLIENT_TIMESTAMP_TYPE_MAPPING=${SNOW_CLIENT_TIMESTAMP_TYPE_MAPPING:TIMESTAMP_NTZ}
spring.snowflake.datasource.username=${SNOW_USER}
spring.snowflake.datasource.password=${SNOW_PASS}
spring.snowflake.datasource.driver-class-name=net.snowflake.client.jdbc.SnowflakeDriver
spring.snowflake.jpa.properties.hibernate.dialect=net.evolveip.ossmosis.api.config.dao.dialects.EmptyDialect
# JPA Properties
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=${JPA_LOB_NON_CONTEXTUAL_CREATION:true}
spring.jpa.properties.hibernate.default_schema=${JPA_DEFAULT_SCHEMA:partnerprovider}
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=${JPA_DDL_AUTO:none}
spring.jpa.open-in-view=${JPA_OPEN_IN_VIEW:true}
spring.jpa.show-sql=${JPA_SHOW_SQL:true}
# CSV Temporary Location:
spring.application.csv.tempdir=${CSVTEMP:/tmp}
# Quartz Scheduler properties
# do NOT set up a separate datasource, use the spring one:
spring.quartz.scheduler-name=${QUARTZ_SCHEDULER_NAME:Ossmosis6ClusteredQuartzScheduler}
spring.quartz.job-store-type=jdbc
# two possible values: "always" | "never"
# "always" should only be used when executing first-time deployments:
spring.quartz.jdbc.initialize-schema=${QUARTZ_INIT_SCHEMA:never}
spring.quartz.jdbc.schema=classpath:db/quartz_scheduler/001-initial-schema.sql
spring.quartz.properties.org.quartz.jobStore.tablePrefix=${QUARTZ_TABLE_PREFIX:quartz_scheduler.qrtz_}
spring.quartz.properties.org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
spring.quartz.properties.org.quartz.jobStore.isClustered=${QUARTZ_IS_CLUSTERED:true}
spring.quartz.properties.org.quartz.jobStore.clusterCheckinInterval=${QUARTZ_CLUSTER_CHECKIN_INTERVAL:1000}
spring.quartz.properties.org.quartz.scheduler.instanceName=${QUARTZ_SCHEDULER_INSTANCE_NAME:Ossmosis6Scheduler}
spring.quartz.properties.org.quartz.scheduler.instanceId=${QUARTZ_SCHEDULER_INSTANCE_ID:auto}
spring.quartz.properties.org.quartz.scheduler.makeSchedulerThreadDaemon=${QUARTZ_MAKE_SCHEDULER_THREAD_DAEMON:true}
spring.quartz.properties.org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool
spring.quartz.properties.org.quartz.threadPool.makeThreadsDaemons=${QUARTZ_MAKE_THREADS_DAEMONS:false}
spring.quartz.properties.org.quartz.threadPool.threadCount=${QUARTZ_THREAD_COUNT:5}
spring.quartz.properties.org.quartz.threadPool.threadPriority=${QUARTZ_THREAD_PRIORITY:5}
spring.quartz.properties.org.quartz.plugin.triggHistory.class=org.quartz.plugins.history.LoggingTriggerHistoryPlugin
spring.quartz.properties.org.quartz.plugin.triggHistory.triggerFiredMessage=Trigger \{1\}.\{0\} fired job \{6\}.\{5\} at: \{4, date, yyyy-MM-dd HH:mm:ss Z\}
spring.quartz.properties.org.quartz.plugin.triggHistory.triggerCompleteMessage=Trigger \{1\}.\{0\} completed firing job \{6\}.\{5\} at \{4, date, yyyy-MM-dd HH:mm:ss Z\}.
spring.quartz.properties.org.quartz.plugin.shutdownhook.class=org.quartz.plugins.management.ShutdownHookPlugin
spring.quartz.properties.org.quartz.plugin.shutdownhook.cleanShutdown=${QUARTZ_CLEAN_SHUTDOWN:true}
spring.quartz.properties.org.quartz.scheduler.skipUpdateCheck=${QUARTZ_SKIP_UPDATE_CHECK:true}
# Spring Email:
spring.mail.host=${RELAY_HOST}
spring.mail.port=${RELAY_PORT:25}
spring.mail.properties.mail.smtp.auth=${RELAY_AUTH:false}
spring.mail.properties.mail.smtp.starttls.enable=${RELAY_STARTTLS_ENABLE:false}
spring.mail.properties.mail.smtp.connectiontimeout=${RELAY_CONNECTION_TIMEOUT:10000}
spring.mail.properties.mail.smtp.timeout=${RELAY_TIMEOUT:10000}
spring.mail.properties.mail.smtp.writetimeout=${RELAY_WRITETIMEOUT:10000}

# Elastic APM Configuration:
elastic.apm.applicationPackages=${ELASTIC_APM_APP_PACKAGES:net.evolveip.ossmosis.api.EipOssmosisApiApplication}
elastic.apm.captureBody=${ELASTIC_APM_CAPTURE_BODY:true}
elastic.apm.enableHttpTracing=${ELASTIC_APM_HTTP_TRACING:true}
elastic.apm.environment=${ENVIRONMENT:local}
elastic.apm.logLevel=${LOG_LEVEL:INFO}
elastic.apm.logReformatting=${LOG_REFORMATTING:OFF}
elastic.apm.secretToken=${ELASTIC_APM_SECRET_TOKEN}
elastic.apm.serverUrl=${ELASTIC_APM_URL}
elastic.apm.serviceName=${ELASTIC_APM_SERVICE_NAME:eip-ossmosis6-web-services}
elastic.apm.setupDelay=${ELASTIC_APM_SETUP_DELAY:10}

# Temporal Configuration:
temporal.host=${TEMPORAL_HOST}
temporal.port=${TEMPORAL_PORT}
temporal.namespace=${TEMPORAL_NAMESPACE:default}
temporal.api.key=${TEMPORAL_API_KEY}
temporal.default.retry-max-attempts=${TEMPORAL_DEFAULT_RETRY_MAX_ATTEMPTS:10}
temporal.default.start-to-close-timeout=${TEMPORAL_DEFAULT_START_TO_CLOSE_TIMEOUT:30s}
temporal.default.schedule-to-close-timeout=${TEMPORAL_DEFAULT_SCHEDULE_TO_CLOSE_TIMEOUT:5000s}
temporal.third-party.retry-max-attempts=${TEMPORAL_THIRD_PARTY_RETRY_MAX_ATTEMPTS:1}
temporal.third-party.start-to-close-timeout=${TEMPORAL_THIRD_PARTY_START_TO_CLOSE_TIMEOUT:60s}
temporal.third-party.schedule-to-close-timeout=${TEMPORAL_THIRD_PARTY_SCHEDULE_TO_CLOSE_TIMEOUT:5000s}

#Broadsoft Configuration:
broadsoft.connection.maxRetries=${BROADSOFT_MAX_RETRIES:3}
broadsoft.connection.retryDelayMs=${BROADSOFT_RETRY_DELAY:1000}