package net.evolveip.ossmosis.api.utils.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import net.evolveip.ossmosis.api.utils.validators.annotations.GroupId;

public class GroupIdValidator implements ConstraintValidator<GroupId, String> {

  @Override
  public void initialize(GroupId constraintAnnotation) {
  }

  @Override
  public boolean isValid(String groupId, ConstraintValidatorContext context) {
    if (groupId == null) {
      return false;
    }
    return groupId.matches("^[a-zA-Z0-9!@$^&*()_,.<>?{}\\[\\]|`~\\-= ]{1,30}$");
  }

}
