package net.evolveip.ossmosis.api.features.phonenumber.service;


import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.ATT;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.AVOXI;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.BANDWIDTH;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.BANDWIDTH_OFF;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.COLT;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.LABEL_ATT;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.LABEL_AVOXI;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.LABEL_BANDWIDTH;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.LABEL_BANDWIDTH_OFF;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.LABEL_COLT;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.LABEL_LEVEL3;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.LABEL_PEERLESS;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.LABEL_TELNIX;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.LABEL_TMOBILE;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.LABEL_USCELLULAR;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.LABEL_VERIZON;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.LABEL_VOIP_INNOVATIONS;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.LABEL_VOXBONE;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.LEVEL3;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.PEERLESS;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.TELNIX;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.TMOBILE;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.USCELLULAR;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.VERIZON;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.VOIP_INNOVATIONS;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.CarrierConstants.VOXBONE;

import java.util.HashMap;
import java.util.Map;
import net.evolveip.ossmosis.api.features.phonenumber.entity.Carrier;
import net.evolveip.ossmosis.api.features.phonenumber.mapper.CarrierMapper;
import net.evolveip.ossmosis.api.features.phonenumber.repository.CarrierRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DefaultCarriersService extends BaseCarrierService {

  private final Map<String, String> defaultCarriers = new HashMap<>() {{
    put(ATT, LABEL_ATT);
    put(AVOXI, LABEL_AVOXI);
    put(BANDWIDTH, LABEL_BANDWIDTH);
    put(BANDWIDTH_OFF, LABEL_BANDWIDTH_OFF);
    put(COLT, LABEL_COLT);
    put(LEVEL3, LABEL_LEVEL3);
    put(PEERLESS, LABEL_PEERLESS);
    put(TELNIX, LABEL_TELNIX);
    put(TMOBILE, LABEL_TMOBILE);
    put(USCELLULAR, LABEL_USCELLULAR);
    put(VERIZON, LABEL_VERIZON);
    put(VOIP_INNOVATIONS, LABEL_VOIP_INNOVATIONS);
    put(VOXBONE, LABEL_VOXBONE);
  }};

  @Autowired
  public DefaultCarriersService(CarrierRepository carrierRepository, CarrierMapper carrierMapper) {
    super(carrierRepository, carrierMapper);
  }

  public void createDefaultCarriers() {
    for (Map.Entry<String, String> entry : defaultCarriers.entrySet()) {
      String carrierName = entry.getKey();
      String carrierLabel = entry.getValue();
      if (carrierRepository.findCarrierByCarrierName(carrierName).isEmpty()) {
        Carrier carrier = new Carrier();
        carrier.setCarrierName(carrierName);
        carrier.setCarrierLabel(carrierLabel);
        carrierRepository.save(carrier);
      }
    }
  }
}