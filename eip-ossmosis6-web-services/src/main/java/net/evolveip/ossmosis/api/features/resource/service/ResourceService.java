package net.evolveip.ossmosis.api.features.resource.service;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.enterprise.constant.EnterpriseConstants;
import net.evolveip.ossmosis.api.features.resource.constant.ResourceConstants;
import net.evolveip.ossmosis.api.features.resource.dto.ResourceViewResponseDTO;
import net.evolveip.ossmosis.api.features.resource.entity.Resource;
import net.evolveip.ossmosis.api.features.resource.entity.view.ResourceView;
import net.evolveip.ossmosis.api.features.resource.mapper.ResourceViewMapper;
import net.evolveip.ossmosis.api.features.resource.repository.ResourceRepository;
import net.evolveip.ossmosis.api.utils.exceptions.NotFoundException;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.service.AuthorizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ResourceService {

  private final ResourceRepository resourceRepository;
  private final ResourceViewMapper resourceViewMapper;

  @Autowired
  private final AuthorizationService authorizationService;

  @Autowired
  public ResourceService(ResourceRepository resourceRepository,
      ResourceViewMapper resourceViewMapper, AuthorizationService authorizationService) {
    this.resourceRepository = resourceRepository;
    this.resourceViewMapper = resourceViewMapper;
    this.authorizationService = authorizationService;
  }

  public List<Resource> createDefaultResources() {
    List<Resource> existingResources = this.resourceRepository.findByResourceNameIn(
        ResourceConstants.resourceNameList);

    if (existingResources.size() != ResourceConstants.resourceNameList.size()) {

      List<Resource> resourceList = new ArrayList<>();
      for (String resourceName : ResourceConstants.resourceNameList) {
        if (existingResources.stream()
            .noneMatch(resource -> resource.getResourceName().equals(resourceName))) {
          resourceList.add(Resource.builder()
              .dateCreated(ZonedDateTime.now())
              .dateUpdated(ZonedDateTime.now())
              .resourceName(resourceName).build());
        }
      }
      if (!resourceList.isEmpty()) {
        existingResources.addAll(this.resourceRepository.saveAll(resourceList));
        logger.info("Created default resources");
      }
    }

    return existingResources;
  }

  public Resource getResourceByResourceId(Integer resourceId) {
    return this.resourceRepository.findById(resourceId)
        .orElseThrow(() -> new NotFoundException("Resource id not found: " + resourceId));
  }

  public ApiResponse<List<ResourceViewResponseDTO>> getResources() {
    try {

      List<ResourceView> resources = this.resourceRepository.findAllResourceViews();
      boolean hasFullAccess = this.authorizationService.doesCurrentUserHaveAccessToEnterprise(
          EnterpriseConstants.ROOT_ENTERPRISE_ID);
      if (!hasFullAccess) {
        resources.removeIf(resourceView -> resourceView.getResourceName().equals("platforms") ||
            resourceView.getResourceName().equals("toolbox"));
      }

      return ApiResponse.create(
          this.resourceViewMapper.entityViewsToDTOs(resources));
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }
}
