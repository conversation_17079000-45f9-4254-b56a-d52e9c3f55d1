package net.evolveip.ossmosis.api.config.dao;

import java.io.IOException;
import java.util.HashMap;
import java.util.Objects;
import javax.sql.DataSource;
import org.hibernate.cfg.AvailableSettings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.init.DataSourceInitializer;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.jdbc.datasource.init.ScriptUtils;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
@EnableJpaRepositories(
    basePackages = {
        "net.evolveip.ossmosis.api.features.audit.repository",
        "net.evolveip.ossmosis.api.features.enterprise.repository",
        "net.evolveip.ossmosis.api.features.group.repository",
        "net.evolveip.ossmosis.api.features.login.repository",
        "net.evolveip.ossmosis.api.features.permission.repository",
        "net.evolveip.ossmosis.api.features.platform.repository",
        "net.evolveip.ossmosis.api.features.resource.repository",
        "net.evolveip.ossmosis.api.features.phonenumber.repository",
        "net.evolveip.ossmosis.api.features.role.repository",
    },
    entityManagerFactoryRef = "primaryEntityManagerFactory",
    transactionManagerRef = "primaryTransactionManager"
)
public class OssmosisPartnerProvDataSourceConfig {

  private final String sqlStartupScriptPath = "classpath:sql/init/**/*.sql";

  private final Environment env;

  @Autowired
  public OssmosisPartnerProvDataSourceConfig(Environment env) {
    this.env = env;
  }

  @Bean
  @ConfigurationProperties(prefix = "spring.datasource")
  public DataSourceProperties primaryDataSourceProperties() {
    return new DataSourceProperties();
  }

  @Bean
  @Primary
  public DataSource primaryDataSource() {
    return new OssmosisDataSource(
        primaryDataSourceProperties().initializeDataSourceBuilder().build());
  }

  @Bean
  public DataSourceInitializer primaryDataSourceInitializer(
      @Qualifier("primaryDataSource") final DataSource dataSource)
      throws IOException {
    //Run all the scripts in the resources/sql/init folder
    ResourceDatabasePopulator resourceDatabasePopulator = new ResourceDatabasePopulator();
    resourceDatabasePopulator.setSeparator(ScriptUtils.EOF_STATEMENT_SEPARATOR);

    PathMatchingResourcePatternResolver loader = new PathMatchingResourcePatternResolver();
    Resource[] resources = loader.getResources(sqlStartupScriptPath);
    for (Resource resource : resources) {
      resourceDatabasePopulator.addScript(resource);
    }

    DataSourceInitializer dataSourceInitializer = new DataSourceInitializer();
    dataSourceInitializer.setDataSource(dataSource);
    dataSourceInitializer.setDatabasePopulator(resourceDatabasePopulator);
    return dataSourceInitializer;
  }

  @Bean
  @Primary
  public LocalContainerEntityManagerFactoryBean primaryEntityManagerFactory() {
    LocalContainerEntityManagerFactoryBean entityManagerFactoryBean = new LocalContainerEntityManagerFactoryBean();
    entityManagerFactoryBean.setDataSource(primaryDataSource());
    entityManagerFactoryBean.setPackagesToScan(
        "net.evolveip.ossmosis.api.features.audit.entity",
        "net.evolveip.ossmosis.api.features.enterprise.entity",
        "net.evolveip.ossmosis.api.features.group.entity",
        "net.evolveip.ossmosis.api.features.login.entity",
        "net.evolveip.ossmosis.api.features.permission.entity",
        "net.evolveip.ossmosis.api.features.platform.entity",
        "net.evolveip.ossmosis.api.features.resource.entity",
        "net.evolveip.ossmosis.api.features.phonenumber.entity",
        "net.evolveip.ossmosis.api.features.role.entity");

    HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
    vendorAdapter.setShowSql(Objects.equals(env.getProperty("spring.jpa.show-sql"), "true"));
    entityManagerFactoryBean.setJpaVendorAdapter(vendorAdapter);

    HashMap<String, Object> properties = new HashMap<>();
    properties.put(AvailableSettings.DIALECT,
        env.getProperty("spring.jpa.properties.hibernate.dialect"));
    properties.put(AvailableSettings.NON_CONTEXTUAL_LOB_CREATION,
        env.getProperty("spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation"));
    properties.put(AvailableSettings.DEFAULT_SCHEMA,
        env.getProperty("spring.jpa.properties.hibernate.default_schema"));
    properties.put(AvailableSettings.HBM2DDL_AUTO,
        env.getProperty("spring.jpa.hibernate.ddl-auto"));
    properties.put(AvailableSettings.IMPLICIT_NAMING_STRATEGY,
        OssmosisImplicitNamingStrategy.class.getName());
    properties.put(AvailableSettings.COLUMN_ORDERING_STRATEGY,
        OssmosisColumnOrderingStrategy.class.getName());
    entityManagerFactoryBean.setJpaPropertyMap(properties);

    return entityManagerFactoryBean;
  }

  @Bean
  @Primary
  public PlatformTransactionManager primaryTransactionManager() {
    JpaTransactionManager transactionManager = new JpaTransactionManager();
    transactionManager.setEntityManagerFactory(primaryEntityManagerFactory().getObject());
    return transactionManager;
  }

}
