package net.evolveip.ossmosis.api.features.audit.dto;


import java.time.ZonedDateTime;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RecordAuditLogDTO {

  private Long recordAuditLogId;

  private String recordId;

  private String oldRecordId;

  private String op;

  private ZonedDateTime timestamp;

  private String tableName;

  private Map<String, Object> record;

  private Map<String, Object> oldRecord;

}
