package net.evolveip.ossmosis.api.features.phonenumber.validator.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import net.evolveip.ossmosis.api.features.phonenumber.validator.annotations.ValidLong;

public class LongValidator implements ConstraintValidator<ValidLong, String> {

  @Override
  public void initialize(ValidLong constraintAnnotation) {
  }

  @Override
  public boolean isValid(String value, final ConstraintValidatorContext context) {
    if (value == null || value.isEmpty() || value.isBlank()) {
      return false;
    }
    try {
      Long.parseLong(value);
      return true;
    } catch (Exception e) {
      return false;
    }
  }
}