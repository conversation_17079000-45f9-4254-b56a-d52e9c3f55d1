package net.evolveip.ossmosis.api.features.group.repository;

import java.util.List;
import java.util.Optional;
import net.evolveip.ossmosis.api.features.group.entity.Group;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface GroupRepository extends JpaRepository<Group, String> {

  @Query(value = "from Group g where g.enterpriseId in (:enterpriseAccessIds)")
  List<Group> findAllByEnterprisesForUser(List<String> enterpriseAccessIds);

  @Query(value = "from Group g where g.enterpriseId = :enterpriseId")
  List<Group> findAllByEnterpriseId(String enterpriseId);

  @Query(value = "select g from Group g " +
      "where g.enterpriseId = :enterpriseId " +
      "and (lower(g.groupId) like %:filterValue% " +
      "or lower(g.groupName) like %:filterValue% " +
      "or lower(g.billingAddress.addressLine1) like %:filterValue% " +
      "or lower(g.billingAddress.addressLine2) like %:filterValue% " +
      "or lower(g.billingAddress.city) like %:filterValue% " +
      "or lower(g.billingAddress.stateOrProvince) like %:filterValue% " +
      "or lower(g.billingAddress.country) like %:filterValue% " +
      "or lower(g.billingAddress.zipOrPostalCode) like %:filterValue% " +
      "or lower(g.timezone) like %:filterValue% )")
  Page<Group> findAllByEnterpriseIdAndFilterValue(String enterpriseId, String filterValue, Pageable pageable);

  Optional<Group> findGroupByEnterpriseIdAndGroupId(String enterpriseId, String groupId);

  boolean existsByEnterpriseIdAndGroupId(String enterpriseId, String groupId);

  void deleteByEnterpriseIdAndGroupId(String enterpriseId, String groupId);

}
