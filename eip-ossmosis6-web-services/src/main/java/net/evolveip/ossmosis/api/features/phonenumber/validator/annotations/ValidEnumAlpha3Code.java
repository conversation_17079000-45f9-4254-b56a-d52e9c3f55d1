package net.evolveip.ossmosis.api.features.phonenumber.validator.annotations;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import net.evolveip.ossmosis.api.features.phonenumber.validator.validators.ISOAlpha3CodeValidator;

@Constraint(validatedBy = ISOAlpha3CodeValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidEnumAlpha3Code {

  String message() default "Invalid ISO alpha 3 code value: [${validatedValue}]";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};

  Class<? extends Enum<?>> enumClass();
}