package net.evolveip.ossmosis.api.features.role.mapper;

import java.util.List;
import net.evolveip.ossmosis.api.features.role.dto.RoleResourcePermissionResponseDTO;
import net.evolveip.ossmosis.api.features.role.entity.view.RoleResourcePermissionRolePermissionView;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = ComponentModel.SPRING)
public interface RoleResourcePermissionRolePermissionViewMapper {

  RoleResourcePermissionRolePermissionViewMapper INSTANCE = Mappers.getMapper(
      RoleResourcePermissionRolePermissionViewMapper.class);


  @Mapping(source = "permissionId", target = "permissionId")
  @Mapping(source = "resourceId", target = "resourceId")
  RoleResourcePermissionResponseDTO toRoleResourcePermissionResponseDTO(
      RoleResourcePermissionRolePermissionView roleResourcePermissionRolePermissionView);


  List<RoleResourcePermissionResponseDTO> entitiesToDTOs(
      List<RoleResourcePermissionRolePermissionView> roleResourcePermissionRolePermissionViews);

}
