package net.evolveip.ossmosis.api.features.calldetails.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Table(name = "CALLCATEGORYDIMENSION")
@NoArgsConstructor
@AllArgsConstructor
public class CallCategoryEntity {

  @Id
  @Column(name = "CCD_ID")
  private Long id;

  @Column(name = "CCD_DESCRIPTION")
  private String callCategory;
}
