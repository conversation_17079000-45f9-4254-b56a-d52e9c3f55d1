package net.evolveip.ossmosis.api.features.login.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.login.dto.LoginRequestDTO;
import net.evolveip.ossmosis.api.features.login.entity.Login;
import net.evolveip.ossmosis.api.features.login.repository.LoginResourcePermissionRepository;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.response.ApiResponseError;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class LoginResourcePermissionService {


  private final LoginResourcePermissionRepository loginResourcePermissionRepository;

  private final ObjectMapper objectMapper;

  public LoginResourcePermissionService(
      LoginResourcePermissionRepository loginResourcePermissionRepository,
      ObjectMapper objectMapper) {
    this.loginResourcePermissionRepository = loginResourcePermissionRepository;
    this.objectMapper = objectMapper;
  }

  public ApiResponse<Boolean> mergePermissionSet(LoginRequestDTO loginRequestDTO,
      Login newLogin) {
    try {
      Boolean permissionMergeSuccess = this.loginResourcePermissionRepository.mergePermissionSet(
          newLogin.getLoginId(),
          this.objectMapper.writeValueAsString(loginRequestDTO.getUserPermissionList()));
      ApiResponse<Boolean> response = ApiResponse.create(
          permissionMergeSuccess);

      if (!permissionMergeSuccess) {
        response.setSuccessful(false);
        response.getErrors().add(
            ApiResponseError.builder().errorMessage("Error assigning permissions to the login.")
                .build());
      }
      return response;
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }

  }

}
