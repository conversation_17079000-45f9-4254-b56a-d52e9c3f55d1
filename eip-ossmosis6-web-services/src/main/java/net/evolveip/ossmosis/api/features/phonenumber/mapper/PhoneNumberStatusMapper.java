package net.evolveip.ossmosis.api.features.phonenumber.mapper;

import net.evolveip.ossmosis.api.features.phonenumber.common.dto.PhoneNumberStatusDTO;
import net.evolveip.ossmosis.api.features.phonenumber.entity.PhoneNumberStatus;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface PhoneNumberStatusMapper {

  @Mapping(ignore = true, target = "statusId")
  @Mapping(ignore = true, target = "dateCreated")
  @Mapping(ignore = true, target = "dateUpdated")
  @Mapping(source = "statusName", target = "statusName")
  PhoneNumberStatus toPhoneNumberStatus(PhoneNumberStatusDTO phoneNumberStatusDTO);

  PhoneNumberStatusDTO toPhoneNumberStatusDTO(PhoneNumberStatus phoneNumberStatus);
}