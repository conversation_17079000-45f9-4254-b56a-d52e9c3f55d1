package net.evolveip.ossmosis.api.scheduler.controller;

import jakarta.validation.Valid;
import java.util.Map;
import net.evolveip.ossmosis.api.features.calldetails.constant.CallDetailConstants;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateCronScheduleJob;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateSimpleScheduleJob;
import net.evolveip.ossmosis.api.scheduler.common.response.HttpResponseGetScheduledJob;
import net.evolveip.ossmosis.api.scheduler.service.scheduler.ScheduledCallDetailService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Validated
@RequestMapping(value = "/enterprise/{enterpriseId}/scheduler/call-details")
public class ScheduledCallDetailController {

  private final ScheduledCallDetailService schedulerReportService;

  @Autowired
  public ScheduledCallDetailController(ScheduledCallDetailService schedulerReportService) {
    this.schedulerReportService = schedulerReportService;
  }

  @Secured({CallDetailConstants.CALL_DETAIL_REPORTS_CREATE, CallDetailConstants.CALL_DETAIL_REPORTS_READ})
  @PostMapping(value = "/simpleschedule")
  public ResponseEntity<ApiResponse<HttpResponseGetScheduledJob>> doPostSimpleSchedule(
      @PathVariable("enterpriseId") String enterpriseId,
      @Valid @RequestBody HttpRequestCreateSimpleScheduleJob request) {
    request.getEmailDefinition().setEnterpriseId(enterpriseId);
    ApiResponse<HttpResponseGetScheduledJob> apiResponse = schedulerReportService.processCreateSimpleScheduleJob(
        request);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured({CallDetailConstants.CALL_DETAIL_REPORTS_CREATE, CallDetailConstants.CALL_DETAIL_REPORTS_READ})
  @PostMapping(value = "/cronschedule")
  public ResponseEntity<ApiResponse<HttpResponseGetScheduledJob>> doPostCronSchedule(
      @PathVariable("enterpriseId") String enterpriseId,
      @Valid @RequestBody HttpRequestCreateCronScheduleJob request) {
    request.getEmailDefinition().setEnterpriseId(enterpriseId);
    ApiResponse<HttpResponseGetScheduledJob> apiResponse = schedulerReportService.processCreateCronScheduleJob(
        request);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(CallDetailConstants.CALL_DETAIL_REPORTS_READ)
  @GetMapping(value = "/list")
  public ResponseEntity<?> doGet(@PathVariable("enterpriseId") String enterpriseId,
      @RequestParam(value = "addDetails", defaultValue = "false") boolean addDetails) {
    ApiResponse<?> apiResponse;
    if (addDetails) {
      apiResponse = schedulerReportService.processGet(enterpriseId, true);
    } else {
      apiResponse = schedulerReportService.processGet(enterpriseId, false);
    }
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }
  @Secured(CallDetailConstants.CALL_DETAIL_REPORTS_READ)
  @GetMapping
  public ResponseEntity<Page<HttpResponseGetScheduledJob>> doGetDetailsPageable(
      @PathVariable("enterpriseId") String enterpriseId,
      @RequestParam(value = "filterValue", required = false, defaultValue = "") String filterValue,
      @SortDefault(sort = "reportTitle", direction = Direction.ASC) @PageableDefault(size = 15) final Pageable pageable
  ) {
    Page<HttpResponseGetScheduledJob> page = schedulerReportService.processGetDetailsPageable(
        enterpriseId, pageable, filterValue);
    return ResponseEntity.ok(page);
  }

  @Secured(CallDetailConstants.CALL_DETAIL_REPORTS_READ)
  @GetMapping(value = "/{reportId}")
  public ResponseEntity<ApiResponse<HttpResponseGetScheduledJob>> doGetById(
      @PathVariable("enterpriseId") String enterpriseId,
      @PathVariable("reportId") String reportId) {
    ApiResponse<HttpResponseGetScheduledJob> apiResponse = schedulerReportService.processGetByJobId(
        enterpriseId, reportId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(CallDetailConstants.CALL_DETAIL_REPORTS_DELETE)
  @DeleteMapping("/{reportId}")
  public ResponseEntity<ApiResponse<Boolean>> doDeleteById(@PathVariable String enterpriseId,
      @PathVariable("reportId") String reportId) {
    ApiResponse<Boolean> apiResponse = schedulerReportService.processDelete(enterpriseId, reportId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(CallDetailConstants.CALL_DETAIL_REPORTS_UPDATE)
  @PostMapping("/{reportId}/pause")
  public ResponseEntity<ApiResponse<?>> doPauseById(@PathVariable String enterpriseId,
      @PathVariable("reportId") String reportId) {
    ApiResponse<?> apiResponse = schedulerReportService.processPause(enterpriseId, reportId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(CallDetailConstants.CALL_DETAIL_REPORTS_UPDATE)
  @PostMapping("/{reportId}/resume")
  public ResponseEntity<ApiResponse<?>> doResumeById(@PathVariable String enterpriseId,
      @PathVariable("reportId") String reportId) {
    ApiResponse<?> apiResponse = schedulerReportService.processResume(enterpriseId, reportId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(CallDetailConstants.CALL_DETAIL_REPORTS_READ)
  @GetMapping(value = "/{reportId}/runtimes")
  public ResponseEntity<ApiResponse<?>> doGetRunTimesById(
      @PathVariable("enterpriseId") String enterpriseId,
      @PathVariable("reportId") String reportId) {
    ApiResponse<?> apiResponse = schedulerReportService.processGetRunTimesById(enterpriseId,
        reportId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured({CallDetailConstants.CALL_DETAIL_REPORTS_UPDATE, CallDetailConstants.CALL_DETAIL_REPORTS_READ})
  @PostMapping("/pauseall")
  public ResponseEntity<ApiResponse<Map<String, Integer>>> doPauseAll(
      @PathVariable String enterpriseId) {
    ApiResponse<Map<String, Integer>> apiResponse = schedulerReportService.processPauseAll(
        enterpriseId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured({CallDetailConstants.CALL_DETAIL_REPORTS_UPDATE, CallDetailConstants.CALL_DETAIL_REPORTS_READ})
  @PostMapping("/resumeall")
  public ResponseEntity<ApiResponse<Map<String, Integer>>> doResumeAll(
      @PathVariable String enterpriseId) {
    ApiResponse<Map<String, Integer>> apiResponse = schedulerReportService.processResumeAll(
        enterpriseId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(CallDetailConstants.CALL_DETAIL_REPORTS_READ)
  @GetMapping("/{reportId}/status")
  public ResponseEntity<ApiResponse<?>> doStatus(@PathVariable String enterpriseId,
      @PathVariable("reportId") String reportId) {
    ApiResponse<?> apiResponse = schedulerReportService.processStatus(enterpriseId, reportId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(CallDetailConstants.CALL_DETAIL_REPORTS_READ)
  @GetMapping("/statusall")
  public ResponseEntity<ApiResponse<?>> doAllStatus(@PathVariable String enterpriseId) {
    ApiResponse<?> apiResponse = schedulerReportService.processStatusAll(enterpriseId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }
}