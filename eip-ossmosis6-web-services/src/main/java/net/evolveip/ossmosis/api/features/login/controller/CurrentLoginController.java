package net.evolveip.ossmosis.api.features.login.controller;

import net.evolveip.ossmosis.api.features.login.dto.LoginInfoResponseDTO;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.service.AuthorizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class CurrentLoginController {

  private final AuthorizationService authorizationService;

  @Autowired
  public CurrentLoginController(AuthorizationService authorizationService) {
    this.authorizationService = authorizationService;
  }

  @GetMapping("/current-user")
  public ResponseEntity<ApiResponse<LoginInfoResponseDTO>> getCurrentLoggedInUserInfo() {
    ApiResponse<LoginInfoResponseDTO> apiResponse = authorizationService.getLoggedInUserInfoResponse();
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

}
