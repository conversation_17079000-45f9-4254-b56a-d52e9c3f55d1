package net.evolveip.ossmosis.api.features.resource.mapper;

import java.util.List;
import net.evolveip.ossmosis.api.features.resource.dto.ResourceViewResponseDTO;
import net.evolveip.ossmosis.api.features.resource.entity.view.ResourceView;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = ComponentModel.SPRING)
public interface ResourceViewMapper {

  ResourceViewMapper INSTANCE = Mappers.getMapper(ResourceViewMapper.class);


  @Mapping(source = "resourceId", target = "resourceId")
  @Mapping(source = "resourceName", target = "resourceName")
  @Mapping(source = "assignablePermissionIds", target = "assignablePermissions")
  ResourceViewResponseDTO toResourceResponseDTO(ResourceView resourceView);


  List<ResourceViewResponseDTO> entityViewsToDTOs(List<ResourceView> resourceViews);

}
