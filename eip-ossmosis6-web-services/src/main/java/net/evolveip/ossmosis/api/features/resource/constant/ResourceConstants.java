package net.evolveip.ossmosis.api.features.resource.constant;

import java.util.AbstractMap;
import java.util.AbstractMap.SimpleEntry;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import net.evolveip.ossmosis.api.features.permission.constant.PermissionConstants;

public final class ResourceConstants {

  // resources
  public static final String RESOURCE_REPORTS_CALL_DETAILS = "call detail reports";
  public static final String RESOURCE_REPORTS_AUDIT_LOGS = "audit log reports";

  public static final String RESOURCE_TELEPHONY_PHONE_NUMBERS = "phone numbers";

  public static final String RESOURCE_SECURITY_LOGINS = "logins";
  public static final String RESOURCE_SECURITY_ROLES = "roles";

  public static final String RESOURCE_PROVISIONING_ENTERPRISES = "enterprises";
  public static final String RESOURCE_PROVISIONING_GROUPS = "groups";

  public static final String RESOURCE_ADMIN_PLATFORMS = "platforms";
  public static final String RESOURCE_ADMIN_TOOLBOX = "toolbox";

  public static final ArrayList<String> resourceNameList = new ArrayList<>(
      List.of(
          RESOURCE_REPORTS_CALL_DETAILS,
          RESOURCE_REPORTS_AUDIT_LOGS,
          RESOURCE_TELEPHONY_PHONE_NUMBERS,
          RESOURCE_SECURITY_LOGINS,
          RESOURCE_SECURITY_ROLES,
          RESOURCE_PROVISIONING_ENTERPRISES,
          RESOURCE_PROVISIONING_GROUPS,
          RESOURCE_ADMIN_PLATFORMS,
          RESOURCE_ADMIN_TOOLBOX)
  );

  public static final Map<String, List<String>> RESOURCE_PERMISSION_MAP = Stream.of(
      new AbstractMap.SimpleEntry<>(RESOURCE_REPORTS_CALL_DETAILS, PermissionConstants.crudPermissionNameList),
      new AbstractMap.SimpleEntry<>(RESOURCE_REPORTS_AUDIT_LOGS, PermissionConstants.crudPermissionNameList),
      new AbstractMap.SimpleEntry<>(RESOURCE_TELEPHONY_PHONE_NUMBERS, PermissionConstants.crudPermissionNameList),
      new AbstractMap.SimpleEntry<>(RESOURCE_SECURITY_LOGINS, PermissionConstants.crudPermissionNameList),
      new AbstractMap.SimpleEntry<>(RESOURCE_SECURITY_ROLES, PermissionConstants.crudPermissionNameList),
      new AbstractMap.SimpleEntry<>(RESOURCE_PROVISIONING_ENTERPRISES, PermissionConstants.crudPermissionNameList),
      new AbstractMap.SimpleEntry<>(RESOURCE_PROVISIONING_GROUPS, PermissionConstants.crudPermissionNameList),
      new AbstractMap.SimpleEntry<>(RESOURCE_ADMIN_PLATFORMS, PermissionConstants.crudPermissionNameList),
      new AbstractMap.SimpleEntry<>(RESOURCE_ADMIN_TOOLBOX, List.of(PermissionConstants.PERMISSION_NAME_FULL_ACCESS))
  ).collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue));

  // constraints
  public static final int MAX_RESOURCE_NAME_LENGTH = 100;
}
