package net.evolveip.ossmosis.api.scheduler.common.dto;

import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
public class CallDetailReportRunnerDTO extends BaseReportRunnerDTO {

  private String enterpriseId;
  private String enterpriseName;
  private String fromEmail;
  private List<String> toEmail;
  private String answerStatus;
  private String externalOnly;
  private String direction;
  private String callType;
  private String userType;
  private List<String> userNumber;
  private List<String> groupNumber;
  private String groupBy;
  private Integer dataWindowOffset;
  private ChronoUnit dataWindowOffsetUnits;
  private LocalTime dataWindowEndTime;
  private Integer dataWindow;
  private ChronoUnit dataWindowUnits;
}