package net.evolveip.ossmosis.api.features.common;


import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import java.io.Serial;
import java.io.Serializable;
import java.time.ZonedDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;
import org.hibernate.annotations.UpdateTimestamp;

@MappedSuperclass
@Getter
@Data
@EqualsAndHashCode
public abstract class BaseEntity implements Serializable {

  @Serial
  private static final long serialVersionUID = 202504040001L;

  @CreationTimestamp(source = SourceType.VM)
  @Column(name = "date_created", nullable = false, updatable = false)
  @ColumnDefault("CURRENT_TIMESTAMP")
  private ZonedDateTime dateCreated;

  @UpdateTimestamp(source = SourceType.VM)
  @Column(name = "date_updated", nullable = false)
  @ColumnDefault("CURRENT_TIMESTAMP")
  @EqualsAndHashCode.Exclude
  private ZonedDateTime dateUpdated;
}