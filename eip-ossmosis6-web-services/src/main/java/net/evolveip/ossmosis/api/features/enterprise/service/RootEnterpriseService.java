package net.evolveip.ossmosis.api.features.enterprise.service;

import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.enterprise.constant.EnterpriseConstants;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.enterprise.repository.EnterpriseRepository;
import net.evolveip.ossmosis.api.features.platform.entity.Platform;
import net.evolveip.ossmosis.api.features.platform.service.RootPlatformService;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RootEnterpriseService {

  private static final String rootEnterpriseId = EnterpriseConstants.ROOT_ENTERPRISE_ID;
  private static final String rootEnterpriseName = EnterpriseConstants.ROOT_ENTERPRISE_NAME;
  private static final String accountCountry = "USA";
  private final EnterpriseRepository enterpriseRepository;
  private final RootPlatformService rootPlatformService;

  public RootEnterpriseService(EnterpriseRepository enterpriseRepository,
      RootPlatformService rootPlatformService) {
    this.enterpriseRepository = enterpriseRepository;
    this.rootPlatformService = rootPlatformService;
  }

  @Transactional
  public void createRootEnterprise() {
    Platform rootPlatform = rootPlatformService.getRootPlatform();

    if (enterpriseRepository.findEnterpriseByEnterpriseId(rootEnterpriseId).isEmpty()) {
      Enterprise rootEnterprise = Enterprise
          .builder()
          .parentEnterprise(null)
          .enterpriseName(rootEnterpriseName)
          .enterpriseId(rootEnterpriseId)
          .billingId(rootEnterpriseId)
          .accountCountry(accountCountry)
          .platform(rootPlatform)
          .build();

      enterpriseRepository.save(rootEnterprise);
      enterpriseRepository.updateTreeClosure(rootEnterpriseId, null);
      logger.info("Root enterprise created: {}", rootEnterpriseName);
    }
  }

  public Enterprise getRootEnterprise() {
    return enterpriseRepository.findEnterpriseByEnterpriseId(rootEnterpriseId)
        .orElseThrow(() -> new RuntimeException("Root enterprise not found"));
  }
}