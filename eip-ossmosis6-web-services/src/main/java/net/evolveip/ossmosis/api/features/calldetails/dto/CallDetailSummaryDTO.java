package net.evolveip.ossmosis.api.features.calldetails.dto;

import jakarta.persistence.OneToMany;
import java.util.List;
import jdk.jfr.Label;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.utils.file.annotations.Exportable;

@Data
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Exportable
public class CallDetailSummaryDTO {

  @Label("User Name")
  private String userName;

  @Label("User Number")
  private String userNumber;

  @Label("User Type")
  private String userType;

  @Label("Group Id")
  private String groupId;

  @Label("Group Number")
  private String groupNumber;

  @Label("Inbound Calls Count")
  private long inboundCallsCount;

  @Label("Inbound Calls Duration")
  private long inboundCallsDuration;

  @Label("Inbound Calls Average Duration")
  private double inboundCallsAverageDuration;

  @Label("Outbound Calls Count")
  private long outboundCallsCount;

  @Label("Outbound Calls Duration")
  private long outboundCallsDuration;

  @Label("Outbound Calls Average Duration")
  private double outboundCallsAverageDuration;

  @Label("Total Calls Count")
  private long totalCallsCount;

  @Label("Total Calls Duration")
  private long totalCallsDuration;

  @Label("Total Calls Average Duration")
  private double totalCallsAverageDuration;

  @Label("Call Detail List")
  @OneToMany
  private List<CallDetailDTO> callDetailDTOList;

}
