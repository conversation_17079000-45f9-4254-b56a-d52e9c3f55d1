package net.evolveip.ossmosis.api.features.calldetails.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.File;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.config.csv.CSVTempLocationPropertiesConfig;
import net.evolveip.ossmosis.api.features.calldetails.dto.CallDetailDTO;
import net.evolveip.ossmosis.api.features.calldetails.dto.CallDetailSummaryDTO;
import net.evolveip.ossmosis.api.features.calldetails.dto.CallDetailSummaryListWrapperDTO;
import net.evolveip.ossmosis.api.features.calldetails.dto.CustomCallDetailsPage;
import net.evolveip.ossmosis.api.features.calldetails.repository.CallDetailRepository;
import net.evolveip.ossmosis.api.utils.file.CSVService;
import net.evolveip.ossmosis.api.utils.file.JSONService;
import net.evolveip.ossmosis.api.utils.file.PDFService;
import net.evolveip.ossmosis.api.utils.file.XLSXService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.service.AuthorizationService;
import net.snowflake.client.jdbc.SnowflakeSQLException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@Lazy
public class CallDetailService {

  @Lazy
  private final CallDetailRepository callDetailRepository;
  private final AuthorizationService authorizationService;
  private final CSVTempLocationPropertiesConfig tempDir;
  private final ObjectMapper objectMapper;

  @Autowired
  public CallDetailService(CallDetailRepository callDetailRepository,
      AuthorizationService authorizationService, CSVTempLocationPropertiesConfig tempDir,
      ObjectMapper objectMapper) {
    this.callDetailRepository = callDetailRepository;
    this.authorizationService = authorizationService;
    this.tempDir = tempDir;
    this.objectMapper = objectMapper;
    this.objectMapper.registerModule(new JavaTimeModule());

    try {
      this.callDetailRepository.setResultFormat();
    } catch (Exception e) {
      logger.error(e.getMessage());
    }
  }

  public ApiResponse<Page<CallDetailSummaryDTO>> processGetPageable(String answerStatus,
      String externalOnly, String direction, String callType, String userType,
      ZonedDateTime startDate, ZonedDateTime endDate, List<String> userNumbers,
      List<String> groupNumbers, String groupBy, String[] enterpriseIds, final Pageable pageable) throws SnowflakeSQLException {
    try {

      List<String> enterpriseIdsList = new ArrayList<>();

      if (enterpriseIds != null && enterpriseIds.length > 0) {
        enterpriseIdsList = Arrays.asList(enterpriseIds);
      } else {
        enterpriseIdsList = authorizationService.getEnterpriseAccessStringListForCurrentUser();
      }

      callDetailRepository.setResultFormat();
      long totalDetails = callDetailRepository.getCustomCallDetailsCount(answerStatus, externalOnly, direction, callType, userType, startDate, endDate, enterpriseIdsList, userNumbers, groupNumbers, groupBy);
      if (totalDetails > 250000) {
        return ApiResponse.create(new CustomCallDetailsPage<>(new ArrayList<>(), pageable, 0, 0), true, "Selected filters have more than 250,000 results, please narrow down the search parameters",
            HttpStatus.OK);
      }

      long totalElements = callDetailRepository.getCustomCallDetailsPageableCount(answerStatus, externalOnly, direction, callType, userType, startDate, endDate, enterpriseIdsList, userNumbers, groupNumbers, groupBy);
      List<CallDetailSummaryDTO> pageList = callDetailRepository.getCustomCallDetailsPageList(answerStatus, externalOnly, direction, callType, userType, startDate,endDate, enterpriseIdsList, userNumbers, groupNumbers, groupBy, pageable);
      List<CallDetailDTO> callDetailList = callDetailRepository.getCustomCallDetailsPageable(answerStatus, externalOnly, direction, callType, userType, startDate, endDate, enterpriseIdsList, userNumbers, groupNumbers, groupBy, pageable);
      List<CallDetailSummaryDTO> summaryList = createCallDetailSummaryList(pageList, callDetailList, groupBy, true);;

      Page page = new CustomCallDetailsPage(summaryList, pageable, totalElements, totalDetails);
      return ApiResponse.create(page);
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }

  public ApiResponse<CallDetailSummaryListWrapperDTO> processGet(String answerStatus,
      String externalOnly, String direction, String callType, String userType,
      ZonedDateTime startDate, ZonedDateTime endDate, List<String> userNumber,
      List<String> groupNumber, String groupBy, String[] enterpriseIds) throws SnowflakeSQLException {
    try {
      CallDetailSummaryListWrapperDTO callDetails = new CallDetailSummaryListWrapperDTO();

      List<String> enterpriseIdsList;

      if (enterpriseIds != null && enterpriseIds.length > 0) {
        enterpriseIdsList = Arrays.asList(enterpriseIds);
      } else {
        enterpriseIdsList = authorizationService.getEnterpriseAccessStringListForCurrentUser();
      }

      callDetailRepository.setResultFormat();
      List<CallDetailSummaryDTO> pageList = callDetailRepository.getCustomCallDetailsPageList(answerStatus, externalOnly, direction, callType, userType, startDate, endDate, enterpriseIdsList, userNumber, groupNumber, groupBy, null);
      List<CallDetailDTO> callDetailDTOList = callDetailRepository.getCustomCallDetails(
          answerStatus, externalOnly, direction, callType, userType, startDate, endDate,
          enterpriseIdsList, userNumber, groupNumber, groupBy);

      List<CallDetailSummaryDTO> summaryList = createCallDetailSummaryList(pageList, callDetailDTOList,
          groupBy, true);

      callDetails.setCallDetails(summaryList);

      return ApiResponse.create(callDetails);

    } catch (Exception e) {
      String message = String.format(e.getMessage());
      logger.error(message);
      return ApiResponse.create(e);
    }
  }

  public File processDownloadReport(String answerStatus,
      String externalOnly, String direction, String callType, String userType,
      ZonedDateTime startDate, ZonedDateTime endDate, List<String> userNumber,
      List<String> groupNumber, String groupBy, String[] enterpriseIds, String listType, String format, String fileName, String timeZone) {

    try {
      String newFileName = fileName + "." + format;
      File file;
      List<String> enterpriseIdsList;

      if (enterpriseIds != null && enterpriseIds.length > 0) {
        enterpriseIdsList = Arrays.asList(enterpriseIds);
      } else {
        enterpriseIdsList = authorizationService.getEnterpriseAccessStringListForCurrentUser();
      }

      callDetailRepository.setResultFormat();
      List<CallDetailSummaryDTO> pageList = callDetailRepository.getCustomCallDetailsPageList(answerStatus, externalOnly, direction, callType, userType, startDate, endDate, enterpriseIdsList, userNumber, groupNumber, groupBy, null);
      if (groupBy.equals("group")) {
        for (CallDetailSummaryDTO callDetailSummaryDTO : pageList) {
          callDetailSummaryDTO.setUserName("");
          callDetailSummaryDTO.setUserType("");
          callDetailSummaryDTO.setUserNumber("");
        }
      }
      List<CallDetailDTO> callDetailDTOList = callDetailRepository.getCustomCallDetails(
          answerStatus, externalOnly, direction, callType, userType, startDate, endDate,
          enterpriseIdsList, userNumber, groupNumber, groupBy);

      if (Objects.equals(listType, "summary")) {

        List<CallDetailSummaryDTO> summaryList = createCallDetailSummaryList(pageList, callDetailDTOList, groupBy, false);

        switch (format) {
          case "pdf" -> {
            PDFService<CallDetailSummaryDTO> pdfService = new PDFService<>();
            file = pdfService.generatePdf(summaryList, tempDir, newFileName, false, timeZone);
          }
          case "xlsx" -> {
            XLSXService<CallDetailSummaryDTO> xlsxService = new XLSXService<>();
            file = xlsxService.generateXlxs(summaryList, tempDir, newFileName, false, timeZone);
          }
          default -> {
            CSVService<CallDetailSummaryDTO> csvService = new CSVService<>();
            file = csvService.generateCsv(summaryList, tempDir, newFileName, timeZone);
          }
        }
      } else if(Objects.equals(listType, "details")) {

        switch (format) {
          case "pdf" -> {
            PDFService<CallDetailDTO> pdfService = new PDFService<>();
            file = pdfService.generatePdf(callDetailDTOList, tempDir, newFileName, false, timeZone);
          }
          case "xlsx" -> {
            XLSXService<CallDetailDTO> xlsxService = new XLSXService<>();
            file = xlsxService.generateXlxs(callDetailDTOList, tempDir, newFileName, false, timeZone);
          }
          default -> {
            CSVService<CallDetailDTO> csvService = new CSVService<>();
            file = csvService.generateCsv(callDetailDTOList, tempDir, newFileName, timeZone);
          }
        }
      } else {

        List<CallDetailSummaryDTO> summaryList = createCallDetailSummaryList(pageList, callDetailDTOList, groupBy, true);

        switch (format) {
          case "pdf" -> {
            PDFService<CallDetailSummaryDTO> pdfService = new PDFService<>();
            file = pdfService.generatePdf(summaryList, tempDir, newFileName, true, timeZone);
          }
          case "xlsx" -> {
            XLSXService<CallDetailSummaryDTO> xlsxService = new XLSXService<>();
            file = xlsxService.generateXlxs(summaryList, tempDir, newFileName, true, timeZone);
          }
          default -> {
            JSONService<CallDetailSummaryListWrapperDTO> jsonService = new JSONService<>(objectMapper);
            file = jsonService.generateJSON(new CallDetailSummaryListWrapperDTO(summaryList), tempDir,
                fileName);
          }
        }
      }

      return file;
    } catch (Exception e) {
      String message = String.format(e.getMessage());
      logger.error(message);
      return null;
    }
  }

  private List<CallDetailSummaryDTO> createCallDetailSummaryList(List<CallDetailSummaryDTO> pageList,
      List<CallDetailDTO> rawCallDetailDTOS, String groupBy, boolean includeList) {

    for (CallDetailSummaryDTO value : pageList) {
      if (includeList) {
        switch (groupBy) {
          case "user" -> {
            value.setCallDetailDTOList(rawCallDetailDTOS.stream()
                .filter(
                    callDetailDTO -> callDetailDTO.getUserNumber().equals(value.getUserNumber()))
                .toList());
          }
          case "group" -> {
            value.setCallDetailDTOList(rawCallDetailDTOS.stream().filter(
                    callDetailDTO -> callDetailDTO.getGroupNumber().equals(value.getGroupNumber()))
                .toList());
          }
        }
      }
      value.setInboundCallsAverageDuration(Math.round((value.getInboundCallsAverageDuration() * 100.0) / 100.0));
      value.setOutboundCallsAverageDuration(Math.round((value.getOutboundCallsAverageDuration() * 100.0) / 100.0));
      value.setTotalCallsAverageDuration(Math.round((value.getTotalCallsAverageDuration() * 100.0) / 100.0));
    }

    return pageList;
  }

}
