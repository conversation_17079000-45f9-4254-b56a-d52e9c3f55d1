package net.evolveip.ossmosis.api.features.platform.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.enterprise.repository.EnterpriseRepository;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformRequestDTO;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformResponseDTO;
import net.evolveip.ossmosis.api.features.platform.entity.Platform;
import net.evolveip.ossmosis.api.features.platform.mapper.PlatformMapper;
import net.evolveip.ossmosis.api.features.platform.repository.PlatformRepository;
import net.evolveip.ossmosis.api.utils.ReferencedWarning;
import net.evolveip.ossmosis.api.utils.exceptions.DuplicateFoundException;
import net.evolveip.ossmosis.api.utils.exceptions.NotFoundException;
import net.evolveip.ossmosis.api.utils.exceptions.ReferencedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class PlatformService {

  private final PlatformRepository platformRepository;
  private final PlatformMapper mapper;
  private final EnterpriseRepository enterpriseRepository;


  @Autowired
  public PlatformService(PlatformMapper mapper, PlatformRepository platformRepository,
      EnterpriseRepository enterpriseRepository) {
    this.mapper = mapper;
    this.platformRepository = platformRepository;
    this.enterpriseRepository = enterpriseRepository;
  }

  /***
   * It returns all platforms stored in the database with filters and pagination
   * @return a Page containing the list of platform DTOs
   */
  public Page<PlatformResponseDTO> processGet(String filterValue, final Pageable pageable) {

    String filterValueLowerCase = filterValue.toLowerCase();
    long totalElements = platformRepository.findFilteredCount(filterValueLowerCase);
    Page<Platform> page = platformRepository.findAllByFilteredPage(filterValueLowerCase, pageable);

    return new PageImpl<>(
        page.getContent().stream().map(mapper::toPlatformDTO).collect(Collectors.toList()),
        pageable, totalElements);
  }

  /***
   * Returns a list of all platforms in the database
   * @return a list of platform DTOs
   */
  public List<PlatformResponseDTO> processGetList() {
    List<Platform> platforms = platformRepository.findAll();
    if (platforms.isEmpty()) {
      return new ArrayList<>();
    }
    return mapper.entitiesToDTOs(platforms);
  }

  /***
   * Looks up a platform by its id
   * @param platformId the platform id to be looked up in the database.
   * @return a platform identified by platform id.
   */
  @Transactional(readOnly = true)
  public PlatformResponseDTO processGetById(Integer platformId) {
    Platform platform = platformRepository.findByPlatformId(platformId)
        .orElseThrow(() -> new NotFoundException("platformId [" + platformId + "] not found"));
    return mapper.toPlatformDTO(platform);
  }

  /***
   * Looks up a platform by its id
   * @param platformRequestDTO the platform request DTO to be looked up in the database.
   * @return a platform identified by platform id.
   */
  public PlatformResponseDTO processGetById(PlatformRequestDTO platformRequestDTO) {
    return processGetById(platformRequestDTO.getPlatformId());
  }

  /***
   * Creates a platform based on a platform request dto
   * @param platformRequestDTO the platform request dto that contains the details
   * @return a platform response dto
   */
  @Transactional
  public PlatformResponseDTO processCreate(PlatformRequestDTO platformRequestDTO) {
    Platform candidatePlatform = mapper.toPlatform(platformRequestDTO);
    validateDuplicateName(candidatePlatform);
    Platform persistedPlatform = platformRepository.saveAndFlush(candidatePlatform);
    return mapper.toPlatformDTO(persistedPlatform);
  }

  /***
   * Updates a platform via the incoming dto, that contains the platform details
   * @param platformRequestDTO the new platform details
   * @return an updated platform
   */
  @Modifying
  @Transactional
  public PlatformResponseDTO processUpdate(PlatformRequestDTO platformRequestDTO) {
    Platform candidatePlatform = mapper.toPlatform(platformRequestDTO);
    Integer candidatePlatformId = candidatePlatform.getPlatformId();
    Platform existingPlatform = platformRepository.findByPlatformId(candidatePlatformId)
        .orElseThrow(
            () -> new NotFoundException("platformId [" + candidatePlatformId + "] not found"));

    checkIfCandidatePlatformNameIsAssignedToAnotherPlatformId(candidatePlatform);

    existingPlatform.setPlatformName(candidatePlatform.getPlatformName());
    Platform persistedPlatform = platformRepository.saveAndFlush(existingPlatform);
    return mapper.toPlatformDTO(persistedPlatform);
  }

  /***
   * Deletes a platform based on the incoming platformId
   * @param platformId the platformId to be deleted
   */
  @Modifying
  @Transactional
  public void processDelete(int platformId) {
    validatePlatformId(platformId);
    Platform platform = platformRepository.findById(platformId).get();
    final ReferencedWarning referencedWarning = getReferencedWarning(platform);

    if (referencedWarning != null) {
      throw new ReferencedException(referencedWarning);
    } else {
      logger.info("Deleting platformId: {}", platformId);
      platformRepository.deleteById(platformId);
    }
  }

  public void validatePlatformId(Integer platformId) {
    if (platformId == null) {
      throw new NotFoundException("platformId cannot be null or blank");
    }
    if (!isPlatformIdValid(platformId)) {
      throw new NotFoundException("platformId not found: %s".formatted(platformId));
    }
  }

  // Private methods section:
  private void validateDuplicateName(Platform candidatePlatform) {
      Integer candidatePlatformId = candidatePlatform.getPlatformId();
      String candidatePlatformName = candidatePlatform.getPlatformName();

      Optional<Platform> platformWithSameName = platformRepository.findByPlatformName(candidatePlatformName);
      if (platformWithSameName.isPresent() && (candidatePlatformId == null || !platformWithSameName.get().getPlatformId().equals(candidatePlatformId))) {
          String message = "Platform with name [" + candidatePlatformName + "] already exists";
          logger.info(message);
          throw new DuplicateFoundException(message);
      }
  }

  private boolean isPlatformIdValid(Integer platformId) {
    return platformRepository.existsById(platformId);
  }

  private void checkIfCandidatePlatformNameIsAssignedToAnotherPlatformId(Platform candidatePlatform) {
    Integer candidatePlatformId = candidatePlatform.getPlatformId();
    String candidatePlatformName = candidatePlatform.getPlatformName();

    Optional<Platform> existingPlatform = platformRepository.findByPlatformName(candidatePlatformName);

    if (existingPlatform.isPresent()) {
      Integer existingPlatformId = existingPlatform.get().getPlatformId();
      if (!existingPlatform.get().getPlatformId().equals(candidatePlatformId)) {
        String message = "platform name [" + candidatePlatformName
            + "] already in use by platform with id [" + existingPlatformId
            + "], cannot use the same name for more than one platform";
        throw new DuplicateFoundException(message);
      }
    }
  }

  private ReferencedWarning getReferencedWarning(final Platform platform) {
    final ReferencedWarning referencedWarning = new ReferencedWarning();
    final List<Enterprise> platformEnterprises = enterpriseRepository.findFirst10ByPlatform(
        platform);
    if (!platformEnterprises.isEmpty()) {
      if (platformEnterprises.size() < 10) {
        referencedWarning.setKey(
            String.format("platformId [%s] in use by %d enterpriseIds: ", platform.getPlatformId(),
                platformEnterprises.size()));
      } else {
        referencedWarning.setKey(
            String.format("platformId [%s] in use by ten or more enterpriseIds: ",
                platform.getPlatformId()));
      }
      List<String> enterpriseIds = platformEnterprises.stream().map(Enterprise::getEnterpriseId)
          .toList();
      referencedWarning.addParam(enterpriseIds);
      return referencedWarning;
    }
    return null;
  }
}