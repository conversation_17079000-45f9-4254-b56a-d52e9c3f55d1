package net.evolveip.ossmosis.api.features.login.controller;

import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.CHANGE_LOGIN_ENTERPRISE_PATH_VALUE;
import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.REQUEST_MULTIPLE_PATH_VALUE;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import java.util.List;
import net.evolveip.ossmosis.api.features.login.constant.LoginConstants;
import net.evolveip.ossmosis.api.features.login.dto.LoginEnterpriseUpdateRequestDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginRequestDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginResponseDTO;
import net.evolveip.ossmosis.api.features.login.service.LoginService;
import net.evolveip.ossmosis.api.features.role.constant.RoleConstants;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/enterprise/{enterpriseId}/logins")
@Validated
public class LoginController {

  private final LoginService loginService;

  @Autowired
  public LoginController(LoginService loginService) {
    this.loginService = loginService;
  }

  @Secured(LoginConstants.LOGINS_READ)
  @GetMapping
  public ResponseEntity<Page<LoginResponseDTO>> doGet(
      @PathVariable String enterpriseId,
      @RequestParam(value = "filterValue", required = false, defaultValue = "") String filterValue,
      @SortDefault(sort = "loginEmail", direction = Direction.ASC) @PageableDefault(size = 15) final Pageable pageable
  ) {
    Page<LoginResponseDTO> page = loginService.processGet(enterpriseId, filterValue, pageable);
    return ResponseEntity.ok(page);
  }

  @Secured(LoginConstants.LOGINS_READ)
  @GetMapping("/list")
  public ResponseEntity<ApiResponse<List<LoginResponseDTO>>> doGetListByEnterpriseId(@PathVariable String enterpriseId) {
    ApiResponse<List<LoginResponseDTO>> apiResponse = loginService.processGetListByEnterpriseId(enterpriseId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(LoginConstants.LOGINS_READ)
  @GetMapping("/id/{loginId}")
  public ResponseEntity<ApiResponse<LoginResponseDTO>> doGetById(@PathVariable String enterpriseId,
      @PathVariable Long loginId) {
    ApiResponse<LoginResponseDTO> apiResponse = loginService.processGetById(enterpriseId, loginId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(LoginConstants.LOGINS_READ)
  @GetMapping("/email/{loginEmail}")
  public ResponseEntity<ApiResponse<LoginResponseDTO>> doGetByEmail(
      @PathVariable String enterpriseId,
      @PathVariable @Email String loginEmail) {
    ApiResponse<LoginResponseDTO> apiResponse = loginService.processGetByEmail(enterpriseId,
        loginEmail);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(LoginConstants.LOGINS_CREATE)
  @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<ApiResponse<LoginResponseDTO>> doCreate(@PathVariable String enterpriseId,
      @Valid @RequestBody LoginRequestDTO loginDTO) {
    ApiResponse<LoginResponseDTO> apiResponse = loginService.processCreateLogin(loginDTO,
        enterpriseId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(LoginConstants.LOGINS_CREATE)
  @PostMapping(value = REQUEST_MULTIPLE_PATH_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<ApiResponse<List<LoginResponseDTO>>> createMultipleLogins(
      @PathVariable String enterpriseId,
      @Valid @RequestBody List<LoginRequestDTO> loginDTOs) {
    ApiResponse<List<LoginResponseDTO>> apiResponse = loginService.processCreateLogins(loginDTOs,
        enterpriseId);
    return new ResponseEntity<>(apiResponse,
        HttpStatusCode.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(LoginConstants.LOGINS_UPDATE)
  @PutMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<ApiResponse<LoginResponseDTO>> updateLogin(
      @PathVariable String enterpriseId,
      @Valid @RequestBody LoginRequestDTO loginDTO) {
    ApiResponse<LoginResponseDTO> apiResponse = loginService.updateLogin(loginDTO, enterpriseId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(LoginConstants.LOGINS_UPDATE)
  @PutMapping(value = REQUEST_MULTIPLE_PATH_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<ApiResponse<List<LoginResponseDTO>>> updateMultipleLogins(
      @PathVariable String enterpriseId,
      @Valid @RequestBody List<LoginRequestDTO> loginDTO) {
    ApiResponse<List<LoginResponseDTO>> apiResponse = loginService.updateLogins(loginDTO,
        enterpriseId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(RoleConstants.TOOLBOX_FULL_ACCESS)
  @PutMapping(value = CHANGE_LOGIN_ENTERPRISE_PATH_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<ApiResponse<LoginResponseDTO>> updateLoginEnterprise(
      @PathVariable String enterpriseId,
      @Valid @RequestBody LoginEnterpriseUpdateRequestDTO request) {
    ApiResponse<LoginResponseDTO> apiResponse = loginService.updateLoginEnterprise(request.loginId,
        request.currentEnterpriseId, request.newEnterpriseId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(LoginConstants.LOGINS_DELETE)
  @DeleteMapping(value = "/{loginId}")
  public ResponseEntity<ApiResponse<Boolean>> doDelete(@PathVariable String enterpriseId,
      @PathVariable Long loginId) {
    ApiResponse<Boolean> apiResponse = loginService.processDeleteByLoginId(loginId, enterpriseId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }
}
