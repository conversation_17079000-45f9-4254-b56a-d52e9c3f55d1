package net.evolveip.ossmosis.api.features.platform.dto;

import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_BLANK_VALIDATION_MESSAGE;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import net.evolveip.ossmosis.api.features.platform.constant.PlatformConstants;
import net.evolveip.ossmosis.api.features.platform.constant.PlatformCredentialConstants;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PlatformCredentialRequestDTO {

  @NotNull
  private Integer platformCredentialId;

  @NotBlank(message = "hostname" + NOT_BLANK_VALIDATION_MESSAGE)
  @Length(max = PlatformConstants.PLATFORM_CRED_MAX_LEN_100)
  private String hostname;

  @Length(max = PlatformConstants.PLATFORM_CRED_MAX_LEN_100)
  private String username;

  @Length(max = PlatformConstants.PLATFORM_CRED_MAX_LEN_100)
  private String password;

  @Positive(message = "Port must be positive")
  @Max(value = 99999, message = "Port value must be less than 99999.")
  @Min(value = 1, message = "Port value must be greater than 0.")
  private Integer port;

  @NotNull(message = "Timeout is required")
  @Max(value = 600000, message = "Timeout value must be less than 600000.")
  @Min(value = 1, message = "Timeout value must be greater than 0.")
  private Integer timeout;

  @PositiveOrZero(message = "Requests per second must be zero or positive")
  private Integer requestsPerSecond;

  @NotNull
  private Boolean enabled;

  @NotNull(message = "Type is required")
  @Pattern(regexp = PlatformCredentialConstants.BROADSOFT_AS_CRED_TYPE + "|" + 
                    PlatformCredentialConstants.BROADSOFT_NS_CRED_TYPE + "|" + 
                    PlatformCredentialConstants.DUBBER_CRED_TYPE + "|" + 
                    PlatformCredentialConstants.REDSKY_CRED_TYPE + "|" + 
                    PlatformCredentialConstants.WEBEX_CRED_TYPE)
  private String type;

  @NotNull(message = "Platform ID is required")
  private Integer platformId;

  // Map to store any additional fields specific to credential types
  private Map<String, Object> extra;
}
