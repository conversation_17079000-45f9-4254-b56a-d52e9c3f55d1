package net.evolveip.ossmosis.api.features.resource.controller;


import java.util.List;
import net.evolveip.ossmosis.api.features.resource.dto.ResourceViewResponseDTO;
import net.evolveip.ossmosis.api.features.resource.service.ResourceService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/resources")
public class ResourceController {

  private final ResourceService resourceService;


  @Autowired
  public ResourceController(ResourceService resourceService) {
    this.resourceService = resourceService;
  }


  @GetMapping
  public ResponseEntity<ApiResponse<List<ResourceViewResponseDTO>>> getResources() {
    ApiResponse<List<ResourceViewResponseDTO>> apiResponse = this.resourceService.getResources();
    return new ResponseEntity<>(apiResponse,
        HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }
}
