package net.evolveip.ossmosis.api.features.role.dto;


import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_NULL_VALIDATION_MESSAGE;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RoleResourcePermissionRequestDTO {

  @NotNull(message = "resourceId" + NOT_NULL_VALIDATION_MESSAGE)
  private Integer resourceId;

  @NotNull(message = "permissionId" + NOT_NULL_VALIDATION_MESSAGE)
  private Integer permissionId;

}
