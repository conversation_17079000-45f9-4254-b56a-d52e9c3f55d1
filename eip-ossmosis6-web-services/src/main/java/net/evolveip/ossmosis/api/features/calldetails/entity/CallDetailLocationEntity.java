package net.evolveip.ossmosis.api.features.calldetails.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Table(name = "LERGDIMENSION")
@NoArgsConstructor
@AllArgsConstructor
public class CallDetailLocationEntity {

  @Id
  @Column(name = "LD_NPANXX")
  private Long npanxx;

  @Column(name = "LD_CITY")
  private String city;

  @Column(name = "LD_STATE")
  private String state;
}
