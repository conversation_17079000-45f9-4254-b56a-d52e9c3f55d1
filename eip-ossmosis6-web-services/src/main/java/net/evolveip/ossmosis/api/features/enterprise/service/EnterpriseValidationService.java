package net.evolveip.ossmosis.api.features.enterprise.service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.common.ValidationService;
import net.evolveip.ossmosis.api.features.common.ValidationContext;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseEmptyViewResponseDTO;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseRequestDTO;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.enterprise.repository.EnterpriseRepository;
import net.evolveip.ossmosis.api.utils.exceptions.DuplicateFoundException;
import net.evolveip.ossmosis.api.utils.exceptions.NotFoundException;
import net.evolveip.ossmosis.api.utils.service.AuthorizationService;
import net.evolveip.ossmosis.api.utils.validators.EnterpriseIdValidator;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class EnterpriseValidationService implements
    ValidationService<EnterpriseRequestDTO>,
    EnterpriseValidationMethods {

  private static final String ENTERPRISE_ID_FORMAT_MESSAGE = "%s [%s] format is not valid. It must be less than 30 characters long and not contain any of the following special characters: %% + / ; : \\ # ' \"";
  private static final String ENTERPRISE_ID_NOT_FOUND = "enterpriseId not found: %s";
  private static final String ENTERPRISE_NAME_NOT_FOUND = "enterpriseName not found: %s";
  private static final String BILLING_ID_IN_USE = "Cannot create enterpriseId [%s] with a billingId [%s], because billingId is already in use";
  private static final String ENTERPRISE_ID_EXISTS_DIFFERENT_PLATFORM = "candidateEnterpriseId [%s] exists, cannot be created with a new candidatePlatformId [%s]";
  private static final String ENTERPRISE_NAME_EXISTS = "candidateEnterpriseName [%s] already exists under a different enterpriseId";
  private static final String ACCESS_DENIED_PARENT = "Access denied to parent enterprise of candidateEnterpriseId: %s";
  private static final String ACCESS_DENIED_TOP_LEVEL = "Access Denied. Cannot move top level enterprise: %s";
  private static final String PARENT_IS_CHILD = "Cannot move enterprise to parent due to parent enterprise being a child of enterprise: %s";

  private final EnterpriseRepository enterpriseRepository;
  private final AuthorizationService authorizationService;
  private final EnterpriseIdValidator enterpriseIdValidator;

  public EnterpriseValidationService(EnterpriseRepository enterpriseRepository,
      AuthorizationService authorizationService) {
    this.enterpriseRepository = enterpriseRepository;
    this.authorizationService = authorizationService;
    this.enterpriseIdValidator = new EnterpriseIdValidator();
  }

  @Override
  public void validate(EnterpriseRequestDTO dto, ValidationContext context) {
    validateIdFormats(dto);

    switch (context) {
      case CREATE -> validateCreateContext(dto);
      case UPDATE -> validateUpdateContext(dto);
      case DELETE -> validateDeleteContext(dto);
      default -> throw new IllegalArgumentException("Unhandled validation context: " + context);
    }
  }

  private void validateIdFormats(EnterpriseRequestDTO dto) {
    validateIdFormat(dto.getEnterpriseId(), "enterpriseId");
    validateIdFormat(dto.getBillingId(), "billingId");
  }

  private void validateIdFormat(String id, String fieldName) {
    if (!enterpriseIdValidator.isValid(id, null)) {
      throw new IllegalArgumentException(String.format(ENTERPRISE_ID_FORMAT_MESSAGE, fieldName, id));
    }
  }

  private void validateCreateContext(EnterpriseRequestDTO dto) {
    validateEnterpriseId(dto.getParentEnterpriseId());
    checkIfBillingIdAlreadyInUse(dto.getEnterpriseId(), dto.getBillingId(), ValidationContext.CREATE);
    checkIfEnterpriseIdExistsWithADifferentPlatformId(dto.getEnterpriseId(), dto.getPlatformId());
    checkIfEnterpriseNameExistsWithADifferentEnterpriseId(dto.getEnterpriseId(), dto.getEnterpriseName());
    userCanAssignParentIdForEnterprise(dto.getEnterpriseId(), dto.getParentEnterpriseId());
  }

  private void validateUpdateContext(EnterpriseRequestDTO dto) {
    checkIfCurrentUserHasAccessToEnterpriseId(dto.getEnterpriseId());
    validateEnterpriseId(dto.getEnterpriseId());
    validateEnterpriseId(dto.getParentEnterpriseId());
    userCanAssignParentIdForEnterprise(dto.getEnterpriseId(), dto.getParentEnterpriseId());
    checkIfCanMoveEnterpriseToTopLevel(dto.getEnterpriseId(), dto.getParentEnterpriseId());
    checkIfEnterpriseIdAlreadyAChildEnterpriseId(dto.getEnterpriseId(), dto.getParentEnterpriseId());
    checkIfBillingIdAlreadyInUse(dto.getEnterpriseId(), dto.getBillingId(), ValidationContext.UPDATE);
  }

  private void validateDeleteContext(EnterpriseRequestDTO dto) {
    validateEnterpriseId(dto.getEnterpriseId());
    checkIfCurrentUserHasAccessToEnterpriseId(dto.getEnterpriseId());
  }

  @Override
  public Boolean isEnterpriseIdValid(String enterpriseId) {
    return enterpriseRepository.existsById(enterpriseId);
  }

  @Override
  public void validateEnterpriseId(String enterpriseId) {
    if (enterpriseId == null || enterpriseId.isEmpty()) {
      throw new NotFoundException("enterpriseId cannot be null or blank");
    }
    if (!isEnterpriseIdValid(enterpriseId)) {
      throw new NotFoundException(String.format(ENTERPRISE_ID_NOT_FOUND, enterpriseId));
    }
  }

  @Override
  public Boolean isEnterpriseNameValid(String enterpriseName) {
    return enterpriseRepository.findEnterpriseByEnterpriseName(enterpriseName).isPresent();
  }

  @Override
  public void validateEnterpriseName(String enterpriseName) {
    if (enterpriseName == null || enterpriseName.isEmpty()) {
      throw new NotFoundException("enterpriseName cannot be null or blank");
    }
    if (!isEnterpriseIdValid(enterpriseName)) {
      throw new NotFoundException(String.format(ENTERPRISE_NAME_NOT_FOUND, enterpriseName));
    }
  }

  @Override
  public void checkIfCurrentUserHasAccessToEnterpriseId(String enterpriseId) {
    authorizationService.checkIfCurrentUserHasAccessToEnterpriseId(enterpriseId);
  }

  @Override
  public void checkIfBillingIdAlreadyInUse(String enterpriseId, String billingId,
      ValidationContext context) {
    String message = String.format(BILLING_ID_IN_USE, enterpriseId, billingId);
    if (context == ValidationContext.CREATE) {
      if (enterpriseRepository.countByBillingId(billingId) > 0) {
        throw new DuplicateFoundException(message);
      }
    }
    if (context == ValidationContext.UPDATE) {
      if (enterpriseRepository.countByBillingId(billingId, enterpriseId) > 0) {
        throw new DuplicateFoundException(message);
      }
    }
  }

  @Override
  public void checkIfEnterpriseIdExistsWithADifferentPlatformId(String candidateEnterpriseId,
      Integer candidatePlatformId) {
    Optional<Enterprise> existingEnterpriseById = enterpriseRepository.findEnterpriseByEnterpriseId(
        candidateEnterpriseId);
    if (existingEnterpriseById.isPresent()) {
      Integer existingPlatformId = existingEnterpriseById.get().getPlatform().getPlatformId();
      if (!Objects.equals(existingPlatformId, candidatePlatformId)) {
        String message = String.format(ENTERPRISE_ID_EXISTS_DIFFERENT_PLATFORM, candidateEnterpriseId,
            candidatePlatformId);
        throw new DuplicateFoundException(message);
      }
    }
  }

  @Override
  public void checkIfEnterpriseNameExistsWithADifferentEnterpriseId(String candidateEnterpriseId,
      String candidateEnterpriseName) {
    Optional<Enterprise> existingEnterpriseByName = enterpriseRepository.findEnterpriseByEnterpriseName(
        candidateEnterpriseName);
    if (existingEnterpriseByName.isPresent() &&
        !existingEnterpriseByName.get().getEnterpriseId().equals(candidateEnterpriseId)) {
      String message = String.format(ENTERPRISE_NAME_EXISTS, candidateEnterpriseName);
      throw new DuplicateFoundException(message);
    }
  }

  @Override
  public void userCanAssignParentIdForEnterprise(String candidateEnterpriseId,
      String candidateParentEnterpriseId) {
    List<EnterpriseEmptyViewResponseDTO> enterprises = authorizationService.getEnterpriseAccessListForLoggedInUser();

    for (EnterpriseEmptyViewResponseDTO enterprise : enterprises) {
      // allow assigment of the parent candidateEnterpriseId of the main enterprise
      // for the login and
      // enterprises beneath in the tree
      String enterpriseId = enterprise.getEnterpriseId();
      String parentEnterpriseId = enterprise.getParentEnterpriseId();

      // If the request parent ID matches the current enterprise's ID
      // or if the logged-in user is associated with the primary enterprise and the
      // current enterprise's parent ID matches the request, exit the check.
      if (enterpriseId.equals(candidateParentEnterpriseId) || (candidateEnterpriseId.equals(
          authorizationService.getLoggedInUserInfo().getLoginResponseDTO()
              .getLoginPrimaryEnterpriseId())
          && parentEnterpriseId.equals(
              candidateParentEnterpriseId))) {
        return;
      }
    }
    String message = String.format(ACCESS_DENIED_PARENT, candidateEnterpriseId);
    throw new AccessDeniedException(message);
  }

  @Override
  public void checkIfCanMoveEnterpriseToTopLevel(String candidateEnterpriseId,
      String candidateParentEnterpriseId) {
    if (authorizationService.getLoggedInUserInfo().getLoginResponseDTO()
        .getLoginPrimaryEnterpriseId().equals(candidateEnterpriseId)
        && !authorizationService.getCurrentUserEnterprise().getParentEnterpriseId()
            .equals(candidateParentEnterpriseId)) {
      String message = String.format(ACCESS_DENIED_TOP_LEVEL, candidateEnterpriseId);
      throw new AccessDeniedException(message);
    }
  }

  @Override
  public void checkIfEnterpriseIdAlreadyAChildEnterpriseId(String candidateEnterpriseId,
      String candidateParentEnterpriseId) {
    if (enterpriseRepository.isParentEnterpriseAlreadyChild(
        candidateEnterpriseId, candidateParentEnterpriseId)) {
      String message = String.format(PARENT_IS_CHILD, candidateEnterpriseId);
      throw new AccessDeniedException(message);
    }
  }

  @Override
  public void checkIfEnterpriseIdOrBillingIdFormatIsValid(String id, String message) {
    if (!enterpriseIdValidator.isValid(id, null)) {
      throw new IllegalArgumentException(message);
    }
  }
}