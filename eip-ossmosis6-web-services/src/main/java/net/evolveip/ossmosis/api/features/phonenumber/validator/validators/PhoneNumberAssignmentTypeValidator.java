package net.evolveip.ossmosis.api.features.phonenumber.validator.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Arrays;
import net.evolveip.ossmosis.api.features.phonenumber.common.enums.PhoneNumberAssignmentType;
import net.evolveip.ossmosis.api.features.phonenumber.validator.annotations.ValidEnumPhoneNumberAssignmentType;

public class PhoneNumberAssignmentTypeValidator implements ConstraintValidator<ValidEnumPhoneNumberAssignmentType, String> {

  private Class<? extends Enum<?>> enumClass;

  @Override
  public void initialize(ValidEnumPhoneNumberAssignmentType constraintAnnotation) {
    enumClass = constraintAnnotation.enumClass();
  }

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {
    if (value == null || enumClass == null) {
      return false;
    }

    return Arrays.stream(enumClass.getEnumConstants())
        .anyMatch(e -> {
          if (e instanceof PhoneNumberAssignmentType) {
            return ((PhoneNumberAssignmentType) e).getAssignmentType().equals(value);
          }
          return false;
        });
  }
}
