package net.evolveip.ossmosis.api.scheduler.validator.validators;


import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidLocalDateTime;

public class LocalDateTimeValidator implements ConstraintValidator<ValidLocalDateTime, String> {

  private String pattern;

  @Override
  public void initialize(ValidLocalDateTime constraintAnnotation) {
    this.pattern = constraintAnnotation.pattern();
  }

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {
    if (value == null || value.isEmpty()) {
      return true; // You can choose to return false if null values are not allowed
    }

    try {
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
      LocalDateTime.parse(value, formatter);
      return true;
    } catch (DateTimeParseException e) {
      return false;
    }
  }
}