package net.evolveip.ossmosis.api.features.platform.controller;


import static net.evolveip.ossmosis.api.features.platform.constant.PlatformConstants.PLATFORMS_CREATE;
import static net.evolveip.ossmosis.api.features.platform.constant.PlatformConstants.PLATFORMS_READ;
import static net.evolveip.ossmosis.api.features.platform.constant.PlatformConstants.PLATFORMS_UPDATE;

import jakarta.validation.Valid;
import java.util.List;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformCredentialRequestDTO;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformCredentialResponseDTO;
import net.evolveip.ossmosis.api.features.platform.service.PlatformCredentialService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "platform/{platformId}/platform-credential")
public class PlatformCredentialController {

  private final PlatformCredentialService platformCredentialService;

  @Autowired
  public PlatformCredentialController(PlatformCredentialService platformCredentialService) {
    this.platformCredentialService = platformCredentialService;
  }

  @Secured(PLATFORMS_READ)
  @GetMapping
  public ResponseEntity<ApiResponse<List<PlatformCredentialResponseDTO>>> doGet(
      @PathVariable Integer platformId) {
    ApiResponse<List<PlatformCredentialResponseDTO>> apiResponse = platformCredentialService.getPlatformCredentials(
        platformId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured({PLATFORMS_CREATE, PLATFORMS_READ})
  @PostMapping()
  public ResponseEntity<ApiResponse<PlatformCredentialResponseDTO>> doCreate(
      @PathVariable int platformId,
      @Valid @RequestBody PlatformCredentialRequestDTO platformCredentialRequestDTO) {
    platformCredentialRequestDTO.setPlatformId(platformId);
    ApiResponse<PlatformCredentialResponseDTO> apiResponse = platformCredentialService.createPlatformCredentials(
        platformCredentialRequestDTO);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured({PLATFORMS_UPDATE, PLATFORMS_READ})
  @PutMapping()
  public ResponseEntity<ApiResponse<PlatformCredentialResponseDTO>> doUpdate(
      @PathVariable int platformId,
      @Valid @RequestBody PlatformCredentialRequestDTO platformCredentialRequestDTO) {
    platformCredentialRequestDTO.setPlatformId(platformId);
    ApiResponse<PlatformCredentialResponseDTO> apiResponse = platformCredentialService.processUpdate(
        platformCredentialRequestDTO);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

}