package net.evolveip.ossmosis.api.scheduler.validator.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidCronExpression;
import org.quartz.CronExpression;

public class CronExpressionValidator implements
    ConstraintValidator<ValidCronExpression, String> {

  @Override
  public void initialize(ValidCronExpression constraintAnnotation) {
  }

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {
    if (value == null || value.isEmpty()) {
      return true;
    }
    return CronExpression.isValidExpression(value);
  }
}