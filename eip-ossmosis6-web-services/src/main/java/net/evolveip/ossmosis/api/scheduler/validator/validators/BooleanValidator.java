package net.evolveip.ossmosis.api.scheduler.validator.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidBoolean;

public class BooleanValidator implements ConstraintValidator<ValidBoolean, String> {

  @Override
  public void initialize(ValidBoolean constraintAnnotation) {
  }

  @Override
  public boolean isValid(String aBoolean, ConstraintValidatorContext constraintValidatorContext) {
    if (aBoolean == null) {
      return false;
    } else
      return "true".equalsIgnoreCase(aBoolean) || "false".equalsIgnoreCase(aBoolean);
  }
}