package net.evolveip.ossmosis.api.features.resource.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import java.time.ZonedDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import net.evolveip.ossmosis.api.features.resource.constant.ResourceConstants;
import org.hibernate.annotations.ColumnDefault;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@Table(name = "resource", uniqueConstraints = {
    @UniqueConstraint(columnNames = { "resource_name" })
})
public class Resource {

  @Id
  @Column(name = "resource_id")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer resourceId;

  @Column(name = "date_created", nullable = false, updatable = false)
  private ZonedDateTime dateCreated;

  @Column(name = "date_updated", nullable = false)
  @ColumnDefault("CURRENT_TIMESTAMP")
  private ZonedDateTime dateUpdated;

  @Column(name = "resource_name", length = ResourceConstants.MAX_RESOURCE_NAME_LENGTH, nullable = false)
  private String resourceName;

  @OneToMany(mappedBy = "resource", fetch = FetchType.LAZY)
  private List<ResourcePermission> assignablePermissions;
}
