package net.evolveip.ossmosis.api.scheduler.controller;

import net.evolveip.ossmosis.api.features.role.constant.RoleConstants;
import net.evolveip.ossmosis.api.scheduler.common.dto.SchedulerJobsStatesDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.SchedulerMetadataDTO;
import net.evolveip.ossmosis.api.scheduler.service.scheduler.SchedulerService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Validated
@RequestMapping(value = "/scheduler")
public class SchedulerController {

  private final SchedulerService schedulerService;

  @Autowired
  public SchedulerController(SchedulerService schedulerService) {
    this.schedulerService = schedulerService;
  }

  @Secured(RoleConstants.TOOLBOX_FULL_ACCESS)
  @PostMapping(value = "/start")
  public ResponseEntity<ApiResponse<SchedulerMetadataDTO>> doStart() {
    ApiResponse<SchedulerMetadataDTO> apiResponse = schedulerService.processStart();
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(RoleConstants.TOOLBOX_FULL_ACCESS)
  @PostMapping(value = "/standby")
  public ResponseEntity<ApiResponse<SchedulerMetadataDTO>> doStandby() {
    ApiResponse<SchedulerMetadataDTO> apiResponse = schedulerService.processStandby();
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(RoleConstants.TOOLBOX_FULL_ACCESS)
  @PostMapping(value = "/pauseall")
  public ResponseEntity<ApiResponse<SchedulerJobsStatesDTO>> doPauseAll() {
    ApiResponse<SchedulerJobsStatesDTO> apiResponse = schedulerService.processPauseAll();
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(RoleConstants.TOOLBOX_FULL_ACCESS)
  @PostMapping(value = "/resumeall")
  public ResponseEntity<ApiResponse<SchedulerJobsStatesDTO>> doResumeAll() {
    ApiResponse<SchedulerJobsStatesDTO> apiResponse = schedulerService.processResumeAll();
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(RoleConstants.TOOLBOX_FULL_ACCESS)
  @GetMapping(value = "/status")
  public ResponseEntity<ApiResponse<SchedulerMetadataDTO>> doStatus() {
    ApiResponse<SchedulerMetadataDTO> apiResponse = schedulerService.processStatus();
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(RoleConstants.TOOLBOX_FULL_ACCESS)
  @GetMapping(value = "/jobsstatus")
  public ResponseEntity<ApiResponse<SchedulerJobsStatesDTO>> doJobsStatus() {
    ApiResponse<SchedulerJobsStatesDTO> apiResponse = schedulerService.processJobsStatus();
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }
}