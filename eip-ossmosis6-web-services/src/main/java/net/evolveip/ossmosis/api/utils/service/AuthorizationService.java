package net.evolveip.ossmosis.api.utils.service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseEmptyViewResponseDTO;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.enterprise.mapper.EnterpriseMapper;
import net.evolveip.ossmosis.api.features.enterprise.repository.EnterpriseRepository;
import net.evolveip.ossmosis.api.features.login.dto.LoginInfoResponseDTO;
import net.evolveip.ossmosis.api.features.login.entity.LoginResourcePermission;
import net.evolveip.ossmosis.api.features.login.entity.view.LoginRoleView;
import net.evolveip.ossmosis.api.features.login.entity.view.LoginView;
import net.evolveip.ossmosis.api.features.login.repository.LoginRepository;
import net.evolveip.ossmosis.api.features.login.repository.LoginResourcePermissionRepository;
import net.evolveip.ossmosis.api.features.role.entity.RoleResourcePermission;
import net.evolveip.ossmosis.api.features.role.repository.RoleResourcePermissionRepository;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AuthorizationService {

  private final LoginRepository loginRepository;
  private final LoginResourcePermissionRepository loginResourcePermissionRepository;
  private final RoleResourcePermissionRepository roleResourcePermissionRepository;
  private final EnterpriseRepository enterpriseRepository;
  private final EnterpriseMapper enterpriseMapper;


  @Autowired
  public AuthorizationService(LoginRepository loginRepository,
      LoginResourcePermissionRepository loginResourcePermissionRepository,
      RoleResourcePermissionRepository roleResourcePermissionRepository,
      EnterpriseRepository enterpriseRepository, EnterpriseMapper enterpriseMapper) {
    this.loginRepository = loginRepository;
    this.loginResourcePermissionRepository = loginResourcePermissionRepository;
    this.roleResourcePermissionRepository = roleResourcePermissionRepository;
    this.enterpriseRepository = enterpriseRepository;
    this.enterpriseMapper = enterpriseMapper;
  }

  public Collection<SimpleGrantedAuthority> getAuthorities(LoginView login) {
    try {
      return getGrantedAuthorities(getPrivileges(login));
    } catch (Exception e) {
      logger.error(e.getMessage());
    }
    return new ArrayList<>();
  }

  private List<String> getPrivileges(LoginView login) {

    List<String> privileges = new ArrayList<>();

    List<RoleResourcePermission> roleResourcePermissions = this.roleResourcePermissionRepository.findAllByRoleIdIn(
        login.getRoles().stream().map(LoginRoleView::getRoleId).toList());
    for (RoleResourcePermission roleResourcePermission : roleResourcePermissions) {
      privileges.add((roleResourcePermission.getResource().getResourceName() + "_"
          + roleResourcePermission.getPermission().getPermissionName()).replaceAll(" ", "_"));
    }
    List<LoginResourcePermission> loginResourcePermissions = this.loginResourcePermissionRepository.findAllByLoginId(
        login.getLoginId());
    for (LoginResourcePermission loginResourcePermission : loginResourcePermissions) {
      privileges.add((loginResourcePermission.getResource().getResourceName() + "_"
          + loginResourcePermission.getPermission().getPermissionName()).replaceAll(" ", "_"));
    }
    return privileges;
  }

  private List<SimpleGrantedAuthority> getGrantedAuthorities(List<String> privileges) {
    List<SimpleGrantedAuthority> authorities = new ArrayList<>();
    for (String privilege : privileges) {
      authorities.add(new SimpleGrantedAuthority(privilege));
    }
    return authorities;
  }


  //this method should only be used in OssmosisJwtAuthenticationConverter
  public List<EnterpriseEmptyViewResponseDTO> getEnterpriseAccessListForCurrentUser(
      LoginView loginView) {
    try {
      List<Enterprise> enterpriseList = this.enterpriseRepository.findEnterprisesByLoginIdForAccess(
          loginView.getLoginId());
      return enterpriseMapper.mapToEmpty(enterpriseList);
    } catch (Exception e) {
      logger.error(e.getMessage());
    }
    return null;
  }

  public List<EnterpriseEmptyViewResponseDTO> getEnterpriseAccessListForLoggedInUser() {
    try {
      return getLoggedInUserInfo().getEnterprises();
    } catch (Exception e) {
      logger.error(e.getMessage());
    }
    return null;
  }

  public LoginInfoResponseDTO getLoggedInUserInfo() {
    LoginInfoResponseDTO response;
    if (SecurityContextHolder.getContext().getAuthentication().getDetails() != null &&
        SecurityContextHolder.getContext().getAuthentication()
            .getDetails() instanceof LoginInfoResponseDTO) {
      response = (LoginInfoResponseDTO) SecurityContextHolder.getContext().getAuthentication()
          .getDetails();
    } else {
      throw new AccessDeniedException("User is not authenticated.");
    }
    return response;
  }

  public ApiResponse<LoginInfoResponseDTO> getLoggedInUserInfoResponse() {
    try {
      return ApiResponse.create(getLoggedInUserInfo());
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }

  public List<String> getEnterpriseAccessStringListForCurrentUser() {
    return getLoggedInUserInfo().getEnterprises().stream().map(
        EnterpriseEmptyViewResponseDTO::getEnterpriseId).collect(
        Collectors.toList());
  }

  public Optional<LoginView> loadUserByUserEmail(String email) {
    logger.info("Load user by userEmail: " + email);
    return this.loginRepository.findLoginByLoginEmailForLoginOnly(email);
  }

  public boolean doesCurrentUserHaveAccessToEnterprise(String enterpriseId) {
    return this.getEnterpriseAccessStringListForCurrentUser().contains(enterpriseId);
  }

  public void checkIfCurrentUserHasAccessToEnterpriseId(String enterpriseId) {
    if (!doesCurrentUserHaveAccessToEnterprise(enterpriseId)){
      throw new AccessDeniedException("Enterprise: " + enterpriseId + " does not exist or you do not have access.");
    }
  }

  public EnterpriseEmptyViewResponseDTO getCurrentUserEnterprise() {
    List<EnterpriseEmptyViewResponseDTO> list = getLoggedInUserInfo().getEnterprises().stream()
        .filter(enterpriseEmptyViewResponseDTO -> enterpriseEmptyViewResponseDTO.getEnterpriseId()
            .equals(getLoggedInUserInfo().getLoginResponseDTO().getLoginPrimaryEnterpriseId()))
        .toList();
    return list.isEmpty() ? null : list.get(0);
  }
}
