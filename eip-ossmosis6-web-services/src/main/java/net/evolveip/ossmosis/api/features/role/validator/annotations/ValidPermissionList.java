package net.evolveip.ossmosis.api.features.role.validator.annotations;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import net.evolveip.ossmosis.api.features.role.validator.validators.PermissionListValidator;

@Constraint(validatedBy = PermissionListValidator.class)
@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidPermissionList {

  String message() default "Empty roles are not allowed. Please assign at least one permission before creating the role.";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
