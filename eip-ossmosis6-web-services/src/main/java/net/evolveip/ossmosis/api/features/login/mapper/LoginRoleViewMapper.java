package net.evolveip.ossmosis.api.features.login.mapper;


import net.evolveip.ossmosis.api.features.login.dto.LoginRoleResponseDTO;
import net.evolveip.ossmosis.api.features.login.entity.view.LoginRoleView;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = ComponentModel.SPRING, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface LoginRoleViewMapper {

  LoginRoleViewMapper INSTANCE = Mappers.getMapper(LoginRoleViewMapper.class);
//
//  @Mapping(source = "roleId", target = "roleId")
//  LoginRoleView toEntity(LoginRoleRequestDTO loginRoleRequestDTO);

  @Mapping(ignore = true, target = "dateCreated")
  @Mapping(ignore = true, target = "dateUpdated")
  @Mapping(ignore = true, target = "loginId")
  @Mapping(source = "roleId", target = "roleId")
  LoginRoleResponseDTO fromEntity(LoginRoleView loginRole);
}