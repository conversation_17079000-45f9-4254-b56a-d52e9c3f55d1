package net.evolveip.ossmosis.api.features.permission.service;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.permission.constant.PermissionConstants;
import net.evolveip.ossmosis.api.features.permission.dto.PermissionResponseDTO;
import net.evolveip.ossmosis.api.features.permission.entity.Permission;
import net.evolveip.ossmosis.api.features.permission.mapper.PermissionMapper;
import net.evolveip.ossmosis.api.features.permission.repository.PermissionRepository;
import net.evolveip.ossmosis.api.utils.exceptions.NotFoundException;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PermissionService {

  private final PermissionRepository permissionRepository;
  private final PermissionMapper permissionMapper;

  @Autowired
  public PermissionService(PermissionRepository PermissionRepository,
      PermissionMapper permissionMapper) {
    this.permissionRepository = PermissionRepository;
    this.permissionMapper = permissionMapper;
  }

  public List<Permission> createDefaultPermissions() {
    List<Permission> existingPermissions = this.permissionRepository.findByPermissionNameIn(
        PermissionConstants.permissionNameList);

    if (existingPermissions.size() != PermissionConstants.permissionNameList.size()) {

      List<Permission> PermissionList = new ArrayList<>();
      for (String PermissionName : PermissionConstants.permissionNameList) {
        if (existingPermissions.stream()
            .noneMatch(Permission -> Permission.getPermissionName().equals(PermissionName))) {
          PermissionList.add(Permission.builder()
              .dateCreated(ZonedDateTime.now())
              .dateUpdated(ZonedDateTime.now())
              .permissionName(PermissionName).build());
        }
      }
      if (!PermissionList.isEmpty()) {
        existingPermissions.addAll(this.permissionRepository.saveAll(PermissionList));
        logger.info("Created default permissions");
      }
    }
    return existingPermissions;
  }

  public Permission getPermissionByPermissionId(Integer permissionId) {
    return permissionRepository.findByPermissionId(permissionId)
        .orElseThrow(() -> new NotFoundException("Permission id not found: " + permissionId));
  }

  public ApiResponse<List<PermissionResponseDTO>> getPermissions() {
    try {
      return ApiResponse.create(
          this.permissionMapper.entitiesToDTOs(this.permissionRepository.findAll()));
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }
}
