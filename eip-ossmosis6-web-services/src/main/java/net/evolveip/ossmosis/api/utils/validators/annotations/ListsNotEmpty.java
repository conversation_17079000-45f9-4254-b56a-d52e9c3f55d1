package net.evolveip.ossmosis.api.utils.validators.annotations;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import net.evolveip.ossmosis.api.utils.validators.ListsNotEmptyValidator;

@Constraint(validatedBy = ListsNotEmptyValidator.class)
@Retention(RUNTIME)
@Target(TYPE)
public @interface ListsNotEmpty {

  String message() default "All lists cannot be empty at the same time";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}