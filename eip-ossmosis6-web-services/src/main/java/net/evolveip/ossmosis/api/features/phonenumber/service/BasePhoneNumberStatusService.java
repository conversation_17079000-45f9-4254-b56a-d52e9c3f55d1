package net.evolveip.ossmosis.api.features.phonenumber.service;

import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.core.BaseGenericService;
import net.evolveip.ossmosis.api.features.phonenumber.common.IdValidator;
import net.evolveip.ossmosis.api.features.phonenumber.entity.PhoneNumberStatus;
import net.evolveip.ossmosis.api.features.phonenumber.mapper.PhoneNumberStatusMapper;
import net.evolveip.ossmosis.api.features.phonenumber.repository.PhoneNumberStatusRepository;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public abstract class BasePhoneNumberStatusService
    extends BaseGenericService
    implements IdValidator<PhoneNumberStatus, Integer> {

  protected final PhoneNumberStatusRepository phoneNumberStatusRepository;
  protected final PhoneNumberStatusMapper mapper;

  @Autowired
  protected BasePhoneNumberStatusService(PhoneNumberStatusRepository phoneNumberStatusRepository,
      PhoneNumberStatusMapper mapper) {
    this.phoneNumberStatusRepository = phoneNumberStatusRepository;
    this.mapper = mapper;
  }

  public Set<Integer> getInvalidIds(Set<Integer> candidateIds) {
    return getInvalidEntries(candidateIds, phoneNumberStatusRepository);
  }

  protected <T> ApiResponse<T> notFoundResponse() {
    String message = "No phone number status found";
    logger.info(message);
    return genericEmptyResponse(message);
  }

  protected <T> ApiResponse<T> notFoundResponse(Integer statusId) {
    String message = "Status ID not found: " + statusId;
    logger.info(message);
    return genericEmptyResponse(message);
  }

  protected <T> ApiResponse<T> idsNotFoundResponse(Set<Integer> statusIds) {
    String message = "Some statusIds not found: " + String.join(", ", statusIds.toString());
    logger.info(message);
    return genericEmptyResponse(message);
  }

  protected <T> ApiResponse<T> notFoundResponse(String statusName) {
    String message = "Status name not found: " + statusName;
    logger.info(message);
    return genericEmptyResponse(message);
  }
}
