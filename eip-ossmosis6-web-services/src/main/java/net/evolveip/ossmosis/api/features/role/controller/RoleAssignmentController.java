package net.evolveip.ossmosis.api.features.role.controller;

import java.util.List;
import jakarta.validation.Valid;
import net.evolveip.ossmosis.api.features.login.service.LoginRoleService;
import net.evolveip.ossmosis.api.features.role.dto.RoleToLoginsRequestDTO;
import net.evolveip.ossmosis.api.features.role.dto.RoleToLoginsResponseDTO;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.validators.annotations.EnterpriseId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Validated
@RequestMapping("/enterprises/{enterpriseId}/roles/{roleId}")
public class RoleAssignmentController {

  private final LoginRoleService loginRoleService;

  @Autowired
  public RoleAssignmentController(LoginRoleService loginRoleService) {
    this.loginRoleService = loginRoleService;
  }

  @PreAuthorize(
      "hasAuthority("
          + "T(net.evolveip.ossmosis.api.features.login.constant.LoginConstants)"
          + ".LOGINS_READ) AND "
          + "hasAuthority("
          + "T(net.evolveip.ossmosis.api.features.role.constant.RoleConstants)"
          + ".ROLES_READ)")
  @GetMapping
  public ResponseEntity<ApiResponse<List<Long>>> doGet(
      @PathVariable Integer roleId, @PathVariable @EnterpriseId String enterpriseId) {
    ApiResponse<List<Long>> apiResponse = loginRoleService.getAssignedLogins(roleId, enterpriseId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @PreAuthorize(
      "hasAuthority("
          + "T(net.evolveip.ossmosis.api.features.login.constant.LoginConstants)"
          + ".LOGINS_UPDATE) AND "
          + "hasAuthority("
          + "T(net.evolveip.ossmosis.api.features.role.constant.RoleConstants)"
          + ".ROLES_UPDATE)")
  @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<ApiResponse<?>> doAssign(@PathVariable @EnterpriseId String enterpriseId,
      @PathVariable Integer roleId, @Valid @RequestBody RoleToLoginsRequestDTO request) {

    RoleToLoginsRequestDTO roleToLoginsRequestDTO = new RoleToLoginsRequestDTO(roleId,
        request.getAssignLoginIds(), request.getRemoveLoginIds());
    ApiResponse<RoleToLoginsResponseDTO> apiResponse = loginRoleService.assignToOrRemoveLoginsFromRole(
        enterpriseId, roleToLoginsRequestDTO);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }
}