package net.evolveip.ossmosis.api.features.phonenumber.service;

import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import java.util.List;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.InvalidPhoneNumberDTO;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.PhoneNumberValidationRequestDTO;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.PhoneNumberValidationResponseDTO;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.ValidPhoneNumberDTO;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.stereotype.Service;

@Service
public class PhoneNumbersValidationService {

  private final PhoneNumberUtil phoneNumberUtil;

  public PhoneNumbersValidationService(PhoneNumberUtil phoneNumberUtil) {
    this.phoneNumberUtil = phoneNumberUtil;
  }

  public ApiResponse<PhoneNumberValidationResponseDTO> processValidateExternalPhoneNumbersList(
      List<PhoneNumberValidationRequestDTO> phoneNumbers) {
    PhoneNumberValidationResponseDTO response = new PhoneNumberValidationResponseDTO();
    for (PhoneNumberValidationRequestDTO candidatePhoneNumber : phoneNumbers) {

      Phonenumber.PhoneNumber phoneNumber = new Phonenumber.PhoneNumber();

      int countryCode;
      try {
        countryCode = Integer.parseInt(candidatePhoneNumber.getCountryCode().substring(1));
      } catch (NumberFormatException e) {
        InvalidPhoneNumberDTO invalidPhoneNumber = InvalidPhoneNumberDTO
            .builder()
            .originalCountryCode(candidatePhoneNumber.getCountryCode())
            .originalPhoneNumber(candidatePhoneNumber.getPhoneNumber())
            .errorMessage("Invalid country code")
            .build();
        response.addInvalidPhoneNumber(invalidPhoneNumber);
        continue;
      }

      long nationalNumber;
      try {
        nationalNumber = Long.parseLong(candidatePhoneNumber.getPhoneNumber());
      } catch (NumberFormatException e) {
        InvalidPhoneNumberDTO invalidPhoneNumber = InvalidPhoneNumberDTO
            .builder()
            .originalCountryCode(candidatePhoneNumber.getCountryCode())
            .originalPhoneNumber(candidatePhoneNumber.getPhoneNumber())
            .errorMessage("Invalid national phone number")
            .build();
        response.addInvalidPhoneNumber(invalidPhoneNumber);
        continue;
      }

      phoneNumber.setCountryCode(countryCode);
      phoneNumber.setNationalNumber(nationalNumber);
      if (phoneNumberUtil.isValidNumber(phoneNumber)) {
        ValidPhoneNumberDTO validPhoneNumber = ValidPhoneNumberDTO
            .builder()
            .phoneNumber(phoneNumber)
            .originalCountryCode(candidatePhoneNumber.getCountryCode())
            .originalPhoneNumber(candidatePhoneNumber.getPhoneNumber())
            .build();
        response.addValidPhoneNumber(validPhoneNumber);
      } else {
        InvalidPhoneNumberDTO invalidPhoneNumber = InvalidPhoneNumberDTO
            .builder()
            .originalCountryCode(candidatePhoneNumber.getCountryCode())
            .originalPhoneNumber(candidatePhoneNumber.getPhoneNumber())
            .errorMessage("Invalid phone number")
            .build();
        response.addInvalidPhoneNumber(invalidPhoneNumber);
      }
    }
    return ApiResponse.create(response);
  }
}