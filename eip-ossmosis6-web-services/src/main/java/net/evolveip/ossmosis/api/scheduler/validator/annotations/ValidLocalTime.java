package net.evolveip.ossmosis.api.scheduler.validator.annotations;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import net.evolveip.ossmosis.api.scheduler.validator.validators.LocalTimeFormatValidator;

@Constraint(validatedBy = LocalTimeFormatValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidLocalTime {

  String message() default "Invalid time format. Must be in 24-hour format (HH:mm:ss)";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
