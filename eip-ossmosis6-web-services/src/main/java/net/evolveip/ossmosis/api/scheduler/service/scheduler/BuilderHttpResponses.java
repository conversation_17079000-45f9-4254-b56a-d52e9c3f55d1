package net.evolveip.ossmosis.api.scheduler.service.scheduler;

import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.ANSWER_STATUS;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.CALL_TYPE;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.DATA_WINDOW;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.DATA_WINDOW_END_TIME;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.DATA_WINDOW_OFFSET;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.DATA_WINDOW_OFFSET_UNITS;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.DATA_WINDOW_UNITS;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.DIRECTION;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.EXTERNAL_ONLY;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.GROUP_BY;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.GROUP_NUMBER;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.REPORT_TITLE;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.USER_NUMBER;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.USER_TYPE;
import static net.evolveip.ossmosis.api.scheduler.common.constants.EmailDefinitionConstants.ENTERPRISE_ID;
import static net.evolveip.ossmosis.api.scheduler.common.constants.EmailDefinitionConstants.ENTERPRISE_NAME;
import static net.evolveip.ossmosis.api.scheduler.common.constants.EmailDefinitionConstants.FROM_EMAIL;
import static net.evolveip.ossmosis.api.scheduler.common.constants.EmailDefinitionConstants.TO_EMAIL;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobDefinitionConstants.CREATED_BY;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobDefinitionConstants.CREATED_TIMESTAMP;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobDefinitionConstants.JOB_TYPE;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobDefinitionConstants.UPDATED_BY;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobDefinitionConstants.UPDATED_TIMESTAMP;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.SCHEDULE_DEFINITION;

import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import net.evolveip.ossmosis.api.scheduler.common.TimeUtils;
import net.evolveip.ossmosis.api.scheduler.common.dto.CallDetailReportDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.EmailDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.JobDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.JobSpecificationDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.TriggerDTO;
import net.evolveip.ossmosis.api.scheduler.common.response.HttpResponseGetScheduledJob;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;

public interface BuilderHttpResponses extends TimeUtils {

  default JobDetail getJobDetail(Scheduler scheduler, JobKey jobKey) throws SchedulerException {
    return scheduler.getJobDetail(jobKey);
  }

  @SuppressWarnings("unchecked")
  default HttpResponseGetScheduledJob buildHttpResponse(Scheduler scheduler, JobKey jobKey,
      boolean triggerState, List<TriggerDTO> triggerDtoList)
      throws SchedulerException {

    JobDetail jobDetail = getJobDetail(scheduler, jobKey);
    JobDataMap jobDataMap = jobDetail.getJobDataMap();

    // build the email definition portion of the response:
    EmailDefinitionDTO emailDefinitionDTO = EmailDefinitionDTO
        .builder()
        .enterpriseId(jobDataMap.getString(ENTERPRISE_ID))
        .enterpriseName(jobDataMap.getString(ENTERPRISE_NAME))
        .fromEmail(jobDataMap.getString(FROM_EMAIL))
        .toEmail((List<String>) jobDataMap.get(TO_EMAIL))
        .build();

    JobDataMap jobScheduleDefinition = (JobDataMap) jobDataMap.get(SCHEDULE_DEFINITION);

    // build the job specification:
    JobSpecificationDTO jobSpecificationDTO = JobSpecificationDTO.builder()
        .jobGroup(jobKey.getGroup()).jobId(jobKey.getName())
        .jobDescription(jobDetail.getDescription())
        .jobRunnerClass(jobDetail.getJobClass().toString())
        .persistJobDataAfterExecution(jobDetail.isPersistJobDataAfterExecution()).isExecuting(
            scheduler.getCurrentlyExecutingJobs().stream()
                .anyMatch(j -> j.getJobDetail().getKey().equals(jobKey)))
        .durable(jobDetail.isDurable()).requestsRecovery(jobDetail.requestsRecovery())
        .concurrentExecutionDisallowed(jobDetail.isConcurrentExectionDisallowed()).build();

    List<String> userNumbers = (List<String>) jobDataMap.get(USER_NUMBER);
    List<String> groupNumbers = (List<String>) jobDataMap.get(GROUP_NUMBER);

    // this DTO is report-specific; eventually needs to be replaced with a generalized mechanism:
    CallDetailReportDTO callDetailReportDTO = CallDetailReportDTO.builder()
        .reportTitle(jobDataMap.getString(REPORT_TITLE))
        .answerStatus(jobDataMap.getString(ANSWER_STATUS))
        .externalOnly(jobDataMap.getString(EXTERNAL_ONLY))
        .direction(jobDataMap.getString(DIRECTION))
        .callType(jobDataMap.getString(CALL_TYPE))
        .userType(jobDataMap.getString(USER_TYPE))
        .userNumbers(userNumbers.isEmpty() ? new ArrayList<>() : userNumbers)
        .groupNumbers(groupNumbers.isEmpty() ? new ArrayList<>() : groupNumbers)
        .groupBy(jobDataMap.getString(GROUP_BY))
        .dataWindowOffset((Integer) jobDataMap.get(DATA_WINDOW_OFFSET))
        .dataWindowOffsetUnits((ChronoUnit) jobDataMap.get(DATA_WINDOW_OFFSET_UNITS))
        .dataWindowEndTime((LocalTime) jobDataMap.get(DATA_WINDOW_END_TIME))
        .dataWindow((Integer) jobDataMap.get(DATA_WINDOW))
        .dataWindowUnits((ChronoUnit) jobDataMap.get(DATA_WINDOW_UNITS))
        .build();

    // build the JobDefinitionDTO:
    JobDefinitionDTO jobDefinitionDTO = JobDefinitionDTO
        .builder()
        .jobType(jobDataMap.getString(JOB_TYPE))
        .enabled(triggerState)
        .createdBy(jobDataMap.getString(CREATED_BY))
        .createdTimestamp(jobDataMap.getString(CREATED_TIMESTAMP))
        .updatedBy(jobDataMap.getString(UPDATED_BY))
        .updatedTimestamp(jobDataMap.getString(UPDATED_TIMESTAMP))
        .jobData(callDetailReportDTO)
        .build();

    return HttpResponseGetScheduledJob.builder()
        .emailDefinition(emailDefinitionDTO)
        .jobScheduleDefinition(jobScheduleDefinition)
        .jobRuntimes(triggerDtoList)
        .jobSpecification(jobSpecificationDTO)
        .jobDefinition(jobDefinitionDTO).build();
  }
}