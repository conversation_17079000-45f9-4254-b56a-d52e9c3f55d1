package net.evolveip.ossmosis.api.config.dao;


import java.util.Comparator;
import java.util.List;
import java.util.Map;
import org.hibernate.boot.Metadata;
import org.hibernate.boot.model.relational.ColumnOrderingStrategyLegacy;
import org.hibernate.cfg.AvailableSettings;
import org.hibernate.mapping.Column;
import org.hibernate.mapping.ForeignKey;
import org.hibernate.mapping.Table;
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OssmosisColumnOrderingStrategy
    extends ColumnOrderingStrategyLegacy
    implements HibernatePropertiesCustomizer {

  @Override
  public List<Column> orderTableColumns(Table table, Metadata metadata) {
    return table.getColumns().stream()
        .sorted(Comparator.comparing((Column c) -> {
          if (table.getPrimaryKey().containsColumn(c)) {
            return "  " + c.getName();
          }
          boolean isFk = false;
          for (ForeignKey foreignKey : table.getForeignKeys().values()) {
            if (foreignKey.containsColumn(c)) {
              isFk = true;
            }
          }
          if (isFk) {
            return " " + c.getName();
          }
          return c.getName();
        }))
        .toList();
  }

  @Override
  public void customize(Map<String, Object> hibernateProperties) {
    hibernateProperties.put(AvailableSettings.COLUMN_ORDERING_STRATEGY, this);
  }

}
