package net.evolveip.ossmosis.api.features.platform.entity;

import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import net.evolveip.ossmosis.api.features.platform.constant.PlatformCredentialConstants;
import org.hibernate.annotations.ColumnTransformer;

@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@DiscriminatorValue(PlatformCredentialConstants.DUBBER_CRED_TYPE)
public class DubberCredential extends PlatformCredential {

  @Column(name = "client_id", length = 100)
  private String clientId;

  @Setter(AccessLevel.NONE)
  @Column(name = "client_secret", columnDefinition = "bytea")
  @ColumnTransformer(
      read = "pgp_sym_decrypt(client_secret, current_setting('var.encryption_key'), 'compress-algo=1, cipher-algo=aes256')",
      write = "pgp_sym_encrypt(?, current_setting('var.encryption_key'), 'compress-algo=1, cipher-algo=aes256')")
  private String clientSecret;

  @Column(name = "account_id", length = 100)
  private String accountId;

  @Setter(AccessLevel.NONE)
  @Column(name = "account_secret", columnDefinition = "bytea")
  @ColumnTransformer(
      read = "pgp_sym_decrypt(account_secret, current_setting('var.encryption_key'), 'compress-algo=1, cipher-algo=aes256')",
      write = "pgp_sym_encrypt(?, current_setting('var.encryption_key'), 'compress-algo=1, cipher-algo=aes256')")
  private String accountSecret;

  @Column(name = "call_recording_platform", length = 255)
  private String callRecordingPlatform;

  @Column(name = "user_default_language", length = 10)
  private String userDefaultLanguage;

  @Column(name = "user_default_role", length = 50)
  private String userDefaultRole;

  @Column(name = "group_id", length = 100)
  private String groupId;

  @Column(name = "enabled")
  private Boolean enabled;
} 