package net.evolveip.ossmosis.api.scheduler.service.scheduler;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.enterprise.service.EnterpriseService;
import net.evolveip.ossmosis.api.scheduler.listener.JobsListener;
import net.evolveip.ossmosis.api.scheduler.listener.TriggersListener;
import net.evolveip.ossmosis.api.scheduler.service.trigger.JobDetailService;
import net.evolveip.ossmosis.api.scheduler.service.trigger.TriggerBuilderService;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger.TriggerState;
import org.quartz.TriggerKey;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public abstract class AbstractSchedulerReportService extends AbstractService {

  protected final TriggerBuilderService triggerBuilderService;
  protected final JobDetailService jobDetailService;

  @Autowired
  public AbstractSchedulerReportService(Scheduler scheduler, EnterpriseService enterpriseService,
      TriggerBuilderService triggerBuilderService, JobDetailService jobDetailService) {
    super(scheduler, enterpriseService);
    this.triggerBuilderService = triggerBuilderService;
    this.jobDetailService = jobDetailService;
  }

  @PostConstruct
  public void registerListeners() {
    try {
      if (scheduler.isStarted()) {
        // set JobListener
        scheduler.getListenerManager().addJobListener(new JobsListener());
        // set TriggerListener
        scheduler.getListenerManager().addTriggerListener(new TriggersListener());
      }
    } catch (Exception e) {
      logger.error(e.getMessage());
    }
  }

  protected String getEnterpriseName(String enterpriseId) {
    return enterpriseService.processGetById(enterpriseId).getEnterpriseName();
  }

  protected Set<JobKey> getJobs(String enterpriseId) throws SchedulerException {
    return scheduler.getJobKeys(GroupMatcher.jobGroupEquals(enterpriseId));
  }

  protected Map<String, TriggerState> getJobState(String enterpriseId, String jobId)
      throws SchedulerException {
    return getJobState(new JobKey(jobId, enterpriseId));
  }

  protected Map<String, TriggerState> getJobState(JobKey jobKey)
      throws SchedulerException {
    return getJobsStates(jobKey.getGroup()).entrySet().stream()
        .filter(entry -> entry.getKey().equalsIgnoreCase(jobKey.getName()))
        .collect(Collectors.toMap(Entry::getKey, Entry::getValue));
  }

  protected Boolean getJobState(String enterpriseId, JobKey jobKey)
      throws SchedulerException {
    TriggerState state = getJobsStates(enterpriseId)
        .entrySet()
        .stream()
        .filter(entry -> entry.getKey().equalsIgnoreCase(jobKey.getName()))
        .map(Map.Entry::getValue)
        .findFirst()
        .orElse(null);
    if (state.toString().equalsIgnoreCase("normal")) {
      return true;
    } else {
      return false;
    }
  }

  protected Map<String, TriggerState> getJobsStates(String enterpriseId) throws SchedulerException {
    Set<JobKey> keySet = getJobs(enterpriseId);
    Map<String, TriggerState> jobStates = new HashMap<>();
    for (JobKey jobKey : keySet) {
      jobStates.put(jobKey.getName(),
          scheduler.getTriggerState(TriggerKey.triggerKey(jobKey.getName(), enterpriseId)));
    }
    return jobStates;
  }

  protected Map<String, Integer> getJobsStatesCounters(String enterpriseId)
      throws SchedulerException {
    Map<String, Integer> jobsStates = new HashMap<>();
    for (TriggerState enumValue : TriggerState.values()) {
      jobsStates.put(enumValue.name(), 0);
    }
    Set<TriggerKey> triggerKeys = scheduler.getTriggerKeys(GroupMatcher.groupEquals(enterpriseId));
    for (TriggerKey triggerKey : triggerKeys) {
      TriggerState triggerState = scheduler.getTriggerState(triggerKey);
      jobsStates.compute(triggerState.name(), (k, v) -> v + 1);
    }
    return jobsStates;
  }
}