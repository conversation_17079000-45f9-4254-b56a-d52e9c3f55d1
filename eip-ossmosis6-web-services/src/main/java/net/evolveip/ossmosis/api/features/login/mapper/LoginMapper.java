package net.evolveip.ossmosis.api.features.login.mapper;

import java.util.List;
import net.evolveip.ossmosis.api.features.login.dto.LoginRequestDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginResponseDTO;
import net.evolveip.ossmosis.api.features.login.entity.Login;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.factory.Mappers;


@Mapper(componentModel = ComponentModel.SPRING, uses = {
    LoginRoleMapper.class,
    LoginResourcePermissionMapper.class}, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface LoginMapper {


  LoginMapper INSTANCE = Mappers.getMapper(LoginMapper.class);

  @Mapping(source = "loginId", target = "loginId")
  @Mapping(source = "loginEmail", target = "loginEmail")
  @Mapping(source = "loginNameFirst", target = "loginNameFirst")
  @Mapping(source = "loginNameLast", target = "loginNameLast")
  @Mapping(source = "active", target = "active")
  @Mapping(source = "locked", target = "locked")
  @Mapping(source = "loginGroup", target = "loginGroup")
  @Mapping(source = "loginPhoneNumber", target = "loginPhoneNumber")
  @Mapping(source = "loginPrimaryEnterpriseId", target = "loginPrimaryEnterprise.enterpriseId")
  @Mapping(ignore = true, target = "roles")
  @Mapping(ignore = true, target = "dateCreated")
  @Mapping(ignore = true, target = "dateUpdated")
  @Mapping(ignore = true, target = "userPermissionList")
  Login toLogin(LoginRequestDTO loginRequestDTO);

  @InheritInverseConfiguration
  @Mapping(ignore = true, target = "roles")
  @Mapping(ignore = true, target = "userPermissionList")
  LoginRequestDTO toLoginDTO(Login login);

  @Mapping(source = "loginId", target = "loginId")
  @Mapping(source = "loginEmail", target = "loginEmail")
  @Mapping(source = "loginNameFirst", target = "loginNameFirst")
  @Mapping(source = "loginNameLast", target = "loginNameLast")
  @Mapping(source = "active", target = "active")
  @Mapping(source = "locked", target = "locked")
  @Mapping(source = "loginGroup", target = "loginGroup")
  @Mapping(source = "loginPhoneNumber", target = "loginPhoneNumber")
  @Mapping(source = "dateCreated", target = "dateCreated")
  @Mapping(source = "dateUpdated", target = "dateUpdated")
  @Mapping(source = "loginPrimaryEnterprise.enterpriseId", target = "loginPrimaryEnterpriseId")
  @Mapping(source = "roles", target = "roles")
  @Mapping(source = "userPermissionList", target = "userPermissionList")
  LoginResponseDTO toLoginResponseDTO(Login login);


  List<LoginResponseDTO> entitiesToDTOs(List<Login> logins);
}
