package net.evolveip.ossmosis.api.features.resource.entity;

import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import net.evolveip.ossmosis.api.features.permission.entity.Permission;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@Table(name = "resource_permission")
public class ResourcePermission {

  @EmbeddedId
  ResourcePermissionId resourcePermissionId;

  @MapsId("permissionId")
  @JoinColumn(name = "permission_id", nullable = false)
  @ManyToOne(targetEntity = Permission.class, fetch = FetchType.LAZY)
  private Permission permission;

  @MapsId("resourceId")
  @JoinColumn(name = "resource_id", nullable = false)
  @ManyToOne(targetEntity = Resource.class, fetch = FetchType.LAZY)
  private Resource resource;
}
