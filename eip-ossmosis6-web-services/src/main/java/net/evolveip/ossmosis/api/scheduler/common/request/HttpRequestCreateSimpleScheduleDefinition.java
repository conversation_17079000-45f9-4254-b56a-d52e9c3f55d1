package net.evolveip.ossmosis.api.scheduler.common.request;

import jakarta.persistence.ElementCollection;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.temporal.ChronoUnit;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.scheduler.common.enums.WeekDays;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidEnum;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidInteger;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidLocalTime;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidScheduleDefinition;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidZoneId;

@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ValidScheduleDefinition
public class HttpRequestCreateSimpleScheduleDefinition {

  @ValidZoneId
  private String userTimeZone;

  @ValidLocalTime
  private String reportRunTime;

  @Min(value = 0, message = "The value must be positive")
  @ValidInteger
  private String frequency;

  @ValidEnum(enumClass = ChronoUnit.class)
  private String frequencyUnits;

  @ElementCollection
  private Set<@ValidEnum(enumClass = WeekDays.class) String> selectedWeekRunDays;

  @ElementCollection
  private Set<@Max(value = 31, message = "The max value is the max number of days in a month") @Min(value = 0, message = "The value must be positive") @ValidInteger String> selectedMonthRunDays;
}