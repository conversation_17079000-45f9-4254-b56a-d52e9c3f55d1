package net.evolveip.ossmosis.api.utils.file;

import com.opencsv.CSVWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Field;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import jdk.jfr.Label;
import net.evolveip.ossmosis.api.config.csv.CSVTempLocationPropertiesConfig;
import net.evolveip.ossmosis.api.utils.file.annotations.Exportable;

public class CSVService<T> {

  final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss z");

  public File generateCsv(List<T> data, CSVTempLocationPropertiesConfig tempDir, String fileName, String timezone)
      throws IOException {

    File file = new File(buildFilePath(tempDir.getPath(), fileName));

    try (CSVWriter writer = new CSVWriter(new FileWriter(file))) {

      Class<?> dataClass = data.get(0).getClass();

      generateTableHeader(writer, dataClass);
      generateTableData(writer, data, timezone);
    }

    return file;
  }

  /***
   * Method to retrieve and write the column header fields
   * @param writer The CSVWriter object
   * @param dataClass The base dataClass type
   */
  private void generateTableHeader(CSVWriter writer, Class<?> dataClass) {
    List<String> headerStrings = new ArrayList<>();
    getDataClassHeaders(headerStrings, dataClass, null);
    writer.writeNext(headerStrings.toArray(new String[0]));
  }

  /***
   * Recursive method to retrieve field labels for the data class and any child data classes
   * @param headerStrings Current list of headers
   * @param dataClass Current dataClass type
   */
  private void getDataClassHeaders(List<String> headerStrings, Class<?> dataClass, Label currentLabel) {

    List<Field> fieldList = Arrays.stream(dataClass.getDeclaredFields()).toList();

    // Loop through all the fields of the dataClass
    for (Field field : fieldList) {
      Label fieldLabel = field.getAnnotation(Label.class);

      // If the field is a collection, skip it
      if (Collection.class.isAssignableFrom(field.getType())) {
        fieldLabel = null;

        // Check if field type is exportable and retrieve additional headers
      } else if (field.getType().getAnnotation(Exportable.class) != null) {
        Class<?> fieldType = field.getType();
        getDataClassHeaders(headerStrings, fieldType, fieldLabel);
        fieldLabel = null;
      }

      // For any standard value add to the header list
      if (fieldLabel != null) {
        String headerValue = "";
        if (currentLabel != null) {
          headerValue += currentLabel.value() + " - ";
        }
        headerValue +=fieldLabel.value();
        headerStrings.add(headerValue);
      }
    }
  }

  /***
   * Method to retrieve and write row data
   * @param writer The CSVWriter object
   * @param data The data list
   * @param timezone The timezone to display any date/times in
   */
  private void generateTableData(CSVWriter writer, List<?> data, String timezone) {

    if (!data.isEmpty()) {

      // Loop through each object in the data list
      for (Object item : data) {
        String[] nextRow = generateTableObject(item, timezone);
        writer.writeNext(nextRow);
      }
    }
  }

  /***
   * Recursive method to retrieve field values from the data class and any child data classes
   * @param data The data list
   * @param timezone The timezone to display date/time in
   * @return The current rowData
   */
  private String[] generateTableObject(Object data, String timezone) {

    List<Field> fieldList = Arrays.stream(data.getClass().getDeclaredFields()).toList();
    List<String> rowData = new ArrayList<>();

    // Loop through each field in the data class
    for (Field field : fieldList) {
      field.setAccessible(true);
      try {
        Object value = field.get(data);
        if (value == null) value = "";

        // If the field is a collection, skip it
        if (Collection.class.isAssignableFrom(field.getType())) {
          value = null;

          // Check if field type is exportable and retrieve additional data
        } else if (field.getType().getAnnotation(Exportable.class) != null) {
          String[] subRowData = generateTableObject(value, timezone);
          rowData.addAll(List.of(subRowData));
          value = null;
        }

        // For any standard value format and add to rowData
        if (value != null) {
          if (value instanceof ZonedDateTime) {
            value = ((ZonedDateTime) value).withZoneSameInstant(ZoneId.of(timezone)).format(formatter);
          }
          rowData.add(value.toString());
        }
      } catch (IllegalAccessException e) {
        throw new RuntimeException(e);
      }
    }

    return rowData.toArray(new String[0]);
  }

  /***
   * Builds a full filepath string
   * @param tempDir The directory to store the file temporarily
   * @param fileName The filename of the file
   * @return The full filepath
   */
  private String buildFilePath(String tempDir, String fileName) {
    return tempDir + File.separator + fileName;
  }
}
