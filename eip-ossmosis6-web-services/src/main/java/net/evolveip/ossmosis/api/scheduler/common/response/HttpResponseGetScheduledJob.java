package net.evolveip.ossmosis.api.scheduler.common.response;


import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.scheduler.common.dto.EmailDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.JobDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.JobSpecificationDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.TriggerDTO;
import org.quartz.JobDataMap;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HttpResponseGetScheduledJob {

  private EmailDefinitionDTO emailDefinition;
  private JobDataMap jobScheduleDefinition;
  private List<TriggerDTO> jobRuntimes;
  private JobSpecificationDTO jobSpecification;
  private JobDefinitionDTO jobDefinition;
}