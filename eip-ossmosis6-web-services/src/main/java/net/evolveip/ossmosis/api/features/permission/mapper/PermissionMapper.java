package net.evolveip.ossmosis.api.features.permission.mapper;

import java.util.List;
import net.evolveip.ossmosis.api.features.permission.dto.PermissionResponseDTO;
import net.evolveip.ossmosis.api.features.permission.entity.Permission;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = ComponentModel.SPRING)
public interface PermissionMapper {

  PermissionMapper INSTANCE = Mappers.getMapper(PermissionMapper.class);


  @Mapping(source = "permissionId", target = "permissionId")
  @Mapping(source = "permissionName", target = "permissionName")
  PermissionResponseDTO toPermissionResponseDTO(Permission permission);


  List<PermissionResponseDTO> entitiesToDTOs(List<Permission> permissions);

}
