package net.evolveip.ossmosis.api.features.audit.repository;


import java.time.ZonedDateTime;
import java.util.List;
import net.evolveip.ossmosis.api.features.audit.entity.RequestAuditLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface RequestAuditLogRepository extends JpaRepository<RequestAuditLog, Long> {

  @Query(value =
      "from RequestAuditLog ral left join fetch ral.recordAuditLogs left join fetch ral.login left join ral.enterprise left join ral.login.loginPrimaryEnterprise "
          + "where ral.timestamp > :startDate and ral.timestamp < :endDate AND "
          + "upper(ral.login.loginEmail) like upper('%' || :loginEmail || '%') escape '\\' AND "
          + "upper(ral.path) like upper('%' || :path || '%') escape '\\' AND "
          + "upper(ral.method) like upper('%' || :method || '%') escape '\\' "
          + "AND (ral.enterprise.enterpriseId in (:enterpriseList) OR (ral.enterprise.enterpriseId is null and ral.login.loginPrimaryEnterprise.enterpriseId in (:enterpriseList)))"
          + "order by ral.timestamp desc")
  List<RequestAuditLog> findAllBySearchParameters(
      @Param("startDate") ZonedDateTime startDate,
      @Param("endDate") ZonedDateTime endDate,
      @Param("loginEmail") String loginEmail,
      @Param("path") String path,
      @Param("method") String method,
      @Param("enterpriseList") List<String> enterpriseList
  );

  @Query(value =
      "from RequestAuditLog ral left join fetch ral.recordAuditLogs left join fetch ral.login left join ral.enterprise left join ral.login.loginPrimaryEnterprise "
          + "where ral.timestamp > :startDate and ral.timestamp < :endDate AND "
          + "upper(ral.login.loginEmail) like upper('%' || :loginEmail || '%') escape '\\' AND "
          + "upper(ral.path) like upper('%' || :path || '%') escape '\\' AND "
          + "upper(ral.method) like upper('%' || :method || '%') escape '\\' "
          + "AND (ral.enterprise.enterpriseId in (:enterpriseList) OR (ral.enterprise.enterpriseId is null and ral.login.loginPrimaryEnterprise.enterpriseId in (:enterpriseList)))")
  Page<RequestAuditLog> findAllBySearchParametersPageable(
      @Param("startDate") ZonedDateTime startDate,
      @Param("endDate") ZonedDateTime endDate,
      @Param("loginEmail") String loginEmail,
      @Param("path") String path,
      @Param("method") String method,
      @Param("enterpriseList") List<String> enterpriseList,
      Pageable pageable
  );
}