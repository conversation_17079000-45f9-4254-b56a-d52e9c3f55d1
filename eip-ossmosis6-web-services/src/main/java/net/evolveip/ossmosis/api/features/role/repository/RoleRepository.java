package net.evolveip.ossmosis.api.features.role.repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import net.evolveip.ossmosis.api.features.role.entity.Role;
import net.evolveip.ossmosis.api.features.role.entity.view.RolePermissionView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface RoleRepository extends JpaRepository<Role, Integer> {

  List<Role> findByRoleNameIn(Collection<String> roleName);

  @Query(value = "select r from Role r where r.roleName = :roleName and r.enterprise.enterpriseId = :enterpriseId")
  Optional<Role> findRoleByRoleNameAndEnterprise_EnterpriseId(String roleName, String enterpriseId);

  @Query(value = "select r from Role r left join fetch r.permissionList list left join fetch list.resource left join fetch list.permission where r.roleId = :roleId and r.enterprise.enterpriseId = :enterpriseId")
  RolePermissionView findRolePermissionViewByRoleId(Integer roleId, String enterpriseId);

  @Query(value = "select r from Role r left join fetch r.permissionList list left join fetch list.resource left join fetch list.permission where r.enterprise.enterpriseId = :enterpriseId or r.defaultRole = true order by r.roleId")
  Page<RolePermissionView> findAllWithPermissionPaginatedView(String enterpriseId, Pageable pageable);

  @Query(value = "select r from Role r left join fetch r.permissionList list left join fetch list.resource left join fetch list.permission where r.enterprise.enterpriseId = :enterpriseId or r.defaultRole = true order by r.roleId")
  List<RolePermissionView> findAllWithPermissionView(String enterpriseId);

  @Query(nativeQuery = true, value = "SELECT * from partnerprovider.fn_roles_update(:jsonText)")
  List<Role> updateRoles(String jsonText);

  int deleteByRoleId(int roleId);

  @Query(value = "SELECT COUNT(r) from Role r where (r.enterprise.enterpriseId = :enterpriseId or r.defaultRole = true) and lower(r.roleName) like lower(concat('%', :filterValue, '%'))")
  long findFilteredCountForEnterpriseWithDataPopulated(String enterpriseId, String filterValue);

  @Query(value = "select r from Role r left join fetch r.permissionList list left join fetch list.resource left join fetch list.permission where (r.enterprise.enterpriseId = :enterpriseId or r.defaultRole = true) and lower(r.roleName) like lower(concat('%', :filterValue, '%')) order by r.roleId")
  Page<RolePermissionView> findAllByFilteredPage(String enterpriseId, String filterValue, Pageable pageable);
}
