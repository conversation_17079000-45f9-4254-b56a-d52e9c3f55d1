package net.evolveip.ossmosis.api.features.calldetails.repository;

import jakarta.persistence.EntityManager;
import java.lang.reflect.Field;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;
import net.evolveip.ossmosis.api.features.calldetails.dto.CallDetailDTO;
import net.evolveip.ossmosis.api.features.calldetails.dto.CallDetailSummaryDTO;
import net.evolveip.ossmosis.api.features.calldetails.entity.CallDetailEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaContext;
import org.springframework.stereotype.Component;

@Component
public class CustomCallDetailRepositoryImpl implements CustomCallDetailRepository {

  private final EntityManager entityManager;

  public CustomCallDetailRepositoryImpl(JpaContext context) {
    this.entityManager = context.getEntityManagerByManagedType(CallDetailEntity.class);
  }

  public List<CallDetailDTO> getCustomCallDetails(String answerStatus, String externalOnly, String direction, String callType, String userType, ZonedDateTime startDate, ZonedDateTime endDate, List<String> enterpriseIds, List<String> userNumber, List<String> groupNumber, String groupBy) {
    String sql = buildQueryString(answerStatus, externalOnly, direction, callType, userType, startDate, endDate, enterpriseIds, userNumber, groupNumber, groupBy);
    List<Object[]> dataList = entityManager.createQuery(sql).getResultList();
    List<CallDetailDTO> callDetailDTOS = createRawCallDetailList(dataList);
    return callDetailDTOS;
  }

  public long getCustomCallDetailsCount(String answerStatus, String externalOnly, String direction, String callType, String userType, ZonedDateTime startDate, ZonedDateTime endDate, List<String> enterpriseIds, List<String> userNumbers, List<String> groupNumbers, String groupBy) {
    String sql = buildCallDetailsCountQueryString(answerStatus, externalOnly, direction, callType, userType, startDate, endDate, enterpriseIds, userNumbers, groupNumbers, groupBy);
    List<Object> dataList = entityManager.createQuery(sql).getResultList();
    long count = (long) dataList.get(0);
    return count;
  }


  public long getCustomCallDetailsPageableCount(String answerStatus, String externalOnly, String direction, String callType, String userType, ZonedDateTime startDate, ZonedDateTime endDate, List<String> enterpriseIds, List<String> userNumbers, List<String> groupNumbers, String groupBy) {
    String sql = buildPageableCountQueryString(answerStatus, externalOnly, direction, callType, userType, startDate, endDate, enterpriseIds, userNumbers, groupNumbers, groupBy);
    List<Object> dataList = entityManager.createQuery(sql).getResultList();
    long count = (long) dataList.get(0);
    return count;
  }

  public List<CallDetailSummaryDTO> getCustomCallDetailsPageList(String answerStatus, String externalOnly, String direction, String callType, String userType, ZonedDateTime startDate, ZonedDateTime endDate, List<String> enterpriseIds, List<String> userNumbers, List<String> groupNumbers, String groupBy, Pageable pageable) {
    String sql = buildPageListQuery(answerStatus, externalOnly, direction, callType, userType, startDate, endDate, enterpriseIds, userNumbers, groupNumbers, groupBy, pageable);
    List<Object[]> dataList = entityManager.createQuery(sql).getResultList();
    List<CallDetailSummaryDTO> callDetailSummaryDTOS = createRawCallDetailSummaryList(dataList);
    return callDetailSummaryDTOS;
  }

  public List<CallDetailDTO> getCustomCallDetailsPageable(String answerStatus, String externalOnly, String direction, String callType, String userType, ZonedDateTime startDate, ZonedDateTime endDate, List<String> enterpriseIds, List<String> userNumbers, List<String> groupNumbers, String groupBy, Pageable pageable) {
    String sql = buildPageableQueryString(answerStatus, externalOnly, direction, callType, userType, startDate, endDate, enterpriseIds, userNumbers, groupNumbers, groupBy, pageable);
    List<Object[]> dataList = entityManager.createQuery(sql).getResultList();
    List<CallDetailDTO> callDetailDTOS = createRawCallDetailList(dataList);
    return callDetailDTOS;
  }

  private List<CallDetailSummaryDTO> createRawCallDetailSummaryList(List<Object[]> dataList) {
    List<CallDetailSummaryDTO> rawCallDetailSummaryDTOS = new ArrayList<>();
    for (Object[] obj : dataList) {
      CallDetailSummaryDTO callDetailSummaryDTO = new CallDetailSummaryDTO();
      Field[] fields = callDetailSummaryDTO.getClass().getDeclaredFields();
      IntStream.range(0, fields.length - 1).forEach((index -> {
        try {
          Field field = fields[index];
          field.setAccessible(true);
          field.set(callDetailSummaryDTO, obj[index]);
        } catch (IllegalAccessException e) {
          throw new RuntimeException(e);
        }
      }));
      rawCallDetailSummaryDTOS.add(callDetailSummaryDTO);
    }

    return rawCallDetailSummaryDTOS;
  }

  private List<CallDetailDTO> createRawCallDetailList(List<Object[]> dataList) {
    List<CallDetailDTO> rawCallDetailDTOS = new ArrayList<>();
    for (Object[] obj : dataList) {
      CallDetailDTO callDetailDTO = new CallDetailDTO();
      Field[] fields = callDetailDTO.getClass().getDeclaredFields();
      IntStream.range(0, fields.length).forEach((index -> {
        try {
          Field field = fields[index];
          field.setAccessible(true);
          if (field.getName().equals("startDateTime")) {
            field.set(callDetailDTO, ZonedDateTime.parse(obj[index].toString().concat(" -00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS z")));
          } else if (field.getName().equals("callDirection")) {
            String value = String.valueOf(obj[index]);
            if (value.equals("Originating")) {
              field.set(callDetailDTO, "Outbound");
            } else if (value.equals("Terminating")) {
              field.set(callDetailDTO, "Inbound");
            } else {
              field.set(callDetailDTO, value);
            }
          } else {
            field.set(callDetailDTO, obj[index]);
          }
        } catch (IllegalAccessException e) {
          throw new RuntimeException(e);
        }
      }));
      rawCallDetailDTOS.add(callDetailDTO);
    }

    return rawCallDetailDTOS;
  }

  private String buildQueryString(String answerStatus, String externalOnly, String direction, String callType, String userType, ZonedDateTime startDate, ZonedDateTime endDate, List<String> enterpriseIds, List<String> userNumbers, List<String> groupNumbers, String groupBy) {

    StringBuilder query = new StringBuilder();
    query.append(addQueryColumns(false, groupBy));
    query.append(addGenericQueryFilters(answerStatus, externalOnly, direction,callType, userType, startDate, endDate, enterpriseIds));
    query.append(addQueryPhoneNumberFilters(userNumbers, groupNumbers));
    query.append(addQueryOrderByNumber(groupBy));

    return query.toString();
  }

  private String buildCallDetailsCountQueryString(String answerStatus, String externalOnly, String direction, String callType, String userType, ZonedDateTime startDate, ZonedDateTime endDate, List<String> enterpriseIds, List<String> userNumbers, List<String> groupNumbers, String groupBy) {
    StringBuilder query = new StringBuilder("SELECT COUNT(cd.userNumber)");

    query.append(" FROM CallDetailEntity as cd "
        + "LEFT OUTER JOIN CallDetailLocationEntity as l1 on cd.calledLerg = l1.npanxx "
        + "LEFT OUTER JOIN CallDetailLocationEntity as l2 on cd.callingLerg = l2.npanxx "
        + "LEFT OUTER JOIN CallCategoryEntity as cc on cd.callCategoryKey = cc.id "
        + "LEFT OUTER JOIN CallTypeEntity as ct on cd.callType = ct.id "
        + "LEFT OUTER JOIN BroadsoftUserEntity as bu on cd.userId = bu.id");
    query.append(addGenericQueryFilters(answerStatus, externalOnly, direction, callType, userType, startDate, endDate, enterpriseIds));
    query.append(addQueryPhoneNumberFilters(userNumbers, groupNumbers));

    String queryString = query.toString();
    queryString = queryString.replaceAll(" callDirection", " cd.callDirection");
    queryString = queryString.replaceAll(" userNumber", " cd.userNumber");
    queryString = queryString.replaceAll(" callType", " ct.callType");
    queryString = queryString.replaceAll(" userType", " bu.userType");
    queryString = queryString.replaceAll(" startDateTime"," cd.startDateTime");
    queryString = queryString.replaceAll(" groupNumber", " cd.groupNumber");

    return queryString;
  }

  private String buildPageableCountQueryString(String answerStatus, String externalOnly, String direction, String callType, String userType, ZonedDateTime startDate, ZonedDateTime endDate, List<String> enterpriseIds, List<String> userNumbers, List<String> groupNumbers, String groupBy) {

    StringBuilder query = new StringBuilder("SELECT COUNT(DISTINCT ");

    switch(groupBy) {
      case "user" -> {
        query.append("cd.userNumber)");
      }
      case "group" -> {
        query.append("cd.groupNumber)");
      }
      default -> {}
    }
    query.append(" FROM CallDetailEntity as cd "
        + "LEFT OUTER JOIN CallDetailLocationEntity as l1 on cd.calledLerg = l1.npanxx "
        + "LEFT OUTER JOIN CallDetailLocationEntity as l2 on cd.callingLerg = l2.npanxx "
        + "LEFT OUTER JOIN CallCategoryEntity as cc on cd.callCategoryKey = cc.id "
        + "LEFT OUTER JOIN CallTypeEntity as ct on cd.callType = ct.id "
        + "LEFT OUTER JOIN BroadsoftUserEntity as bu on cd.userId = bu.id");
    query.append(addGenericQueryFilters(answerStatus, externalOnly, direction, callType, userType, startDate, endDate, enterpriseIds));
    query.append(addQueryPhoneNumberFilters(userNumbers, groupNumbers));

    String queryString = query.toString();
    queryString = queryString.replaceAll(" callDirection", " cd.callDirection");
    queryString = queryString.replaceAll(" userNumber", " cd.userNumber");
    queryString = queryString.replaceAll(" callType", " ct.callType");
    queryString = queryString.replaceAll(" userType", " bu.userType");
    queryString = queryString.replaceAll(" startDateTime"," cd.startDateTime");
    queryString = queryString.replaceAll(" groupNumber", " cd.groupNumber");

    return queryString;
  }

  private String buildPageListQuery(String answerStatus, String externalOnly, String direction, String callType, String userType, ZonedDateTime startDate, ZonedDateTime endDate, List<String> enterpriseIds, List<String> userNumbers, List<String> groupNumbers, String groupBy, Pageable pageable) {

    StringBuilder query = new StringBuilder();

    query.append(addQueryColumns(true, groupBy));
    query.append(addGenericQueryFilters(answerStatus, externalOnly, direction, callType, userType, startDate, endDate, enterpriseIds));
    query.append(addQueryPhoneNumberFilters(userNumbers, groupNumbers));
    query.append(addQueryGroupBy(groupBy));
    if (pageable == null) {
      query.append(addQueryOrderByNumber(groupBy));
    } else {
      query.append(addQueryOrderBy(pageable));
      query.append(addQueryLimit(pageable));
    }

    return query.toString();
  }

  private String buildPageableQueryString(String answerStatus, String externalOnly, String direction, String callType, String userType, ZonedDateTime startDate, ZonedDateTime endDate, List<String> enterpriseIds, List<String> userNumbers, List<String> groupNumbers, String groupBy, Pageable pageable) {

    StringBuilder query = new StringBuilder();

    query.append(addQueryColumns(false, groupBy));
    query.append(addGenericQueryFilters(answerStatus, externalOnly, direction, callType, userType, startDate, endDate, enterpriseIds));
    query.append(addQueryPhoneNumberFilters(userNumbers, groupNumbers));

    return query.toString();
  }

  private String addQueryColumns(boolean getPage, String groupBy) {

    StringBuilder query = new StringBuilder("SELECT ");

    if (getPage) {
      switch (groupBy) {
        case "user" -> {
          query.append("Min(bu.userDisplayName) as userDisplayName, "
                      + "cd.userNumber as userNumber, "
                      + "Min(bu.userType) as userType, "
                      + "Min(cd.groupId) as groupId, "
                      + "Min(cd.groupNumber) as groupNumber, ");
        }
        case "group" -> {
          query.append("Min(bu.userDisplayName) as userDisplayName, "
                      + "Min(cd.userNumber) as userNumber, "
                      + "Min(bu.userType) as userType, "
                      + "Min(cd.groupId) as groupId, "
                      + "cd.groupNumber as groupNumber, ");
        }
        default -> {}
      }
      query.append("COUNT(case when cd.callDirection = 'Terminating' then 1 else null end) as inboundCallsCount, "
              + "COALESCE(SUM(case when cd.callDirection = 'Terminating' then cd.callDuration else null end),0) as inboundCallsDuration, "
              + "COALESCE(AVG(case when cd.callDirection = 'Terminating' then cd.callDuration else null end),0) as inboundCallsAverageDuration, "
              + "COUNT(case when cd.callDirection = 'Originating' then 1 else null end) as outboundCallsCount, "
              + "COALESCE(SUM(case when cd.callDirection = 'Originating' then cd.callDuration else null end),0) as outboundCallsDuration, "
              + "COALESCE(AVG(case when cd.callDirection = 'Originating' then cd.callDuration else null end),0) as outboundCallsAverageDuration, "
              + "COUNT(cd.callDuration) as totalCallsCount, "
              + "COALESCE(SUM(cd.callDuration),0) as totalCallsDuration, "
              + "COALESCE(AVG(cd.callDuration),0) as totalCallsAverageDuration "
              + "FROM CallDetailEntity as cd "
              + "LEFT OUTER JOIN CallTypeEntity as ct on cd.callType = ct.id "
              + "LEFT OUTER JOIN BroadsoftUserEntity as bu on cd.userId = bu.id");
    } else {
      query.append(" cd.id as id, "
          + "cd.userNumber as userNumber, "
          + "cd.callingNumber as callingNumber, "
          + "cd.calledNumber as calledNumber, "
          + "cd.callDuration as callDuration, "
          + "cd.callDirection as callDirection, "
          + "l1.city as city1, "
          + "l1.state as state1, "
          + "l2.city as city2, "
          + "l2.state as state2, "
          + "cd.groupNumber as groupNumber, "
          + "cd.groupId as groupId, "
          + "cc.callCategory as callCategory, "
          + "ct.callType as callType, "
          + "bu.userDisplayName as userDisplayName, "
          + "bu.userType as userType, "
          + "cd.startDateTime as startDateTime "
          + "FROM CallDetailEntity as cd "
          + "LEFT OUTER JOIN CallDetailLocationEntity as l1 on cd.calledLerg = l1.npanxx "
          + "LEFT OUTER JOIN CallDetailLocationEntity as l2 on cd.callingLerg = l2.npanxx "
          + "LEFT OUTER JOIN CallCategoryEntity as cc on cd.callCategoryKey = cc.id "
          + "LEFT OUTER JOIN CallTypeEntity as ct on cd.callType = ct.id "
          + "LEFT OUTER JOIN BroadsoftUserEntity as bu on cd.userId = bu.id");
    }

    return query.toString();
  }

  private String addGenericQueryFilters(String answerStatus, String externalOnly, String direction, String callType, String userType, ZonedDateTime startDate, ZonedDateTime endDate, List<String> enterpriseIds) {

    // Pattern to find ip address and optional port which identifies external calls
    String externalCallsPattern = "(\\\\d{1,3}\\\\.\\\\d{1,3}\\\\.\\\\d{1,3}\\\\.\\\\d{1,3})(:\\\\d{1,5})?";
    String tollFreeCallsPattern = "^(8(00|33|44|55|66|77|88))";

    StringBuilder queryFilters = new StringBuilder(" WHERE cd.enterpriseId IN('");

    for (String enterpriseId : enterpriseIds) {
      queryFilters.append(enterpriseId);
      queryFilters.append("','");
    }
    queryFilters.delete(queryFilters.length()-3, queryFilters.length()-1);
    queryFilters.append(")");

    switch (answerStatus) {
      case "answered" -> {
        queryFilters.append(" and cd.answerStatus = 'Y'");
      }
      case "unanswered" -> {
        queryFilters.append(" and cd.answerStatus = 'N'");
      }
      default -> {}
    }

    switch (externalOnly) {
      case "external" -> {
        queryFilters.append(" and REGEXP_LIKE(cd.callRoute, '" + externalCallsPattern + "') = true");
      }
      case "internal" -> {
        queryFilters.append(" and (cd.callRoute = 'Group' or cd.callRoute = 'Enterprise' or contains(cd.callRoute, 'evolveip.net') = true)");
      }
      default -> {}
    }

    switch (direction) {
      case "inbound" -> {
        queryFilters.append(" and callDirection = 'Terminating'");
      }
      case "outbound" -> {
        queryFilters.append(" and callDirection = 'Originating'");
      }
      default -> {}
    }

    switch (callType) {
      case "evolveip" -> {
        queryFilters.append(" and ct.callType = 'On Evolve IP Network'");
      }
      case "nonbillable" -> {
        queryFilters.append(" and ct.callType = 'Non Billable'");
      }
      case "domestic" -> {
        queryFilters.append(" and ct.callType = 'Domestic'");
      }
      case "tollfree" -> {
        queryFilters.append(" and contains(ct.callType, 'Toll Free') = true");
      }
      case "inboundtollfree" -> {
        queryFilters.append(" and contains(ct.callType, 'Inbound Toll Free') = true");
      }
      case "outboundtollfree" -> {
        queryFilters.append(" and contains(ct.callType, 'Outbound Toll Free') = true");
      }
      default -> {}
    }

    switch (userType) {
      case "autoattendant" -> {
        queryFilters.append(" and userType = 'AUTOATTENDANT'");
      }
      case "user" -> {
        queryFilters.append(" and userType = 'USER'");
      }
      case "extensionuser" -> {
        // EXTENTIONUSER spelling is what is used in snowflake
        queryFilters.append(" and userType = 'EXTENTIONUSER'");
      }
      case "huntgroup" -> {
        queryFilters.append(" and userType = 'HUNTGROUP'");
      }
      case "callcenter" -> {
        queryFilters.append(" and userType = 'CALLCENTER'");
      }
      case "voicemail" -> {
        queryFilters.append(" and userType = 'VOICEMAIL'");
      }
      case "audioconference" -> {
        queryFilters.append(" and userType = 'AUDIOCONFERENCE'");
      }
      case "groupcalling" -> {
        queryFilters.append(" and userType = 'GROUPCALLING'");
      }
      default -> {}
    }

    if (startDate != null) {
      queryFilters.append(" and startDateTime >= '");
      queryFilters.append(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS").format(startDate));
      queryFilters.append("'");
    }

    if (endDate != null) {
      queryFilters.append(" and startDateTime <= '");
      queryFilters.append(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS").format(endDate));
      queryFilters.append("'");
    }

    return queryFilters.toString();
  }

  private String addQueryPhoneNumberFilters(List<String> userNumbers, List<String> groupNumbers) {

    StringBuilder query = new StringBuilder();

    if (userNumbers != null && !userNumbers.isEmpty()) {
      query.append(" and userNumber IN ('");
      for(String userNumber: userNumbers) {
        query.append(userNumber);
        query.append("','");
      }
      query.delete(query.length()-3, query.length()-1);
      query.append(")");
    }

    if (groupNumbers != null && !groupNumbers.isEmpty()) {
      query.append(" and groupNumber IN ('");
      for(String groupNumber: groupNumbers) {
        query.append(groupNumber);
        query.append("','");
      }
      query.delete(query.length()-3, query.length()-1);
      query.append(")");
    }

    return query.toString();
  }

  private String addQueryGroupBy(String groupBy) {

    StringBuilder query = new StringBuilder(" GROUP BY ");

    switch (groupBy) {
      case "user" -> {
        query.append("userNumber");
      }
      case "group" -> {
        query.append("groupNumber");
      }
      default -> {}
    }

    return query.toString();
  }

  private String addQueryOrderByNumber(String groupBy) {

    StringBuilder query = new StringBuilder(" ORDER BY ");

    switch (groupBy) {
      case "user" -> {
        query.append("userNumber");
      }
      case "group" -> {
        query.append("groupNumber");
      }
      default -> {
      }
    }

    return query.toString();
  }

  private String addQueryOrderBy(Pageable pageable) {

    StringBuilder query = new StringBuilder(" ORDER BY ");

    String sortColumn = pageable.getSort().toString().split(":")[0].trim();
    String sortDirection = pageable.getSort().toString().split(":")[1].trim();

    switch (sortColumn) {
      case "userName" -> {
        query.append("userDisplayName");
      }
      case "userNumber",
           "userType",
           "groupNumber",
           "groupId",
           "inboundCallsCount",
           "inboundCallsDuration",
           "inboundCallsAverageDuration",
           "outboundCallsCount",
           "outboundCallsDuration",
           "outboundCallsAverageDuration",
           "totalCallsCount",
           "totalCallsDuration",
           "totalCallsAverageDuration" -> {
        query.append(sortColumn);
      }
      default -> {
        query.append("userNumber");
      }
    }

    query.append(" ").append(sortDirection);

    return query.toString();
  }

  private String addQueryLimit(Pageable pageable) {

    StringBuilder query = new StringBuilder(" LIMIT ");
    int pageSize = pageable.getPageSize();
    long pageOffset = pageable.getOffset();

    query.append(pageSize).append(" OFFSET ").append(pageOffset);

    return query.toString();
  }
}
