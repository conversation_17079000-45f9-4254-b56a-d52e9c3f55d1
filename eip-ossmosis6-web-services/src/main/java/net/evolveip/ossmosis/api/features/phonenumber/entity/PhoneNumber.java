package net.evolveip.ossmosis.api.features.phonenumber.entity;

import jakarta.persistence.AttributeOverride;
import jakarta.persistence.AttributeOverrides;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.ZonedDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.group.entity.Address;

/**
 * This class represents the phone number entity mapped to its respective database table
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode
@Table(name = "phone_number", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"country_code", "phone_number"})},
    indexes = {
        @Index(name = "idx_enterprise_country_phone", columnList = "enterprise_id, country_code, phone_number"),
        @Index(name = "idx_country_code", columnList = "country_code"),
        @Index(name = "idx_phone_number", columnList = "phone_number"),
        @Index(name = "idx_phone_number_carrier_id", columnList = "carrier_id"),
        @Index(name = "idx_phone_number_status_id", columnList = "status_id")
    }
)
@Entity
public class PhoneNumber {

  @Id
  @Column(name = "phone_number_id", columnDefinition = "INT", nullable = false)
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @EqualsAndHashCode.Exclude
  private Long phoneNumberId;

  @Column(name = "date_created", nullable = false, updatable = false)
  @EqualsAndHashCode.Exclude
  private ZonedDateTime dateCreated;

  @Column(name = "date_updated", nullable = false)
  @EqualsAndHashCode.Exclude
  private ZonedDateTime dateUpdated;

  @NotNull
  @Pattern(regexp = "^\\+.*$", message = "Country code must start with a '+'")
  @Column(name = "country_code", nullable = false, columnDefinition = "VARCHAR(8) check(country_code like '+%')")
  private String countryCode;

  @NotNull
  @Size(max = 15, message = "Phone number must not exceed 15 characters")
  @Column(name = "phone_number", nullable = false, length = 15, columnDefinition = "VARCHAR(15)")
  private String phoneNumber;

  @NotNull
  @JoinColumn(name = "enterprise_id", nullable = false, columnDefinition = "CHAR(30)")
  @ManyToOne(targetEntity = Enterprise.class, fetch = FetchType.LAZY)
  private Enterprise enterprise;

  @NotNull
  @Column(name = "account_number", columnDefinition = "VARCHAR(100)")
  private String accountNumber;

  @NotNull
  @Size(max = 8, message = "Btn country code must not exceed 8 characters")
  @Column(name = "btn_country_code", nullable = false, columnDefinition = "VARCHAR(8) check(btn_country_code like '+%')")
  private String btnCountryCode;

  @Column(name = "btn_phone_number", nullable = false, length = 15, columnDefinition = "VARCHAR(15)")
  private String btnPhoneNumber;

  @JoinColumn(name = "carrier_id", nullable = false)
  @ManyToOne(targetEntity = Carrier.class, fetch = FetchType.LAZY)
  private Carrier carrier;

  @JoinColumn(name = "status_id", nullable = false)
  @ManyToOne(targetEntity = PhoneNumberStatus.class, fetch = FetchType.LAZY)
  private PhoneNumberStatus status;

  @Column(name = "pin", length = 10, columnDefinition = "VARCHAR(10)")
  private String pin;

  @Embedded
  @AttributeOverrides({
      @AttributeOverride(name = "addressLine1", column = @Column(name = "service_adress_line_1")),
      @AttributeOverride(name = "addressLine2", column = @Column(name = "service_adress_line_2")),
      @AttributeOverride(name = "city", column = @Column(name = "service_city")),
      @AttributeOverride(name = "stateOrProvince", column = @Column(name = "service_state_or_province")),
      @AttributeOverride(name = "stateOrProvinceDisplayName", column = @Column(name = "service_state_or_province_display_name")),
      @AttributeOverride(name = "zipOrPostalCode", column = @Column(name = "service_zip_or_postal_code")),
      @AttributeOverride(name = "country", column = @Column(name = "service_country"))
  })
  private Address serviceAddress;

  @Column(name = "assignment_type", nullable = false)
  private String assignmentType;

  @Column(name = "assignment_id")
  private String assignmentId;

  @Column(name = "assignment_name")
  private String assignmentName;

  @Column(name = "callingLineIdName")
  private String callingLineIdName;

  @Embedded
  @AttributeOverrides({
      @AttributeOverride(name = "addressLine1", column = @Column(name = "e911_adress_line_1")),
      @AttributeOverride(name = "addressLine2", column = @Column(name = "e911_adress_line_2")),
      @AttributeOverride(name = "city", column = @Column(name = "e911_city")),
      @AttributeOverride(name = "stateOrProvince", column = @Column(name = "e911_state_or_province")),
      @AttributeOverride(name = "stateOrProvinceDisplayName", column = @Column(name = "e911_state_or_province_display_name")),
      @AttributeOverride(name = "zipOrPostalCode", column = @Column(name = "e911_zip_or_postal_code")),
      @AttributeOverride(name = "country", column = @Column(name = "e911_country"))
  })
  private Address e911Address;

  public String getPString() {
    return String.format("%s.%s", countryCode, phoneNumber);
  }

  public String getCountryCodeNoPlus() {
    return countryCode.substring(1);
  }

  @PrePersist
  protected void onCreate() {
    ZonedDateTime now = ZonedDateTime.now();
    if (dateCreated == null) {
      dateCreated = now;
    }
    if (dateUpdated == null) {
      dateUpdated = now;
    }
  }

  @PreUpdate
  protected void onUpdate() {
    dateUpdated = ZonedDateTime.now();
  }
}