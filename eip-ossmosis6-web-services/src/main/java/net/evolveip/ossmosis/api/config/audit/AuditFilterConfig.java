package net.evolveip.ossmosis.api.config.audit;

import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.security.SecurityProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class AuditFilterConfig {

  private final AuditRequestFilter auditRequestFilter;

  public static final String DEFAULT_MDC_UUID_TOKEN_KEY = "ossRequestUUID";

  @Bean
  public FilterRegistrationBean<AuditRequestFilter> auditRequestFilterConfigBean() {
    final FilterRegistrationBean<AuditRequestFilter> filterRegistrationBean = new FilterRegistrationBean<>();
    filterRegistrationBean.setFilter(auditRequestFilter);
    filterRegistrationBean.setOrder(SecurityProperties.DEFAULT_FILTER_ORDER - 1);
    return filterRegistrationBean;
  }

  //TODO: when a service starts a new thread this value will not be available. Need to add it in to the new thread context.
  public static UUID getCurrentRequestId() {
    return MDC.get(AuditFilterConfig.DEFAULT_MDC_UUID_TOKEN_KEY) == null ?
        new UUID(0, 0) : UUID.fromString(MDC.get(AuditFilterConfig.DEFAULT_MDC_UUID_TOKEN_KEY));
  }
}