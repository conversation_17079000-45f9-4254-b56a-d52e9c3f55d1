package net.evolveip.ossmosis.api.scheduler.service.email;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public interface ListConverter<S extends String, F extends File> {

  default List<S> toEmails(S... toEmails) {
    return new ArrayList<>(Arrays.asList(toEmails));
  }

  @SuppressWarnings("unchecked")
  default S[] toEmails(List<S> toEmails) {
    String[] toEmailsArray = new String[toEmails.size()];
    toEmails.toArray(toEmailsArray);
    return (S[]) toEmailsArray;
  }

  default List<S> toEmails(S toEmail) {
    return Collections.singletonList(toEmail);
  }

  default List<F> toList(F... files) {
    return new ArrayList<>(Arrays.asList(files));
  }
}