package net.evolveip.ossmosis.api.config.apm;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import co.elastic.apm.attach.ElasticApmAttacher;

@Component
@ConfigurationProperties(prefix = "elastic.apm")
public class ElasticApmConfiguration {

    private boolean captureBody;
    private boolean enableHttpTracing;
    private int setupDelay; 
    private String applicationPackages;
    private String environment;
    private String logLevel;
    private String logReformatting;
    private String secretToken;
    private String serverUrl;
    private String serviceName;

    @EventListener(ContextRefreshedEvent.class)
    public void initializeElasticApm() {
        ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
        System.out.println("Elastic APM - Delaying Setup for " + setupDelay + " seconds");
        scheduler.schedule(() -> {
            try {
                initializeApmAgent();
            } catch (Exception e) {
                System.err.println("Failed to initialize Elastic APM: " + e.getMessage());
                e.printStackTrace();
            }
        }, setupDelay, TimeUnit.SECONDS);
    }

    private void initializeApmAgent() {
        if (serverUrl == null || serverUrl.isEmpty()) {
            System.out.println("Elastic APM initialization skipped: 'serverUrl' is missing or empty.");
            return;
        }
        if (secretToken == null || secretToken.isEmpty()) {
            System.out.println("Elastic APM initialization skipped: 'secretToken' is missing or empty.");
            return;
        }

        Map<String, String> apmProps = new HashMap<>();
        apmProps.put("application_packages", applicationPackages);
        apmProps.put("capture_body", captureBody ? "all" : "off");
        apmProps.put("enable_http_tracing", String.valueOf(enableHttpTracing));
        apmProps.put("environment", environment);
        apmProps.put("log_ecs_reformatting", logReformatting);
        apmProps.put("log_level", logLevel);
        apmProps.put("secret_token", secretToken);
        apmProps.put("server_url", serverUrl);
        apmProps.put("service_name", serviceName);
        ElasticApmAttacher.attach(apmProps);
    }

    // Getters and Setters

    /**
     * @return the server URL for the Elastic APM server.
     */
    public String getServerUrl() {
        return serverUrl;
    }

    /**
     * Sets the server URL for the Elastic APM server.
     * @param serverUrl the server URL to set.
     */
    public void setServerUrl(String serverUrl) {
        this.serverUrl = serverUrl;
    }

    /**
     * @return the service name reported to Elastic APM.
     */
    public String getServiceName() {
        return serviceName;
    }

    /**
     * Sets the service name reported to Elastic APM.
     * @param serviceName the service name to set.
     */
    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    /**
     * @return the environment (e.g., dev, prod) reported to Elastic APM.
     */
    public String getEnvironment() {
        return environment;
    }

    /**
     * Sets the environment reported to Elastic APM.
     * @param environment the environment to set.
     */
    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    /**
     * @return the application packages for Elastic APM instrumentation.
     */
    public String getApplicationPackages() {
        return applicationPackages;
    }

    /**
     * Sets the application packages for Elastic APM instrumentation.
     * @param applicationPackages the application packages to set.
     */
    public void setApplicationPackages(String applicationPackages) {
        this.applicationPackages = applicationPackages;
    }

    /**
     * @return the log level for Elastic APM.
     */
    public String getLogLevel() {
        return logLevel;
    }

    /**
     * Sets the log level for Elastic APM.
     * @param logLevel the log level to set.
     */
    public void setLogLevel(String logLevel) {
        this.logLevel = logLevel;
    }

    /**
     * @return the secret token for authenticating with Elastic APM server.
     */
    public String getSecretToken() {
        return secretToken;
    }

    /**
     * Sets the secret token for authenticating with Elastic APM server.
     * @param secretToken the secret token to set.
     */
    public void setSecretToken(String secretToken) {
        this.secretToken = secretToken;
    }

    /**
     * @return true if HTTP tracing is enabled, false otherwise.
     */
    public boolean isEnableHttpTracing() {
        return enableHttpTracing;
    }

    /**
     * Enables or disables HTTP tracing.
     * @param enableHttpTracing the value to set.
     */
    public void setEnableHttpTracing(boolean enableHttpTracing) {
        this.enableHttpTracing = enableHttpTracing;
    }

    /**
     * @return the log reformatting setting.
     */
    public String getLogReformatting() {
        return logReformatting;
    }

    /**
     * Sets the log reformatting setting.
     * @param logReformatting the value to set.
     */
    public void setLogReformatting(String logReformatting) {
        this.logReformatting = logReformatting;
    }

    /**
     * @return true if request bodies should be captured, false otherwise.
     */
    public boolean isCaptureBody() {
        return captureBody;
    }

    /**
     * Enables or disables capturing of request bodies.
     * @param captureBody the value to set.
     */
    public void setCaptureBody(boolean captureBody) {
        this.captureBody = captureBody;
    }
    /**
     * @return the delay in seconds before initializing Elastic APM.
     */
    public int getSetupDelay() {
        return setupDelay;
    }

    /**
     * Sets the delay in seconds before initializing Elastic APM.
     * @param setupDelay the delay to set.
     */
    public void setSetupDelay(int delayInSeconds) {
        this.setupDelay = delayInSeconds;
    }

}
