package net.evolveip.ossmosis.api.config.auth;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.utils.service.AuthorizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.HandlerMapping;

@Component
@Slf4j
public class OssmosisEnterprisePathParamInterceptor implements HandlerInterceptor {

  private final AuthorizationService authorizationService;

  @Autowired
  public OssmosisEnterprisePathParamInterceptor(AuthorizationService authorizationService) {
    this.authorizationService = authorizationService;
  }

  @Override
  public boolean preHandle(@NonNull HttpServletRequest request,
      @NonNull HttpServletResponse response, @NonNull Object object) {

    Map pathVariables = (Map) request.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
    if (pathVariables != null) {
      String enterpriseId = (String) pathVariables.get("enterpriseId");
      if (enterpriseId != null && !authorizationService.doesCurrentUserHaveAccessToEnterprise(
          enterpriseId)) {
        logger.error("Path param enterpriseId security failure for enterpriseId: " + enterpriseId);
        throw new AccessDeniedException("Access denied to enterpriseId: " + enterpriseId);
      }
    }
    return true;
  }

}
