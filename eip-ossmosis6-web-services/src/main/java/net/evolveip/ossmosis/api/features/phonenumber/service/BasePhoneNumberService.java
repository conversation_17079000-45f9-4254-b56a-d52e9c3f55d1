package net.evolveip.ossmosis.api.features.phonenumber.service;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.core.BaseGenericService;
import net.evolveip.ossmosis.api.features.enterprise.repository.EnterpriseRepository;
import net.evolveip.ossmosis.api.features.phonenumber.common.DtoUtils;
import net.evolveip.ossmosis.api.features.phonenumber.common.IdValidator;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.HttpPostRequestPhoneNumberDTO;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.HttpPutRequestPhoneNumberDTO;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.HttpRequestPhoneNumberDTO;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.HttpResponsePhoneNumberDTO;
import net.evolveip.ossmosis.api.features.phonenumber.common.enums.CountryCode;
import net.evolveip.ossmosis.api.features.phonenumber.entity.PhoneNumber;
import net.evolveip.ossmosis.api.features.phonenumber.mapper.PhoneNumberMapper;
import net.evolveip.ossmosis.api.features.phonenumber.repository.PhoneNumberRepository;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.service.AuthorizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;

@Slf4j
public abstract class BasePhoneNumberService
    extends BaseGenericService
    implements DtoUtils, IdValidator<PhoneNumber, Long> {

  protected final PhoneNumberRepository phoneNumberRepository;
  protected final AuthorizationService authorizationService;
  protected final EnterpriseRepository enterpriseRepository;
  protected final CarrierService carrierService;
  protected final PhoneNumberStatusService phoneNumberStatusService;
  protected final PhoneNumberMapper mapper;
  protected final Set<String> referenceCountryCodes = Arrays.stream(CountryCode.values())
      .map(CountryCode::getDialingCode).collect(Collectors.toSet());

  @Autowired
  protected BasePhoneNumberService(
      PhoneNumberRepository phoneNumberRepository,
      AuthorizationService authorizationService,
      CarrierService carrierService,
      EnterpriseRepository enterpriseRepository,
      PhoneNumberStatusService phoneNumberStatusService,
      PhoneNumberMapper mapper
  ) {
    this.phoneNumberRepository = phoneNumberRepository;
    this.authorizationService = authorizationService;
    this.enterpriseRepository = enterpriseRepository;
    this.carrierService = carrierService;
    this.phoneNumberStatusService = phoneNumberStatusService;
    this.mapper = mapper;
  }

  // checks section
  protected ApiResponse<List<HttpResponsePhoneNumberDTO>> validatePostRequest(
      List<HttpPostRequestPhoneNumberDTO> request) {
    return validateRequest(new ArrayList<>(request));
  }

  protected ApiResponse<List<HttpResponsePhoneNumberDTO>> validatePutRequest(
      List<HttpPutRequestPhoneNumberDTO> request) {
    return validateRequest(new ArrayList<>(request));
  }

  protected ApiResponse<List<HttpResponsePhoneNumberDTO>> validateRequest(
      List<HttpRequestPhoneNumberDTO> request) {

    ApiResponse<List<HttpResponsePhoneNumberDTO>> response;

    // check if there are duplicates in the incoming phone number list itself.
    // It does not check for existing numbers in the database.
    response = validatePhoneNumbersListForDuplicates(extractSliceToList(request,
        HttpRequestPhoneNumberDTO::getPString));
    if (response != null) {
      return response;
    }

    // verify that carrierIds are valid:
    response = validateCarrierIds(
        extractSliceToSet(request, p -> Integer.parseInt(p.getCarrierId())));
    if (response != null) {
      return response;
    }

    // verify phone number status ids are valid:
    response = validatePhoneNumberStatusIds(
        extractSliceToSet(request, p -> Integer.parseInt(p.getStatusId())));
    if (response != null) {
      return response;
    }

    // verify that country codes are valid:
    response = validateCountryCodes(
        extractSliceToSet(request, HttpRequestPhoneNumberDTO::getCountryCode));
    if (response != null) {
      return response;
    }

    // verify that btn country codes are valid:
    response = validateCountryCodes(
        extractSliceToSet(request, HttpRequestPhoneNumberDTO::getBtnCountryCode));
    if (response != null) {
      return response;
    }

    return null;
  }

  protected <T> ApiResponse<T> checkCarrierIdOrCarrieNameResponse(
      Optional<Integer> carrierId,
      Optional<String> carrierName) {
    if (carrierId.isPresent() && carrierName.isPresent()) {
      String message = "Provide either carrierId or carrierName, but not both.";
      return genericBadResponse(message, HttpStatus.BAD_REQUEST);
    }
    return null;
  }

  protected <T> ApiResponse<T> checkStatusIdOrStatusNameResponse(
      Optional<Integer> statusId,
      Optional<String> statusName) {
    if (statusId.isPresent() && statusName.isPresent()) {
      String message = "Provide either statusId or statusName, but not both.";
      return genericBadResponse(message, HttpStatus.BAD_REQUEST);
    }
    return null;
  }

  protected <T> ApiResponse<T> validateAuthorization(String enterpriseId) {
    if (!authorizationService.doesCurrentUserHaveAccessToEnterprise(enterpriseId)) {
      return accessDeniedResponse(enterpriseId);
    }
    return null;
  }

  protected <T> ApiResponse<T> validatePhoneNumbersListForDuplicates(
      List<String> phoneNumbersPlusCountryCodes) {
    Set<String> duplicates = hasDuplicates(phoneNumbersPlusCountryCodes);
    if (!duplicates.isEmpty()) {
      return duplicatePhoneNumbersResponse(duplicates);
    }
    return null;
  }

  protected <T> ApiResponse<T> validateCarrierIds(Set<Integer> carrierIds) {
    if (!carrierIds.isEmpty()) {
      Set<Integer> invalidCarrierIds = carrierService.getInvalidIds(carrierIds);
      if (!invalidCarrierIds.isEmpty()) {
        return carrierService.idsNotFoundResponse(invalidCarrierIds);
      } else {
        carrierService.notFoundResponse();
      }
    }
    return null;
  }

  protected <T> ApiResponse<T> validatePhoneNumberIds(
      String enterpriseId,
      List<HttpPutRequestPhoneNumberDTO> request
  ) {
    Set<Long> invalidPhoneNumberIds = new HashSet<>();
    // check if the incoming phoneNumberIds list itself has duplicates:
    List<Long> phoneNumberIds = extractSliceToList(request,
        p -> Long.parseLong(p.getPhoneNumberId()));
    invalidPhoneNumberIds.addAll(hasDuplicates(phoneNumberIds));
    if (!invalidPhoneNumberIds.isEmpty()) {
      return duplicatePhoneNumberIdsResponse(invalidPhoneNumberIds);
    }

    // check if the phoneNumberIds exist in the database:
    // check if the phoneNumberIds belong to the enterpriseId:
    // check if the phoneNumberIds match the provided country code and phone number:
    Set<Long> unmatchedPhoneNumberIdsWithPhoneNumbers = new HashSet<>();
    for (HttpPutRequestPhoneNumberDTO dto : request) {
      Long phoneNumberId = Long.parseLong(dto.getPhoneNumberId());
      Optional<PhoneNumber> phoneNumberEntity = phoneNumberRepository.findByEnterpriseEnterpriseIdAndPhoneNumberId(
          enterpriseId, phoneNumberId);
      if (phoneNumberEntity.isEmpty()) {
        invalidPhoneNumberIds.add(phoneNumberId);
      } else {
        PhoneNumber existingPhoneNumber = phoneNumberEntity.get();
        if (!existingPhoneNumber.getCountryCode().equalsIgnoreCase(dto.getCountryCode())
            || !existingPhoneNumber.getPhoneNumber().equalsIgnoreCase(dto.getPhoneNumber())) {
          unmatchedPhoneNumberIdsWithPhoneNumbers.add(phoneNumberId);
        }
      }
    }
    if (!invalidPhoneNumberIds.isEmpty()) {
      return invalidPhoneNumberIdsResponse(invalidPhoneNumberIds);
    }
    if (!unmatchedPhoneNumberIdsWithPhoneNumbers.isEmpty()) {
      return unmatchedPhoneNumberIdsWithPhoneNumbersResponse(
          unmatchedPhoneNumberIdsWithPhoneNumbers);
    }
    return null;
  }

  protected <T> ApiResponse<T> validatePhoneNumberStatusIds(Set<Integer> statusIds) {
    if (!statusIds.isEmpty()) {
      Set<Integer> invalidStatusIds = phoneNumberStatusService.getInvalidIds(statusIds);
      if (!invalidStatusIds.isEmpty()) {
        return phoneNumberStatusService.idsNotFoundResponse(invalidStatusIds);
      }
    } else {
      return phoneNumberStatusService.notFoundResponse();
    }
    return null;
  }

  protected <T> ApiResponse<T> validateCountryCodes(Set<String> countryCodes) {
    if (!countryCodes.isEmpty()) {
      Set<String> invalidCountryCodes = checkCountryCode(countryCodes);
      if (!invalidCountryCodes.isEmpty()) {
        return countryCodesInvalidResponse(invalidCountryCodes);
      }
    } else {
      return countryCodesInvalidResponse();
    }
    return null;
  }

  private Set<String> checkCountryCode(Set<String> dialingCodes) {
    Set<String> invalidDialingCodes = new HashSet<>();
    for (String dialingCode : dialingCodes) {
      if (!referenceCountryCodes.contains(dialingCode)) {
        invalidDialingCodes.add(dialingCode);
      }
    }
    return invalidDialingCodes;
  }

  protected <T> ApiResponse<T> validatePhoneNumberIds(List<Long> phoneNumberIds) {
    if (!phoneNumberIds.isEmpty()) {
      Set<Long> invalidPhoneNumberIds = getInvalidEntries(new HashSet<>(phoneNumberIds),
          phoneNumberRepository);
      if (!invalidPhoneNumberIds.isEmpty()) {
        return invalidPhoneNumberIdsResponse(invalidPhoneNumberIds);
      }
    }
    return null;
  }

  protected <T> ApiResponse<T> validatePhoneNumberId(String enterprise, Long phoneNumberId) {
    Optional<PhoneNumber> phoneNumber = phoneNumberRepository.findByEnterpriseEnterpriseIdAndPhoneNumberId(
        enterprise, phoneNumberId);
    if (phoneNumber.isEmpty()) {
      return invalidPhoneNumberIdsResponse(Set.of(phoneNumberId));
    }
    return null;
  }

  // responses section:
  protected <T> ApiResponse<T> emptyCandidateListResponse() {
    String message = "No candidate phone numbers left to add";
    logger.info(message);
    return genericEmptyResponse(message);
  }

  protected <T> ApiResponse<T> duplicatePhoneNumbersResponse(Set<String> phoneNumbers) {
    String message = String.format("Some phone numbers are duplicates: %s",
        String.join(", ", phoneNumbers));
    logger.info(message);
    return genericBadResponse(message, HttpStatus.BAD_REQUEST);
  }

  protected <T> ApiResponse<T> unmatchedPhoneNumberIdsWithPhoneNumbersResponse(
      Set<Long> phoneNumberIds) {
    return invalidPhoneNumberIdsResponse(phoneNumberIds,
        "Some phone numbers ids do not match the existing country code/phone numbers: %s");
  }

  protected <T> ApiResponse<T> duplicatePhoneNumberIdsResponse(Set<Long> phoneNumberIds) {
    return invalidPhoneNumberIdsResponse(phoneNumberIds,
        "Some phone numbers ids are duplicate: %s");
  }

  protected <T> ApiResponse<T> invalidPhoneNumberIdsResponse(Set<Long> phoneNumberIds) {
    return invalidPhoneNumberIdsResponse(phoneNumberIds, "Invalid phone number ids: %s");
  }

  protected <T> ApiResponse<T> invalidPhoneNumberIdsResponse(Set<Long> phoneNumberIds,
      String initialMessage) {
    String message = String.format(initialMessage,
        String.join(", ", phoneNumberIds.toString()));
    logger.info(message);
    return genericEmptyResponse(message);
  }

  protected ApiResponse<Boolean> invalidPhoneNumberIdsResponse(String message) {
    logger.info(message);
    return genericBooleanResponse(message);
  }

  protected <T> ApiResponse<T> accessDeniedResponse(String enterpriseId) {
    String message = String.format("Access denied to enterprise id: %s", enterpriseId);
    logger.info(message);
    return genericBadResponse(message, HttpStatus.FORBIDDEN);
  }

  protected <T> ApiResponse<T> countryCodesInvalidResponse(Set<String> dialingCodes) {
    String message = String.format(
        "Some of the dialing codes are invalid: %s", String.join(", ", dialingCodes));
    logger.info(message);
    return genericBadResponse(message, HttpStatus.BAD_REQUEST);
  }

  protected <T> ApiResponse<T> countryCodesInvalidResponse() {
    String message = "No valid country codes are valid";
    logger.info(message);
    return genericBadResponse(message, HttpStatus.BAD_REQUEST);
  }
}