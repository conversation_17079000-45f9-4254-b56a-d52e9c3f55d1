package net.evolveip.ossmosis.api.features.login.entity;

import jakarta.persistence.Embeddable;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Embeddable
public class LoginResourcePermissionId implements Serializable {

  private Long loginId;

  private Integer resourceId;

  private Integer permissionId;

}
