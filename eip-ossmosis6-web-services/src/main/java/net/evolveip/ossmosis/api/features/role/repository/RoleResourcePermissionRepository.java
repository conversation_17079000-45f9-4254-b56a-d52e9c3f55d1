package net.evolveip.ossmosis.api.features.role.repository;

import java.util.List;
import net.evolveip.ossmosis.api.features.role.entity.RoleResourcePermission;
import net.evolveip.ossmosis.api.features.role.entity.RoleResourcePermissionId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface RoleResourcePermissionRepository extends
    JpaRepository<RoleResourcePermission, RoleResourcePermissionId> {

  @Query(value = "SELECT rrp from RoleResourcePermission rrp join fetch rrp.resource join fetch rrp.permission join fetch rrp.role where rrp.role.roleId in (:roleIds)")
  List<RoleResourcePermission> findAllByRoleIdIn(List<Integer> roleIds);

  @Query(nativeQuery = true, value = "SELECT role_id, resource_id, permission_id FROM partnerprovider.role_resource_permission WHERE role_id = :roleId")
  List<RoleResourcePermission> findAllByRoleId(Integer roleId);

  @Query(nativeQuery = true, value = "SELECT * from partnerprovider.fn_roles_merge_permissions(:roleId, :jsonText)")
  Boolean mergePermissionSet(Integer roleId, String jsonText);

  void deleteAllByRoleRoleId(int roleId);
}