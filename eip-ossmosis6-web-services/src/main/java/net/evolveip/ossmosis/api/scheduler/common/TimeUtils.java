package net.evolveip.ossmosis.api.scheduler.common;

import static net.evolveip.ossmosis.api.scheduler.common.TimeConstants.DATE_FORMATTER_EMAIL;
import static net.evolveip.ossmosis.api.scheduler.common.TimeConstants.DATE_FORMATTER_FILENAMES;
import static net.evolveip.ossmosis.api.scheduler.common.TimeConstants.DATE_FORMATTER_READABLE;
import static net.evolveip.ossmosis.api.scheduler.common.TimeConstants.EST;
import static net.evolveip.ossmosis.api.scheduler.common.TimeConstants.UTC;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.TimeZone;

public interface TimeUtils {

  default String convertToTimeZone(ZonedDateTime originalZonedDateTime, TimeZone targetTimeZone) {
    ZoneId originalZoneId = originalZonedDateTime.getZone();
    ZonedDateTime offsetDateTime = originalZonedDateTime.withZoneSameInstant(originalZoneId)
        .toLocalDateTime().atZone(originalZoneId);
    ZonedDateTime convertedDateTime = offsetDateTime.withZoneSameInstant(targetTimeZone.toZoneId());
    DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(DATE_FORMATTER_READABLE);
    return convertedDateTime.format(dateFormatter);
  }

  default String convertToEmailDateTime(ZonedDateTime originalZonedDateTime, TimeZone targetTimeZone) {
    ZoneId originallZoneId = originalZonedDateTime.getZone();
    ZonedDateTime offsetDateTime = originalZonedDateTime.withZoneSameInstant(originallZoneId)
        .toLocalDateTime().atZone(originallZoneId);
    ZonedDateTime convertedDateTime = offsetDateTime.withZoneSameInstant(targetTimeZone.toZoneId());
    DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(DATE_FORMATTER_EMAIL);
    return convertedDateTime.format(dateFormatter);
  }

  static ZonedDateTime toEST(ZonedDateTime zonedDateTime) {
    ZonedDateTime zonedDateTimeInUTC;
    if (!zonedDateTime.getZone().equals(ZoneId.of(UTC))) {
      zonedDateTimeInUTC = zonedDateTime.withZoneSameInstant(ZoneId.of(UTC));
    } else {
      zonedDateTimeInUTC = zonedDateTime;
    }
    return zonedDateTimeInUTC.withZoneSameInstant(ZoneId.of(EST));
  }

  static ZonedDateTime toUTC(ZonedDateTime zonedDateTime) {
    ZonedDateTime zonedDateTimeInUTC;
    if (!zonedDateTime.getZone().equals(ZoneId.of(UTC))) {
      zonedDateTimeInUTC = zonedDateTime.withZoneSameInstant(ZoneId.of(UTC));
    } else {
      zonedDateTimeInUTC = zonedDateTime;
    }
    return zonedDateTimeInUTC.withZoneSameInstant(ZoneId.of(UTC));
  }

  default String now(ZonedDateTime zonedDateTime) {
    String zoneId = ZoneId.systemDefault().getId();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMATTER_FILENAMES);
    String formattedDateTime = zonedDateTime.format(formatter);
    return formattedDateTime + zoneId;
  }

  default ZonedDateTime toZonedDateTime(Date date) {
    return toZonedDateTime(date, ZoneId.systemDefault());
  }

  default ZonedDateTime toZonedDateTime(Date date, ZoneId zoneId) {
    return ZonedDateTime.ofInstant(date.toInstant(), zoneId);
  }

  default ZonedDateTime nowEst() {
    return toEST(getNow());
  }

  default ZonedDateTime getNow() {
    return ZonedDateTime.now();
  }
}