package net.evolveip.ossmosis.api.features.phonenumber.mapper;

import net.evolveip.ossmosis.api.features.phonenumber.common.dto.CarrierDTO;
import net.evolveip.ossmosis.api.features.phonenumber.entity.Carrier;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface CarrierMapper {

  @Mapping(ignore = true, target = "carrierId")
  Carrier toCarrier(CarrierDTO carrierDto);

  CarrierDTO toCarrierDto(Carrier carrier);
}