package net.evolveip.ossmosis.api.features.group.controller;

import jakarta.validation.Valid;
import java.io.File;
import java.util.List;
import net.evolveip.ossmosis.api.features.enterprise.validator.annotations.ValidEnterprise;
import net.evolveip.ossmosis.api.features.group.common.constants.GroupConstants;
import net.evolveip.ossmosis.api.features.group.common.dto.GroupRequestDTO;
import net.evolveip.ossmosis.api.features.group.common.dto.GroupResponseDTO;
import net.evolveip.ossmosis.api.features.group.service.GroupService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.validators.annotations.EnterpriseId;
import net.evolveip.ossmosis.api.utils.validators.annotations.GroupId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

@Validated
@RestController
@RequestMapping(value = "/enterprise/{enterpriseId}/groups")
public class GroupController {

  private final GroupService groupService;

  @Autowired
  public GroupController(GroupService groupService) {
    this.groupService = groupService;
  }

  @Secured(GroupConstants.GROUPS_READ)
  @GetMapping
  public ResponseEntity<Page<GroupResponseDTO>> doGet(
      @PathVariable @EnterpriseId String enterpriseId,
      @RequestParam(value = "filterValue", required = false, defaultValue = "") String filterValue,
      @SortDefault(sort = "groupId", direction = Direction.ASC) @PageableDefault(size = 15) final Pageable pageable
  ) {
    Page<GroupResponseDTO> page = groupService.processGet(enterpriseId, filterValue, pageable);
    return ResponseEntity.ok(page);
  }

  @Secured(GroupConstants.GROUPS_READ)
  @GetMapping("/list")
  public ResponseEntity<ApiResponse<List<GroupResponseDTO>>> doGetList(
      @PathVariable @EnterpriseId String enterpriseId
  ) {
    ApiResponse<List<GroupResponseDTO>> apiResponse = groupService.processGetList(enterpriseId);
    return new ResponseEntity<>(apiResponse,
        org.springframework.http.HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(GroupConstants.GROUPS_READ)
  @GetMapping("/{groupId}")
  public ResponseEntity<ApiResponse<GroupResponseDTO>> doGetById(
      @PathVariable @EnterpriseId String enterpriseId,
      @PathVariable @GroupId String groupId
  ) {
    ApiResponse<GroupResponseDTO> apiResponse = groupService.processGetById(enterpriseId, groupId);
    return new ResponseEntity<>(apiResponse,
        org.springframework.http.HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(GroupConstants.GROUPS_CREATE)
  @PostMapping
  public ResponseEntity<?> doCreate(
      @ValidEnterprise @PathVariable String enterpriseId,
      @Valid @RequestBody GroupRequestDTO groupRequestDTO
  ) {
    groupRequestDTO.setEnterpriseId(enterpriseId);
    groupService.processCreate(groupRequestDTO);
    return ResponseEntity.accepted().build();
  }

  @Secured(GroupConstants.GROUPS_UPDATE)
  @PutMapping
  public ResponseEntity<?> doUpdate(
      @PathVariable @EnterpriseId String enterpriseId,
      @Valid @RequestBody GroupRequestDTO groupRequestDTO
  ) {
    groupService.processUpdate(groupRequestDTO);
    return ResponseEntity.accepted().build();
  }

  @Secured(GroupConstants.GROUPS_DELETE)
  @DeleteMapping("/{groupId}")
  public ResponseEntity<ApiResponse<Boolean>> doDelete(
      @PathVariable @EnterpriseId String enterpriseId,
      @PathVariable String groupId
  ) {
    groupService.processDelete(enterpriseId, groupId);
    return ResponseEntity.accepted().build();
  }

  @Secured(GroupConstants.GROUPS_READ)
  @GetMapping(value = "/download")
  public ResponseEntity<FileSystemResource> doDownload(
      @PathVariable @EnterpriseId String enterpriseId,
      @RequestParam(value = "filterValue", required = false, defaultValue = "") String filterValue,
      @RequestParam(value = "format", required = false, defaultValue = "csv") String format,
      @RequestParam(value = "fileName", required = false, defaultValue = "Groups") String fileName,
      @RequestParam(value = "timezone", required = false, defaultValue = "") String timezone
  ) {
    try {
      File file = groupService.processDownload(enterpriseId, filterValue, format, fileName,
          timezone);

      String contentType = switch (format) {
        case "csv" -> "text/csv";
        case "pdf" -> "application/pdf";
        case "xlsx" -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        case "json" -> "application/json";
        default -> "text/csv";
      };

      return ResponseEntity.ok()
          .header("Content-Disposition", "attachment; filename=" + fileName)
          .contentLength(file.length())
          .contentType(MediaType.parseMediaType(contentType))
          .body(new FileSystemResource(file));
    } catch (Exception e) {
      throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR,
          "Unable to download groups", e);
    }
  }
}
