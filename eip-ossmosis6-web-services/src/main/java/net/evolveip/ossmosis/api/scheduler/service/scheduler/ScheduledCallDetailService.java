package net.evolveip.ossmosis.api.scheduler.service.scheduler;

import static net.evolveip.ossmosis.api.scheduler.common.constants.GenericServiceConstants.NOT_APPLICABLE;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.FREQUENCY;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.FREQUENCY_UNITS;

import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.enterprise.service.EnterpriseValidationService;
import net.evolveip.ossmosis.api.features.enterprise.service.EnterpriseService;
import net.evolveip.ossmosis.api.scheduler.common.dto.CronScheduleJobDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.SimpleScheduleJobDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.TriggerDTO;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateCronScheduleJob;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateSimpleScheduleJob;
import net.evolveip.ossmosis.api.scheduler.common.response.HttpResponseGetScheduledJob;
import net.evolveip.ossmosis.api.scheduler.mapper.MapperScheduledJob;
import net.evolveip.ossmosis.api.scheduler.service.trigger.JobDetailService;
import net.evolveip.ossmosis.api.scheduler.service.trigger.TriggerBuilderService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.quartz.CronTrigger;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ScheduledCallDetailService extends AbstractSchedulerReportService {

  private final MapperScheduledJob mapperScheduledJob;
  private final EnterpriseValidationService validationService;


  @Autowired
  public ScheduledCallDetailService(Scheduler scheduler,
      EnterpriseService enterpriseService,
      EnterpriseValidationService validationService,
      MapperScheduledJob mapperScheduledJob,
      TriggerBuilderService triggerBuilderService,
      JobDetailService jobDetailService
  ) {
    super(scheduler, enterpriseService, triggerBuilderService, jobDetailService);
    this.mapperScheduledJob = mapperScheduledJob;
    this.validationService = validationService;
  }

  public ApiResponse<HttpResponseGetScheduledJob> processCreateSimpleScheduleJob(
      HttpRequestCreateSimpleScheduleJob httpRequestCreateSimpleScheduleJob) {
    try {
      SimpleScheduleJobDTO entryDto = mapperScheduledJob.toDTO(httpRequestCreateSimpleScheduleJob);

      // check the enterpriseId:
      String enterpriseId = entryDto.getEmailDefinition().getEnterpriseId();
      validationService.validateEnterpriseId(enterpriseId);

      // build the jobDetail:
      JobDetail jobDetail = jobDetailService.createJobDetail(entryDto,
          getEnterpriseName(enterpriseId));

      //
      Trigger trigger = triggerBuilderService.buildTrigger(scheduler, jobDetail,
          entryDto.getJobScheduleDefinition());

      // schedule the job and its trigger:
      scheduler.scheduleJob(jobDetail, trigger);
      if (!entryDto.getJobDefinition().getEnabled()) {
        scheduler.pauseJob(jobDetail.getKey());
      }

      JobKey jobKey = jobDetail.getKey();
      boolean triggerState = getJobState(enterpriseId, jobKey);
      return response(buildHttpResponse(scheduler, jobKey, triggerState, getTriggers(jobKey)));
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  /***
   * It processes the POST request in order to create a scheduled job for a call detailed report
   * @param httpRequestCreateCronScheduleJob a compound DTO containing email, cron and job parameters
   * @return a HttpCreateResponseScheduledJob encapsulating the message and success status
   */
  public ApiResponse<HttpResponseGetScheduledJob> processCreateCronScheduleJob(
      HttpRequestCreateCronScheduleJob httpRequestCreateCronScheduleJob) {
    try {
      CronScheduleJobDTO entryDto = mapperScheduledJob.toDto(httpRequestCreateCronScheduleJob);

      // check the enterpriseId:
      String enterpriseId = entryDto.getEmailDefinition().getEnterpriseId();
      validationService.validateEnterpriseId(enterpriseId);

      // build JobDetail:
      JobDetail jobDetail = jobDetailService.createJobDetail(entryDto,
          getEnterpriseName(enterpriseId));

      // build the cron trigger, to be plugged in into the scheduler:
      CronTrigger cronTrigger = triggerBuilderService.buildTrigger(jobDetail,
          entryDto.getJobScheduleDefinition());

      // schedule the job and its trigger:
      scheduler.scheduleJob(jobDetail, cronTrigger);
      if (!entryDto.getJobDefinition().getEnabled()) {
        scheduler.pauseJob(jobDetail.getKey());
      }
      JobKey jobKey = jobDetail.getKey();
      boolean triggerState = getJobState(enterpriseId, jobKey);
      return response(buildHttpResponse(scheduler, jobKey, triggerState, getTriggers(jobKey)));
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  /***
   * Retrieves all the jobs created under the enterpriseId/jobGroup passed in as argument.
   * If addDetails exists and is "true", the details for each job is retrieved as well
   * @param enterpriseId the enterpriseId for which the jobIds are being retrieved
   * @param addDetails the boolean flag that signals the retrieval of the details of each jobId
   * @return a list of either String(s) or HttpResponseGetScheduledJob
   */
  public ApiResponse<?> processGet(String enterpriseId, Boolean addDetails) {
    try {
      // check the enterpriseId:
      validationService.validateEnterpriseId(enterpriseId);
      Set<JobKey> jobKeys = getJobs(enterpriseId);
      if (jobKeys.isEmpty()) {
        return noJobsFoundForEnterprise(enterpriseId);
      }
      if (addDetails) {
        return response(jobKeys.stream().map(jobKey -> {
          try {
            boolean triggerState = getJobState(enterpriseId, jobKey);
            return buildHttpResponse(scheduler, jobKey, triggerState, getTriggers(jobKey));
          } catch (SchedulerException e) {
            logger.error(e.getMessage());
            throw new RuntimeException(e);
          }
        }).toList());
      } else {
        return response(getJobsStates(enterpriseId));
      }
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  public Page<HttpResponseGetScheduledJob> processGetDetailsPageable(String enterpriseId, final
      Pageable pageable, String filterValue) {
    try {
      Set<JobKey> jobKeys = getJobs(enterpriseId);
      List<HttpResponseGetScheduledJob> list = jobKeys.stream().map(jobKey -> {
        try {
          boolean triggerState = getJobState(enterpriseId, jobKey);
          return buildHttpResponse(scheduler, jobKey, triggerState, getTriggers(jobKey));
        } catch (SchedulerException e) {
          logger.error(e.getMessage());
          throw new RuntimeException(e);
        }
      }).toList();
      List<HttpResponseGetScheduledJob> filteredList = filterJobList(list, filterValue);
      List<HttpResponseGetScheduledJob> sortedList = sortJobList(filteredList, pageable.getSort());
      List<HttpResponseGetScheduledJob> pagedList = IntStream.range(0, sortedList.size()).mapToObj(index -> {
        int start = pageable.getPageNumber() * pageable.getPageSize();
        int end = Math.min((start + pageable.getPageSize()), sortedList.size());
        if (index >= start && index < end) {
          return sortedList.get(index);
        } else {
          return null;
        }
      }).filter(Objects::nonNull).toList();

      return new PageImpl<>(pagedList, pageable, sortedList.size());

    } catch (Exception e) {
      String message = String.format(e.getMessage());
      logger.error(message);
      return new PageImpl<>(new ArrayList<>(), pageable, 0);
    }
  }

  /***
   * Retrieves a job by enterpriseId and jobId
   * @param enterpriseId the enterprise the job belongs to
   * @param jobId the job id for lookup
   * @return a DTO with all details associated with the jobId
   */
  public ApiResponse<HttpResponseGetScheduledJob> processGetByJobId(String enterpriseId,
      String jobId) {
    try {
      // check the enterpriseId:
      validationService.validateEnterpriseId(enterpriseId);
      Set<JobKey> keySet = getJobs(enterpriseId);
      if (keySet.isEmpty()) {
        return noJobsFoundForEnterprise(enterpriseId);
      } else if (keySet.contains(new JobKey(jobId, enterpriseId))) {
        JobKey jobKey = new JobKey(jobId, enterpriseId);
        boolean triggerState = getJobState(enterpriseId, jobKey);
        return response(
            buildHttpResponse(scheduler, jobKey, triggerState, getTriggers(jobKey)));
      } else {
        return noJobIdForEnterprise(enterpriseId, jobId);
      }
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  /***
   * Retrieves a job by enterpriseId and jobId
   * @param enterpriseId the enterprise the job belongs to
   * @param jobId the job id for lookup
   * @return a DTO with all details associated with the jobId
   */
  public ApiResponse<?> processGetRunTimesById(String enterpriseId, String jobId) {
    try {
      // check the enterpriseId:
      validationService.validateEnterpriseId(enterpriseId);
      Set<JobKey> keySet = getJobs(enterpriseId);
      if (keySet.isEmpty()) {
        return noJobsFoundForEnterprise(enterpriseId);
      }
      JobKey jobKey = new JobKey(jobId, enterpriseId);
      if (keySet.contains(jobKey)) {
        return response(getTriggers(jobKey));
      } else {
        return noJobIdForEnterprise(enterpriseId, jobId);
      }
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  /***
   * Delete a JobKey for a particular enterpriseId.
   * @param enterpriseId the enterprise for which the jobkey is to be looked up
   * @param jobId the jobId to be deleted
   * @return true or false
   */
  public ApiResponse<Boolean> processDelete(String enterpriseId, String jobId) {
    try {
      validationService.validateEnterpriseId(enterpriseId);
      Set<JobKey> keySet = getJobs(enterpriseId);
      if (keySet.isEmpty()) {
        return noJobsFoundForEnterprise(enterpriseId);
      } else if (keySet.contains(new JobKey(jobId, enterpriseId))) {
        scheduler.deleteJob(new JobKey(jobId, enterpriseId));
        scheduler.deleteCalendar(jobId);
        return response(true);
      } else {
        return noJobIdForEnterprise(enterpriseId, jobId);
      }
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  public ApiResponse<?> processPause(String enterpriseId, String jobId) {
    try {
      validationService.validateEnterpriseId(enterpriseId);
      Set<JobKey> keySet = getJobs(enterpriseId);
      if (keySet.isEmpty()) {
        return noJobsFoundForEnterprise(enterpriseId);
      } else if (keySet.contains(new JobKey(jobId, enterpriseId))) {
        scheduler.pauseJob(new JobKey(jobId, enterpriseId));
        return response(getJobState(enterpriseId, jobId));
      } else {
        return noJobIdForEnterprise(enterpriseId, jobId);
      }
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  public ApiResponse<?> processResume(String enterpriseId, String jobId) {
    try {
      // check the enterpriseId:
      validationService.validateEnterpriseId(enterpriseId);
      Set<JobKey> keySet = getJobs(enterpriseId);
      if (keySet.isEmpty()) {
        return noJobsFoundForEnterprise(enterpriseId);
      } else if (keySet.contains(new JobKey(jobId, enterpriseId))) {
        scheduler.resumeJob(new JobKey(jobId, enterpriseId));
        return response(getJobState(enterpriseId, jobId));
      } else {
        return noJobIdForEnterprise(enterpriseId, jobId);
      }
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  public ApiResponse<?> processStatus(String enterpriseId, String jobId) {
    try {
      // check the enterpriseId:
      validationService.validateEnterpriseId(enterpriseId);
      Set<JobKey> keySet = getJobs(enterpriseId);
      if (keySet.isEmpty()) {
        return noJobsFoundForEnterprise(enterpriseId);
      } else if (keySet.contains(new JobKey(jobId, enterpriseId))) {
        return response(getJobState(enterpriseId, jobId));
      } else {
        return noJobIdForEnterprise(enterpriseId, jobId);
      }
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  public ApiResponse<Map<String, Integer>> processPauseAll(String enterpriseId) {
    try {
      // check the enterpriseId:
      validationService.validateEnterpriseId(enterpriseId);
      scheduler.pauseJobs(GroupMatcher.groupEquals(enterpriseId));
      return response(getJobsStatesCounters(enterpriseId));
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  public ApiResponse<Map<String, Integer>> processResumeAll(String enterpriseId) {
    try {
      // check the enterpriseId:
      validationService.validateEnterpriseId(enterpriseId);
      scheduler.resumeJobs(GroupMatcher.groupEquals(enterpriseId));
      return response(getJobsStatesCounters(enterpriseId));
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  public ApiResponse<Map<String, Integer>> processStatusAll(String enterpriseId) {
    try {
      // check the enterpriseId:
      validationService.validateEnterpriseId(enterpriseId);
      return response(getJobsStatesCounters(enterpriseId));
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  private List<TriggerDTO> getTriggers(JobKey jobKey)
      throws SchedulerException {
    List<? extends Trigger> triggerList = scheduler.getTriggersOfJob(jobKey);
    List<TriggerDTO> triggerDtoList = new ArrayList<>();
    for (Trigger trigger : triggerList) {
      TriggerDTO triggerDTO = TriggerDTO
          .builder()
          .jobGroup(trigger.getJobKey().getGroup())
          .jobName(trigger.getJobKey().getName())
          .startTime(
              trigger.getStartTime() == null ? NOT_APPLICABLE
                  : toZonedDateTime(trigger.getStartTime()).toString())
          .endTime(
              trigger.getEndTime() == null ? NOT_APPLICABLE
                  : toZonedDateTime(trigger.getEndTime()).toString())
          .previousFireTime(
              trigger.getPreviousFireTime() == null ? NOT_APPLICABLE
                  : toZonedDateTime(trigger.getPreviousFireTime()).toString())
          .nextFireTime(
              trigger.getNextFireTime() == null ? NOT_APPLICABLE
                  : toZonedDateTime(trigger.getNextFireTime()).toString())
          .triggerState(String.valueOf(getJobState(jobKey).get(jobKey.getName())))
          .build();
      triggerDtoList.add(triggerDTO);
    }
    return triggerDtoList;
  }

  private List<HttpResponseGetScheduledJob> filterJobList(List<HttpResponseGetScheduledJob> list, String filterValue) {
    List<HttpResponseGetScheduledJob> newList = new ArrayList<>();
    for (HttpResponseGetScheduledJob job: list) {
      String reportTitle = job.getJobDefinition().getJobData().getReportTitle().toLowerCase();
      List<String> emailList = job.getEmailDefinition().getToEmail();
      emailList.sort(String::compareTo);
      String frequencyUnits = String.valueOf(job.getJobScheduleDefinition().get(FREQUENCY_UNITS))
          .toLowerCase();
      String dataWindowUnits = String.valueOf(
          job.getJobDefinition().getJobData().getDataWindowUnits()).toLowerCase();
      String enabled = String.valueOf(
          !job.getJobRuntimes().get(0).getTriggerState().equals("PAUSED")).toLowerCase();
      if (reportTitle.contains(filterValue) ||
          (emailList.stream().anyMatch(email -> email.toLowerCase().contains(filterValue))) ||
          frequencyUnits.contains(filterValue) ||
          dataWindowUnits.contains(filterValue) ||
          enabled.contains(filterValue) ||
          (enabled.equals("true") && filterValue.equals("enabled")) ||
          (enabled.equals("false") && filterValue.equals("disabled"))) {
        newList.add(job);
      }
    }
    return newList;
  }

  private List<HttpResponseGetScheduledJob> sortJobList(List<HttpResponseGetScheduledJob> list, Sort sort) {
    switch(sort.toString().split(":")[0].trim()) {
      case "reportTitle": {
        list.sort((j1, j2) -> j1.getJobDefinition().getJobData().getReportTitle()
            .compareTo(j2.getJobDefinition().getJobData().getReportTitle()));
        break;
      }
      case "toEmail": {
        list.sort((j1, j2) -> j1.getEmailDefinition().getToEmail().get(0)
            .compareTo(j2.getEmailDefinition().getToEmail().get(0)));
        break;
      }
      case "frequency": {
        list.sort((j1, j2) -> {
          int unitComparison = ((ChronoUnit) j1.getJobScheduleDefinition()
              .get(FREQUENCY_UNITS)).compareTo(
              ((ChronoUnit) j2.getJobScheduleDefinition().get(FREQUENCY_UNITS)));
          if (unitComparison != 0) {
            return unitComparison;
          } else {
            return j1.getJobScheduleDefinition().getInt(FREQUENCY) - j2.getJobScheduleDefinition()
                .getInt(FREQUENCY);
          }
        });
        break;
      }
      case "dataWindow": {
        list.sort((j1, j2) -> {
          int unitComparison = j1.getJobDefinition().getJobData().getDataWindowUnits()
              .compareTo(j2.getJobDefinition().getJobData().getDataWindowUnits());
          if (unitComparison != 0) {
            return unitComparison;
          } else {
            return j1.getJobDefinition().getJobData().getDataWindow() - j2.getJobDefinition()
                .getJobData().getDataWindow();
          }
        });
        break;
      }
      case "lastRun": {
        list.sort((j1, j2) -> {
          String prevFireTime1 = j1.getJobRuntimes().get(0).getPreviousFireTime();
          String prevFireTime2 = j2.getJobRuntimes().get(0).getPreviousFireTime();

          if (prevFireTime1.equals("N/A") && prevFireTime2.equals("N/A"))
            return 0;
          if (prevFireTime1.equals("N/A"))
            return 1;
          if (prevFireTime2.equals("N/A"))
            return -1;

          ZonedDateTime j1LastRun = ZonedDateTime.parse(prevFireTime1);
          ZonedDateTime j2LastRun = ZonedDateTime.parse(prevFireTime2);

          return j1LastRun.compareTo(j2LastRun);
        });
        break;
      }
      case "nextRun": {
        list.sort((j1, j2) -> {
          String nextFireTime1 = j1.getJobRuntimes().get(0).getNextFireTime();
          String nextFireTime2 = j2.getJobRuntimes().get(0).getNextFireTime();

          if (j1.getJobRuntimes().get(0).getTriggerState().equals("PAUSED") && j2.getJobRuntimes().get(0).getTriggerState().equals("PAUSED")) return 0;
          if (j1.getJobRuntimes().get(0).getTriggerState().equals("PAUSED")) return 1;
          if (j2.getJobRuntimes().get(0).getTriggerState().equals("PAUSED")) return -1;

          ZonedDateTime j1NextRun = ZonedDateTime.parse(nextFireTime1);
          ZonedDateTime j2NextRun = ZonedDateTime.parse(nextFireTime2);
          return j1NextRun.compareTo(j2NextRun);
        });
        break;
      }
      case "enabled": {
        list.sort((j1, j2) -> Boolean.compare(
            !j1.getJobRuntimes().get(0).getTriggerState().equals("PAUSED"),
            !j2.getJobRuntimes().get(0).getTriggerState().equals("PAUSED")));
        break;
      }
      default: {
        break;
      }
    }

    if (sort.toString().split(":")[1].trim().equals("DESC")) {
      Collections.reverse(list);
    }
    return list;
  }
}