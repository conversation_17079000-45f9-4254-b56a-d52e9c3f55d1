package net.evolveip.ossmosis.api.scheduler.mapper;

import net.evolveip.ossmosis.api.scheduler.common.dto.JobDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateJobDefinition;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.factory.Mappers;

@Mapper(
    componentModel = ComponentModel.SPRING,
    injectionStrategy = InjectionStrategy.CONSTRUCTOR,
    uses = {MapperCallDetailReport.class}
)
public interface MapperJobDefinition {

  MapperJobDefinition INSTANCE = Mappers.getMapper(MapperJobDefinition.class);

  @Mapping(source = "jobType", target = "jobType")
  @Mapping(source = "enabled", target = "enabled")
  @Mapping(source = "createdBy", target = "createdBy")
  @Mapping(source = "createdTimestamp", target = "createdTimestamp")
  @Mapping(source = "updatedBy", target = "updatedBy")
  @Mapping(source = "updatedTimestamp", target = "updatedTimestamp")
  @Mapping(source = "jobData", target = "jobData")
  JobDefinitionDTO toDto(HttpRequestCreateJobDefinition entryHttpRequest);
}
