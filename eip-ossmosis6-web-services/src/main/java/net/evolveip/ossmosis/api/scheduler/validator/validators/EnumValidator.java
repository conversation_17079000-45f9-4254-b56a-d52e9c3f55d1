package net.evolveip.ossmosis.api.scheduler.validator.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Arrays;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidEnum;

public class EnumValidator implements ConstraintValidator<ValidEnum, String> {

  private Class<? extends Enum<?>> enumClass;

  @Override
  public void initialize(ValidEnum constraintAnnotation) {
    this.enumClass = constraintAnnotation.enumClass();
  }

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {
    if (value == null) {
      return true; // Use @NotNull or @NotEmpty for null/empty validation
    }
    return Arrays.stream(enumClass.getEnumConstants())
        .anyMatch(e -> e.name().equalsIgnoreCase(value));
  }
}