package net.evolveip.ossmosis.api.features.login.mapper;

import java.util.List;
import net.evolveip.ossmosis.api.features.login.dto.LoginResponseDTO;
import net.evolveip.ossmosis.api.features.login.entity.view.LoginView;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.factory.Mappers;


@Mapper(componentModel = ComponentModel.SPRING, uses = {
    LoginResourcePermissionLoginPermissionViewMapper.class,
    LoginRoleViewMapper.class}, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface LoginViewMapper {

  LoginViewMapper INSTANCE = Mappers.getMapper(LoginViewMapper.class);

  @Mapping(source = "loginId", target = "loginId")
  @Mapping(source = "loginEmail", target = "loginEmail")
  @Mapping(source = "loginNameFirst", target = "loginNameFirst")
  @Mapping(source = "loginNameLast", target = "loginNameLast")
  @Mapping(source = "active", target = "active")
  @Mapping(source = "locked", target = "locked")
  @Mapping(source = "loginGroup", target = "loginGroup")
  @Mapping(source = "loginPhoneNumber", target = "loginPhoneNumber")
  @Mapping(source = "dateCreated", target = "dateCreated")
  @Mapping(source = "dateUpdated", target = "dateUpdated")
  @Mapping(source = "loginPrimaryEnterpriseId", target = "loginPrimaryEnterpriseId")
  @Mapping(source = "roles", target = "roles")
  @Mapping(source = "userPermissionList", target = "userPermissionList")
  LoginResponseDTO toLoginResponseDTO(LoginView loginView);


  List<LoginResponseDTO> entitiesToDTOs(List<LoginView> loginViews);
}
