package net.evolveip.ossmosis.api.scheduler.mapper;

import net.evolveip.ossmosis.api.scheduler.common.dto.CronScheduleJobDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.SimpleScheduleJobDTO;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateCronScheduleJob;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateSimpleScheduleJob;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = ComponentModel.SPRING, injectionStrategy = InjectionStrategy.CONSTRUCTOR, uses = {
    MapperEmailDefinition.class, MapperCronScheduleDefinition.class,
    MapperSimpleScheduleDefinition.class, MapperJobDefinition.class})
public interface MapperScheduledJob {

  MapperScheduledJob INSTANCE = Mappers.getMapper(MapperScheduledJob.class);

  CronScheduleJobDTO toDto(HttpRequestCreateCronScheduleJob entryRequestCreate);

  SimpleScheduleJobDTO toDTO(HttpRequestCreateSimpleScheduleJob entryRequestCreate);
}