package net.evolveip.ossmosis.api.features.permission.controller;

import java.util.List;
import net.evolveip.ossmosis.api.features.permission.dto.PermissionResponseDTO;
import net.evolveip.ossmosis.api.features.permission.service.PermissionService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/permissions")
public class PermissionController {

  private final PermissionService permissionService;


  @Autowired
  public PermissionController(PermissionService permissionService) {
    this.permissionService = permissionService;
  }

  @GetMapping
  public ResponseEntity<ApiResponse<List<PermissionResponseDTO>>> getPermissions() {
    ApiResponse<List<PermissionResponseDTO>> apiResponse = this.permissionService.getPermissions();
    return new ResponseEntity<>(apiResponse,
        HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

}
