package net.evolveip.ossmosis.api.features.enterprise.mapper;

import java.util.List;
import javax.xml.bind.JAXBElement;
import javax.xml.namespace.QName;
import net.evolveip.broadsoft.oci.pojo.as.ServiceProviderConsolidatedAddRequest;
import net.evolveip.broadsoft.oci.pojo.as.ServiceProviderDeleteRequest;
import net.evolveip.broadsoft.oci.pojo.as.ServiceProviderModifyRequest;
import net.evolveip.broadsoft.oci.pojo.as.ServiceProviderThirdPartyEmergencyCallingModifyRequest;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseEmptyViewResponseDTO;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseRequestDTO;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseResponseDTO;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.platform.mapper.PlatformMapper;
import net.evolveip.redsky.client.administration.model.CompanyAddTO;
import net.evolveip.redsky.client.administration.model.CompanyEditTO;
import net.evolveip.redsky.client.administration.model.CompanySecretsTO;
import org.mapstruct.BeanMapping;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "spring", uses = PlatformMapper.class, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface EnterpriseMapper {

  @Mapping(source = "enterpriseId", target = "enterpriseId")
  @Mapping(source = "billingId", target = "billingId")
  @Mapping(source = "enterpriseName", target = "enterpriseName")
  @Mapping(source = "accountCountry", target = "accountCountry")
  @Mapping(source = "platformId", target = "platform.platformId")
  @Mapping(source = "parentEnterpriseId", target = "parentEnterprise.enterpriseId")
  Enterprise toEntity(EnterpriseRequestDTO enterpriseDTO);

  @Mapping(source = "enterpriseId", target = "enterpriseId")
  @Mapping(source = "billingId", target = "billingId")
  @Mapping(source = "enterpriseName", target = "enterpriseName")
  @Mapping(source = "accountCountry", target = "accountCountry")
  @Mapping(source = "platform", target = "platform")
  @Mapping(source = "parentEnterprise.enterpriseId", target = "parentEnterpriseId")
  EnterpriseResponseDTO toDTO(Enterprise enterprise);

  List<EnterpriseResponseDTO> map(List<Enterprise> enterprise);


  @Mapping(source = "enterpriseId", target = "enterpriseId")
  @Mapping(source = "billingId", target = "billingId")
  @Mapping(source = "enterpriseName", target = "enterpriseName")
  @Mapping(source = "accountCountry", target = "accountCountry")
  @Mapping(source = "platform.platformId", target = "platformId")
  @Mapping(source = "parentEnterprise.enterpriseId", target = "parentEnterpriseId")
  EnterpriseEmptyViewResponseDTO mapToEmpty(Enterprise enterprise);

  List<EnterpriseEmptyViewResponseDTO> mapToEmpty(List<Enterprise> enterprise);

  @BeanMapping(ignoreByDefault = true)
  @Mapping(source = "enterpriseId", target = "serviceProviderId")
  @Mapping(source = "enterpriseName", target = "serviceProviderName")
  @Mapping(target = "defaultDomain", constant = "voip.evolveip.net")
  @Mapping(target = "isEnterprise", constant = "true")
  ServiceProviderConsolidatedAddRequest toBroadsoftServiceProviderAddRequest(Enterprise enterprise);

  @BeanMapping(ignoreByDefault = true)
  @Mapping(source = "enterpriseId", target = "serviceProviderId")
  @Mapping(target = "serviceProviderName", expression = "java(mapJAXBString(enterprise.getEnterpriseName(), \"serviceProviderName\"))")
  @Mapping(target = "defaultDomain", constant = "voip.evolveip.net")
  ServiceProviderModifyRequest toBroadsoftServiceProviderModifyRequest(Enterprise enterprise);

  @BeanMapping(ignoreByDefault = true)
  @Mapping(source = "enterpriseId", target = "serviceProviderId")
  ServiceProviderDeleteRequest toBroadsoftServiceProviderDeleteRequest(String enterpriseId);

  @BeanMapping(ignoreByDefault = true)
  @Mapping(source = "enterpriseName", target = "name")
  @Mapping(source = "enterpriseId", target = "externalOrgId")
  @Mapping(target = "callRecordingEnabled", expression = "java(true)")
  @Mapping(target = "orgType", expression = "java(CompanyAddTO.OrgTypeEnum.fromValue(\"CUSTOMER\"))")
  @Mapping(target = "licenseModel", expression = "java(CompanyAddTO.LicenseModelEnum.fromValue(\"SUBSCRIPTION\"))")
  @Mapping(target = "locationTreeType", expression = "java(CompanyAddTO.LocationTreeTypeEnum.fromValue(\"BUILDING\"))")
  CompanyAddTO toRedskyCompanyAddTO(Enterprise enterprise);

  @BeanMapping(ignoreByDefault = true)
  @Mapping(source = "enterpriseName", target = "name")
  @Mapping(source = "enterpriseId", target = "externalOrgId")
  @Mapping(target = "callRecordingEnabled", expression = "java(true)")
  @Mapping(target = "orgType", expression = "java(CompanyEditTO.OrgTypeEnum.fromValue(\"CUSTOMER\"))")
  @Mapping(target = "locationTreeType", expression = "java(CompanyEditTO.LocationTreeTypeEnum.fromValue(\"BUILDING\"))")
  CompanyEditTO toRedskyCompanyEditTO(Enterprise enterprise);

  @BeanMapping(ignoreByDefault = true)
  @Mapping(source = "enterpriseId", target = "serviceProviderId")
  @Mapping(target = "customerId", expression = "java(mapJAXBString(companySecrets.getHeldToken().toString(), \"customerId\"))")
  @Mapping(target = "secretKey", expression = "java(mapJAXBString(companySecrets.getHeldPlusSecret(), \"secretKey\"))")
  @Mapping(target = "allowActivation", constant = "true")
  ServiceProviderThirdPartyEmergencyCallingModifyRequest toBroadsoftThirdPartyEmergencyCallingModifyRequest(
      CompanySecretsTO companySecrets, String enterpriseId);

  @Named("toJAXBString")
  default JAXBElement<String> mapJAXBString(String value, String qName) {
    JAXBElement<String> element = new JAXBElement<String>(new QName("", qName), String.class, value);
    return element;
  }
}