package net.evolveip.ossmosis.api.features.phonenumber.repository;

import java.util.Optional;
import net.evolveip.ossmosis.api.features.phonenumber.entity.Carrier;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CarrierRepository extends JpaRepository<Carrier, Integer> {

  Optional<Carrier> findCarrierByCarrierName(String carrierName);
}