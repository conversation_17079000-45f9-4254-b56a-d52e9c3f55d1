package net.evolveip.ossmosis.api.utils.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import net.evolveip.ossmosis.api.utils.validators.annotations.EnterpriseId;
import org.apache.commons.lang3.StringUtils;

public class EnterpriseIdValidator implements ConstraintValidator<EnterpriseId, String> {

  @Override
  public void initialize(EnterpriseId constraintAnnotation) {
  }


  @Override
  public boolean isValid(String enterpriseId, ConstraintValidatorContext context) {
      if (StringUtils.isEmpty(enterpriseId) || StringUtils.isAllBlank(enterpriseId)) {
          return false;
      }
      return enterpriseId.matches("^[a-zA-Z0-9!@$^&*()_,.<>?{}\\[\\]|`~\\-= ]{1,30}$");
  }
}