package net.evolveip.ossmosis.api.features.phonenumber.controller;

import jakarta.validation.Valid;
import java.io.File;
import java.util.List;
import net.evolveip.ossmosis.api.features.phonenumber.common.constants.PhoneNumberConstants;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.CarrierDTO;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.HttpPostRequestPhoneNumberDTO;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.HttpPutRequestPhoneNumberDTO;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.HttpResponsePhoneNumberDTO;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.PhoneNumberStatusDTO;
import net.evolveip.ossmosis.api.features.phonenumber.common.enums.PhoneNumberAssignmentType;
import net.evolveip.ossmosis.api.features.phonenumber.service.CarrierService;
import net.evolveip.ossmosis.api.features.phonenumber.service.PhoneNumberService;
import net.evolveip.ossmosis.api.features.phonenumber.service.PhoneNumberStatusService;
import net.evolveip.ossmosis.api.features.phonenumber.validator.annotations.ValidEnumPhoneNumberAssignmentType;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.validators.annotations.EnterpriseId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

@Validated
@RestController
@RequestMapping("/enterprise/{enterpriseId}/phonenumber")
public class PhoneNumbersController {

  private final PhoneNumberService phoneNumberService;
  private final CarrierService carrierService;
  private final PhoneNumberStatusService phoneNumberStatusService;

  @Autowired
  public PhoneNumbersController(PhoneNumberService phoneNumberService,
      CarrierService carrierService, PhoneNumberStatusService phoneNumberStatusService) {
    this.phoneNumberService = phoneNumberService;
    this.carrierService = carrierService;
    this.phoneNumberStatusService = phoneNumberStatusService;
  }

  @Secured(PhoneNumberConstants.PHONE_NUMBERS_CREATE)
  @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<ApiResponse<List<HttpResponsePhoneNumberDTO>>> doCreate(
      @PathVariable @EnterpriseId String enterpriseId,
      @Valid @RequestBody List<HttpPostRequestPhoneNumberDTO> request) {
    request.forEach(dto -> dto.setEnterpriseId(enterpriseId));
    ApiResponse<List<HttpResponsePhoneNumberDTO>> apiResponse = phoneNumberService.processCreate(
        enterpriseId, request);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(PhoneNumberConstants.PHONE_NUMBERS_READ)
  @GetMapping
  public ResponseEntity<ApiResponse<?>> doGet(
      @PathVariable @EnterpriseId String enterpriseId,
      @RequestParam(value = "filterValue", required = false, defaultValue = "") String filterValue,
      Pageable pageable) {
    ApiResponse<?> apiResponse = phoneNumberService.processGet(enterpriseId, filterValue, pageable);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(PhoneNumberConstants.PHONE_NUMBERS_READ)
  @GetMapping("/{phoneNumberId}")
  public ResponseEntity<ApiResponse<HttpResponsePhoneNumberDTO>> doGetById(
      @PathVariable @EnterpriseId String enterpriseId,
      @PathVariable Long phoneNumberId) {
    ApiResponse<HttpResponsePhoneNumberDTO> apiResponse =
        phoneNumberService.processGetById(enterpriseId, phoneNumberId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(PhoneNumberConstants.PHONE_NUMBERS_READ)
  @GetMapping("/{phoneNumberId}/validate")
  public ResponseEntity<ApiResponse<Boolean>> doValidateById(
      @PathVariable @EnterpriseId String enterpriseId,
      @PathVariable Long phoneNumberId) {
    ApiResponse<Boolean> apiResponse =
        phoneNumberService.processValidateById(enterpriseId, phoneNumberId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(PhoneNumberConstants.PHONE_NUMBERS_UPDATE)
  @PutMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<ApiResponse<List<HttpResponsePhoneNumberDTO>>> doUpdate(
      @PathVariable @EnterpriseId String enterpriseId,
      @Valid @RequestBody List<HttpPutRequestPhoneNumberDTO> request) {
    request.forEach(dto -> dto.setEnterpriseId(enterpriseId));
    ApiResponse<List<HttpResponsePhoneNumberDTO>> apiResponse = phoneNumberService.processUpdate(
        enterpriseId, request);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(PhoneNumberConstants.PHONE_NUMBERS_DELETE)
  @DeleteMapping
  public ResponseEntity<ApiResponse<Boolean>> doDelete(
      @PathVariable @EnterpriseId String enterpriseId,
      @Valid @RequestBody List<Long> request) {
    ApiResponse<Boolean> apiResponse = phoneNumberService.processDelete(enterpriseId, request);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(PhoneNumberConstants.PHONE_NUMBERS_READ)
  @GetMapping(value = "/download")
  public ResponseEntity<FileSystemResource> doDownload(
      @PathVariable @EnterpriseId String enterpriseId,
      @RequestParam(value = "filterValue", required = false, defaultValue = "") String filterValue,
      @RequestParam(value = "format", required = false, defaultValue = "csv") String format,
      @RequestParam(value = "fileName", required = false, defaultValue = "PhoneNumbers") String fileName,
      @RequestParam(value = "timezone", required = false, defaultValue = "") String timezone
  ) {
    try {
      File file = phoneNumberService.processDownload(enterpriseId, filterValue, format, fileName, timezone);

      String contentType = switch (format) {
        case "csv" -> "text/csv";
        case "pdf" -> "application/pdf";
        case "xlsx" -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        case "json" -> "application/json";
        default -> "text/csv";
      };

      return ResponseEntity.ok()
          .header("Content-Disposition", "attachment; filename=" + fileName)
          .contentLength(file.length())
          .contentType(MediaType.parseMediaType(contentType))
          .body(new FileSystemResource(file));
    } catch (Exception e) {
      throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Unable to download phone numbers", e);
    }
  }

  @Secured(PhoneNumberConstants.PHONE_NUMBERS_READ)
  @GetMapping("/carriers")
  public ResponseEntity<ApiResponse<List<CarrierDTO>>>  getCarriers(
      @PathVariable @EnterpriseId String enterpriseId) {
    ApiResponse<List<CarrierDTO>> apiResponse = carrierService.processGet();
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(PhoneNumberConstants.PHONE_NUMBERS_READ)
  @GetMapping("/statuses")
  public ResponseEntity<ApiResponse<List<PhoneNumberStatusDTO>>> getPhoneNumberStatuses(
      @PathVariable @EnterpriseId String enterpriseId){
    ApiResponse<List<PhoneNumberStatusDTO>> apiResponse = phoneNumberStatusService.processGet();
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(PhoneNumberConstants.PHONE_NUMBERS_READ)
  @GetMapping("/assignment")
  public ResponseEntity<ApiResponse<HttpResponsePhoneNumberDTO>> getPhoneNumberAssignment(
      @PathVariable @EnterpriseId String enterpriseId,
      @RequestParam(value = "assignmentType", required = true, defaultValue = "") @ValidEnumPhoneNumberAssignmentType(enumClass = PhoneNumberAssignmentType.class) String assignmentType,
      @RequestParam(value = "assignmentId", required = true, defaultValue = "") String assignmentId) {
    ApiResponse<HttpResponsePhoneNumberDTO> apiResponse = phoneNumberService.processGetPhoneNumberAssignment(enterpriseId, assignmentType, assignmentId);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }
}