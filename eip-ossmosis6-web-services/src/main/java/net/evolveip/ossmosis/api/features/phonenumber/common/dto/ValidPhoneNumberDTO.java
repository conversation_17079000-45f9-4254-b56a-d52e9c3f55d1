package net.evolveip.ossmosis.api.features.phonenumber.common.dto;

import com.google.i18n.phonenumbers.Phonenumber.PhoneNumber;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ValidPhoneNumberDTO {

  private PhoneNumber phoneNumber;
  private String originalCountryCode;
  private String originalPhoneNumber;
}