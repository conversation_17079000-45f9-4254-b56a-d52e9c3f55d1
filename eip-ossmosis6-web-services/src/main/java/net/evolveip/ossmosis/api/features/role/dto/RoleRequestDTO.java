package net.evolveip.ossmosis.api.features.role.dto;

import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_EMPTY_VALIDATION_MESSAGE;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.features.enterprise.validator.annotations.ValidEnterprise;
import net.evolveip.ossmosis.api.features.role.validator.annotations.ValidPermissionList;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Builder
public class RoleRequestDTO {

  private Integer roleId;

  @NotEmpty(message = "roleName" + NOT_EMPTY_VALIDATION_MESSAGE)
  private String roleName;

  @ValidEnterprise
  private String enterpriseId;

  @ValidPermissionList
  private List<RoleResourcePermissionRequestDTO> permissionList;
}