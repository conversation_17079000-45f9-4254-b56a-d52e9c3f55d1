package net.evolveip.ossmosis.api.features.phonenumber.validator.annotations;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import net.evolveip.ossmosis.api.features.phonenumber.validator.validators.PhoneNumberAssignmentTypeValidator;

@Constraint(validatedBy = PhoneNumberAssignmentTypeValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidEnumPhoneNumberAssignmentType {

  String message() default "Invalid phone number assignment type: [${validatedValue}]";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};

  Class<? extends Enum<?>> enumClass();
}
