package net.evolveip.ossmosis.api.features.role.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import net.evolveip.ossmosis.api.features.common.BaseEntity;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import org.hibernate.annotations.ColumnDefault;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Table(name = "role")
public class Role extends BaseEntity {

  @Id
  @Column(name = "role_id")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @EqualsAndHashCode.Exclude
  private Integer roleId;

  @Column(name = "role_name", length = 100, nullable = false)
  private String roleName;

  @Column(name = "default_role", nullable = false)
  @ColumnDefault("false")
  private Boolean defaultRole;

  @JoinColumn(name = "enterprise_id", nullable = false)
  @ManyToOne(targetEntity = Enterprise.class, fetch = FetchType.LAZY)
  private Enterprise enterprise;

  @OneToMany(mappedBy = "role", fetch = FetchType.LAZY)
  private List<RoleResourcePermission> permissionList;
}