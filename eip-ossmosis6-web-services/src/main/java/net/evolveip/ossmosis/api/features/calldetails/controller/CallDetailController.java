package net.evolveip.ossmosis.api.features.calldetails.controller;

import java.io.File;
import java.time.ZonedDateTime;
import java.util.List;
import lombok.extern.java.Log;
import net.evolveip.ossmosis.api.features.calldetails.constant.CallDetailConstants;
import net.evolveip.ossmosis.api.features.calldetails.dto.CallDetailSummaryDTO;
import net.evolveip.ossmosis.api.features.calldetails.dto.CallDetailSummaryListWrapperDTO;
import net.evolveip.ossmosis.api.features.calldetails.service.CallDetailService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.snowflake.client.jdbc.SnowflakeSQLException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.FileSystemResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

@RestController
@RequestMapping(value = "/call-details")
@Log
@Validated
@Lazy
public class CallDetailController {

  final CallDetailService reportsService;

  @Autowired
  public CallDetailController(CallDetailService reportsService) { this.reportsService = reportsService; }


  @Secured(CallDetailConstants.CALL_DETAIL_REPORTS_READ)
  @GetMapping
  public ResponseEntity<ApiResponse<Page<CallDetailSummaryDTO>>> doGetPageable(
      @RequestParam(value = "answerStatus", required = false, defaultValue = "all") String answerStatus,
      @RequestParam(value = "externalOnly", required = false, defaultValue = "all") String externalOnly,
      @RequestParam(value = "direction", required = false, defaultValue = "all") String direction,
      @RequestParam(value = "callType", required = false, defaultValue = "all") String callType,
      @RequestParam(value = "userType", required = false, defaultValue = "all") String userType,
      @RequestParam(value = "startDate", required = false, defaultValue = "") ZonedDateTime startDate,
      @RequestParam(value = "endDate", required = false, defaultValue = "") ZonedDateTime endDate,
      @RequestParam(value = "userNumber", required = false, defaultValue = "") List<String> userNumber,
      @RequestParam(value = "groupNumber", required = false, defaultValue = "") List<String> groupNumber,
      @RequestParam(value = "groupBy", required = false, defaultValue = "user") String groupBy,
      @RequestParam(value = "enterpriseIds", required = false) String[]enterpriseIds,
      @SortDefault(sort = "userNumber", direction = Direction.ASC) @PageableDefault(size = 15) final Pageable pageable
  ) throws SnowflakeSQLException {
    ApiResponse<Page<CallDetailSummaryDTO>> apiResponse = reportsService.processGetPageable(answerStatus, externalOnly, direction, callType, userType, startDate, endDate, userNumber, groupNumber, groupBy, enterpriseIds, pageable);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(CallDetailConstants.CALL_DETAIL_REPORTS_READ)
  @GetMapping(value = "/download")
  public ResponseEntity<FileSystemResource> downloadReport(
      @RequestParam(value = "answerStatus", required = false, defaultValue = "all") String answerStatus,
      @RequestParam(value = "externalOnly", required = false, defaultValue = "all") String externalOnly,
      @RequestParam(value = "direction", required = false, defaultValue = "all") String direction,
      @RequestParam(value = "callType", required = false, defaultValue = "all") String callType,
      @RequestParam(value = "userType", required = false, defaultValue = "all") String userType,
      @RequestParam(value = "startDate", required = false, defaultValue = "") ZonedDateTime startDate,
      @RequestParam(value = "endDate", required = false, defaultValue = "") ZonedDateTime endDate,
      @RequestParam(value = "userNumber", required = false, defaultValue = "") List<String> userNumber,
      @RequestParam(value = "groupNumber", required = false, defaultValue = "") List<String> groupNumber,
      @RequestParam(value = "groupBy", required = false, defaultValue = "user") String groupBy,
      @RequestParam(value = "enterpriseIds", required = false) String[]enterpriseIds,
      @RequestParam(value = "listType", required = false, defaultValue = "details") String listType,
      @RequestParam(value = "format", required = false, defaultValue = "csv") String format,
      @RequestParam(value = "fileName", required = false, defaultValue = "CallDetails") String fileName,
      @RequestParam(value = "timeZone", required = false, defaultValue = "") String timeZone
  ) {
    try {
      File file = reportsService.processDownloadReport(answerStatus, externalOnly, direction,
          callType, userType, startDate, endDate, userNumber, groupNumber, groupBy, enterpriseIds, listType,
          format, fileName, timeZone);

      String contentType = switch (format) {
        case "csv" -> "text/csv";
        case "pdf" -> "application/pdf";
        case "xlsx" -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        case "json" -> "application/json";
        default -> "text/csv";
      };

      return ResponseEntity.ok()
          .header("Content-Disposition", "attachment; filename=" + fileName)
          .contentLength(file.length())
          .contentType(MediaType.parseMediaType(contentType))
          .body(new FileSystemResource(file));
    } catch (Exception e) {
      throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Unable to download report", e);
    }
  }

  @Secured(CallDetailConstants.CALL_DETAIL_REPORTS_READ)
  @GetMapping("/list")
  public ResponseEntity<ApiResponse<CallDetailSummaryListWrapperDTO>> doGet(
      @RequestParam(value = "answerStatus", required = false, defaultValue = "all") String answerStatus,
      @RequestParam(value = "externalOnly", required = false, defaultValue = "all") String externalOnly,
      @RequestParam(value = "direction", required = false, defaultValue = "all") String direction,
      @RequestParam(value = "callType", required = false, defaultValue = "all") String callType,
      @RequestParam(value = "userType", required = false, defaultValue = "all") String userType,
      @RequestParam(value = "startDate", required = false, defaultValue = "") ZonedDateTime startDate,
      @RequestParam(value = "endDate", required = false, defaultValue = "") ZonedDateTime endDate,
      @RequestParam(value = "userNumber", required = false, defaultValue = "") List<String> userNumber,
      @RequestParam(value = "groupNumber", required = false, defaultValue = "") List<String> groupNumber,
      @RequestParam(value = "groupBy", required = false, defaultValue = "user") String groupBy,
      @RequestParam(value = "enterpriseIds", required = false) String[]enterpriseIds
  ) {
    try {
      ApiResponse<CallDetailSummaryListWrapperDTO> apiResponse = reportsService.processGet(answerStatus, externalOnly, direction, callType, userType, startDate, endDate, userNumber, groupNumber, groupBy, enterpriseIds);
      return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
    } catch (Exception e) {
      logger.severe(e.getMessage());
      return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
