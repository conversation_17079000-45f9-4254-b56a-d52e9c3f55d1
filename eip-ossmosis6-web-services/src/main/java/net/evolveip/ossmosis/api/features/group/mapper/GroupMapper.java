package net.evolveip.ossmosis.api.features.group.mapper;

import javax.xml.bind.JAXBElement;
import javax.xml.namespace.QName;
import net.evolveip.broadsoft.oci.pojo.ObjectFactory;
import net.evolveip.broadsoft.oci.pojo.as.GroupAddRequest;
import net.evolveip.broadsoft.oci.pojo.as.GroupDeleteRequest;
import net.evolveip.broadsoft.oci.pojo.as.GroupModifyRequest;
import net.evolveip.broadsoft.oci.pojo.as.GroupThirdPartyEmergencyCallingModifyRequest;
import net.evolveip.broadsoft.oci.pojo.common.Contact;
import net.evolveip.broadsoft.oci.pojo.common.StreetAddress;
import net.evolveip.ossmosis.api.features.group.common.dto.GroupRequestDTO;
import net.evolveip.ossmosis.api.features.group.common.dto.GroupResponseDTO;
import net.evolveip.ossmosis.api.features.group.entity.Address;
import net.evolveip.ossmosis.api.features.group.entity.Group;
import net.evolveip.ossmosis.api.features.phonenumber.common.enums.CountryCode;
import net.evolveip.ossmosis.api.features.phonenumber.mapper.CarrierMapper;
import net.evolveip.redsky.client.geography.model.BuildingCompactInputTO;
import net.evolveip.redsky.client.geography.model.BuildingEditCompactInputTO;
import net.evolveip.redsky.client.geography.model.CompactAddressTO;
import org.mapstruct.AfterMapping;
import org.mapstruct.BeanMapping;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;

@Mapper(componentModel = "spring", uses = CarrierMapper.class, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface GroupMapper {

  @Mapping(source = "groupId", target = "groupId")
  @Mapping(source = "enterpriseId", target = "enterpriseId")
  @Mapping(source = "groupName", target = "groupName")
  @Mapping(source = "billingAddress", target = "billingAddress")
  @Mapping(source = "timezone", target = "timezone")
  @Mapping(source = "contactName", target = "contactName")
  @Mapping(source = "contactPhone", target = "contactPhone")
  @Mapping(source = "contactEmail", target = "contactEmail")
  @Mapping(source = "callingLineIdName", target = "callingLineIdName")
  @Mapping(source = "callingLineIdNumber", target = "callingLineIdNumber")
  @Mapping(source = "extensionLength", target = "extensionLength")
  @Mapping(source = "localCarrierId", target = "localCarrierId")
  @Mapping(source = "externalCallsPolicy", target = "externalCallsPolicy")
  @Mapping(source = "enterpriseCallsPolicy", target = "enterpriseCallsPolicy")
  @Mapping(source = "groupCallsPolicy", target = "groupCallsPolicy")
  Group toEntity(GroupRequestDTO groupDTO);

  @Mapping(target = "localCarrierId", source = "localCarrierId")
  @Mapping(source = "groupId", target = "groupId")
  @Mapping(source = "enterpriseId", target = "enterpriseId")
  @Mapping(source = "groupName", target = "groupName")
  @Mapping(source = "billingAddress", target = "billingAddress")
  @Mapping(source = "timezone", target = "timezone")
  GroupResponseDTO toDTO(Group group);

  @BeanMapping(ignoreByDefault = true)
  @Mapping(source = "groupId", target = "groupId")
  @Mapping(source = "enterpriseId", target = "serviceProviderId")
  @Mapping(source = "groupName", target = "groupName")
  @Mapping(target = "userLimit", constant = "50")
  @Mapping(source = "timezone", target = "timeZone")
  @Mapping(target = "defaultDomain", constant = "voip.evolveip.net")
  @Mapping(source = "billingAddress", target = "address", qualifiedByName = "toBroadsoftStreetAddress")
  @Mapping(source = "callingLineIdName", target = "callingLineIdName")
  @Mapping(source = "callingLineIdNumber", target = "defaultUserCallingLineIdPhoneNumber")
  GroupAddRequest toBroadsoftGroupAddRequest(Group group);

  @AfterMapping
  default void afterToBroadsoftGroupAddRequest(Group group,
      @MappingTarget GroupAddRequest groupAddRequest) {

    // add the contact
    ObjectFactory factory = new ObjectFactory();
    Contact contact = new Contact();
    contact.setContactName(factory.createContactContactName(group.getContactName()));
    contact.setContactNumber(factory.createContactContactNumber(group.getContactPhone()));
    contact.setContactEmail(factory.createContactContactEmail(group.getContactEmail()));
    groupAddRequest.setContact(contact);
  }

  @BeanMapping(ignoreByDefault = true)
  @Mapping(source = "groupId", target = "groupId")
  @Mapping(source = "enterpriseId", target = "serviceProviderId")
  @Mapping(target = "groupName", expression = "java(mapJAXBString(group.getGroupName(), \"groupName\"))")
  @Mapping(target = "userLimit", constant = "50")
  @Mapping(source = "timezone", target = "timeZone")
  @Mapping(target = "defaultDomain", constant = "voip.evolveip.net")
  @Mapping(source = "billingAddress", target = "address", qualifiedByName = "toBroadsoftStreetAddress")
  @Mapping(source = "callingLineIdName", target = "callingLineIdName")
  @Mapping(source = "callingLineIdNumber", target = "defaultUserCallingLineIdPhoneNumber")
  GroupModifyRequest toBroadsoftGroupModifyRequest(Group group);

  @AfterMapping
  default void afterToBroadsoftGroupModifyRequest(Group group,
      @MappingTarget GroupModifyRequest groupModifyRequest) {
    // add the contact
    ObjectFactory factory = new ObjectFactory();
    Contact contact = new Contact();
    contact.setContactName(factory.createContactContactName(group.getContactName()));
    contact.setContactNumber(factory.createContactContactNumber(group.getContactPhone()));
    contact.setContactEmail(factory.createContactContactEmail(group.getContactEmail()));
    groupModifyRequest.setContact(contact);
  }

  @BeanMapping(ignoreByDefault = true)
  @Mapping(source = "groupId", target = "groupId")
  @Mapping(source = "enterpriseId", target = "serviceProviderId")
  GroupDeleteRequest toBroadsoftGroupDeleteRequest(String enterpriseId, String groupId);

  @BeanMapping(ignoreByDefault = true)
  @Mapping(source = "groupId", target = "groupId")
  @Mapping(source = "enterpriseId", target = "serviceProviderId")
  @Mapping(target = "enableDeviceManagement", constant = "true")
  @Mapping(target = "enableRouting", constant = "true")
  GroupThirdPartyEmergencyCallingModifyRequest toBroadsoftThirdPartyEmergencyCallingModifyRequest(
      Group group);

  @BeanMapping(ignoreByDefault = true)
  @Mapping(target = "name", expression = "java(group.getGroupName() + \";\" + group.getGroupId())")
  @Mapping(source = "billingAddress.addressLine2", target = "supplementalData", qualifiedByName = "trimAddressLine2")
  @Mapping(source = "billingAddress", target = "compactAddressTO", qualifiedByName = "toRedskyCompactAddressTO")
  BuildingCompactInputTO toRedskyBuildingCompactInputTO(Group group);

  @BeanMapping(ignoreByDefault = true)
  @Mapping(target = "name", expression = "java(group.getGroupName() + \";\" + group.getGroupId())")
  @Mapping(source = "billingAddress.addressLine2", target = "supplementalData", qualifiedByName = "trimAddressLine2")
  @Mapping(source = "billingAddress", target = "compactAddressTO", qualifiedByName = "toRedskyCompactAddressTO")
  BuildingEditCompactInputTO toRedskyBuildingEditCompactInputTO(Group group);

  @Named("toBroadsoftStreetAddress")
  default StreetAddress mapToStreetAddress(Address billingAddress) {
    ObjectFactory factory = new ObjectFactory();
    StreetAddress address = new StreetAddress();
    address.setAddressLine1(
        factory.createStreetAddressAddressLine1(billingAddress.getAddressLine1()));
    if (billingAddress.getAddressLine2() != null && !billingAddress.getAddressLine2().isEmpty()) {
      address.setAddressLine2(
          factory.createStreetAddressAddressLine2(billingAddress.getAddressLine2()));
    }
    address.setCity(factory.createStreetAddressCity(billingAddress.getCity()));
    address.setStateOrProvince(
        factory.createStreetAddressStateOrProvince(billingAddress.getStateOrProvinceDisplayName()));

    String country = billingAddress.getCountry();
    if (country.equals(CountryCode.UnitedStates.getIsoAlpha2Code())) {
      country = CountryCode.UnitedStates.getIsoAlpha3Code();
    } else if (country.equals(CountryCode.Canada.getIsoAlpha2Code())) {
      country = CountryCode.Canada.getIsoAlpha3Code();
    }

    address.setCountry(factory.createStreetAddressCountry(country));
    address.setZipOrPostalCode(
        factory.createStreetAddressZipOrPostalCode(billingAddress.getZipOrPostalCode()));
    return address;
  }

  @Named("toJAXBString")
  default JAXBElement<String> mapJAXBString(String value, String qName) {
    JAXBElement<String> element = new JAXBElement<String>(new QName("", qName), String.class,
        value);
    return element;
  }

  @Named("toRedskyCompactAddressTO")
  default CompactAddressTO mapToCompactAddressTO(Address billingAddress) {
    CompactAddressTO compactAddressTO = new CompactAddressTO();
    compactAddressTO.setStreet(billingAddress.getAddressLine1());
    compactAddressTO.setCity(billingAddress.getCity());
    compactAddressTO.setState(billingAddress.getStateOrProvince());
    compactAddressTO.setZipCode(billingAddress.getZipOrPostalCode());
    compactAddressTO.setCountry(billingAddress.getCountry());
    return compactAddressTO;
  }

  @Named("trimAddressLine2")
  default String trimAddressLine2(String addressLine2) {
    if (addressLine2 != null) {
      if (addressLine2.length() > 20) {
        addressLine2 = addressLine2.substring(0, 20);
      }
      return addressLine2;
    }

    return null;
  }
}
