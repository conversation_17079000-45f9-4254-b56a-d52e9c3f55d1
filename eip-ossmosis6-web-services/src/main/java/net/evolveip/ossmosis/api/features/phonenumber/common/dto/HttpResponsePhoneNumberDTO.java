package net.evolveip.ossmosis.api.features.phonenumber.common.dto;

import java.time.ZonedDateTime;
import jdk.jfr.Label;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import net.evolveip.ossmosis.api.features.group.entity.Address;
import net.evolveip.ossmosis.api.utils.file.annotations.Exportable;

@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Exportable
public class HttpResponsePhoneNumberDTO {

  @Label("Phone Number Id")
  private Long phoneNumberId;
  @Label("Date Created")
  private ZonedDateTime dateCreated;
  @Label("Date Updated")
  private ZonedDateTime dateUpdated;
  @Label("Country Code")
  private String countryCode;
  @Label("Phone Number")
  private String phoneNumber;
  @Label("Enterprise Id")
  private String enterpriseId;
  @Label("Account Number")
  private String accountNumber;
  @Label("BTN Country Code")
  private String btnCountryCode;
  @Label("BTN Phone Number")
  private String btnPhoneNumber;
  @Label("Carrier Id")
  private Integer carrierId;
  @Label("Carrier Name")
  private String carrierName;
  @Label("Status Id")
  private Integer statusId;
  @Label("Status Name")
  private String statusName;
  @Label("PIN")
  private String pin;
  @Label("Service Address")
  private Address serviceAddress;
  @Label("Assignment Type")
  private String assignmentType;
  @Label("Assignment Id")
  private String assignmentId;
  @Label("Assignment Name")
  private String assignmentName;
  @Label("Calling Line ID Name")
  private String callingLineIdName;
  @Label("E911 Address")
  private Address e911Address;
}