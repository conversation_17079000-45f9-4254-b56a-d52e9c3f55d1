package net.evolveip.ossmosis.api.scheduler.validator.annotations;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import net.evolveip.ossmosis.api.scheduler.validator.validators.CronExpressionValidator;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CronExpressionValidator.class)
public @interface ValidCronExpression {

  String message() default "Invalid cron expression: [${validatedValue}]";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}