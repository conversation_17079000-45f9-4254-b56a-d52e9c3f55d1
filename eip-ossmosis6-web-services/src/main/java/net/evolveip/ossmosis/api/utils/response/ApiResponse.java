package net.evolveip.ossmosis.api.utils.response;


import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import net.evolveip.ossmosis.api.config.audit.AuditFilterConfig;
import org.postgresql.util.PSQLException;
import org.springframework.dao.QueryTimeoutException;
import org.springframework.http.HttpStatus;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class ApiResponse<T> extends ApiResponseBase {

  private static final String POSTGRES_QUERY_CANCELED_ERR_CODE = "57014";

  // unique response identifier:
  private UUID responseId;
  // timestamp of the response:
  private ZonedDateTime timestamp;
  // HTTP status code associated with the API response.
  private int httpStatusCode;
  // HTTP status reason of the API response, indicating success or failure.
  private List<ApiResponseError> errors;
  // boolean flag that determines if the request has been successful or not:
  private boolean isSuccessful;
  // The data payload included in the API response, holding the actual content.
  private T payload;

  /**
   * Creates an APIResponse for a successful operation.
   *
   * @param data The data to include in the response.
   * @param <T>  The type of data to be included in the response.
   * @return An APIResponse indicating a successful operation.
   */
  public static <T> ApiResponse<T> create(
      T data
  ) {
    return ApiResponse
        .<T>builder()
        .responseId(AuditFilterConfig.getCurrentRequestId())
        .timestamp(ZonedDateTime.now())
        .httpStatusCode(HttpStatus.OK.value())
        .errors(new ArrayList<>())
        .isSuccessful(true)
        .payload(data).build();
  }

  public static <T> ApiResponse<T> create(
      T data,
      boolean successStatus,
      List<ApiResponseError> errors,
      HttpStatus httpStatus
  ) {
    return ApiResponse
        .<T>builder()
        .responseId(AuditFilterConfig.getCurrentRequestId())
        .timestamp(ZonedDateTime.now())
        .httpStatusCode(httpStatus.value())
        .errors(errors)
        .isSuccessful(successStatus)
        .payload(data)
        .build();
  }

  public static <T> ApiResponse<T> create(
      T data,
      boolean successStatus,
      String errorMessage,
      HttpStatus httpStatus
  ) {
    return ApiResponse
        .<T>builder()
        .responseId(AuditFilterConfig.getCurrentRequestId())
        .timestamp(ZonedDateTime.now())
        .httpStatusCode(httpStatus.value())
        .errors(Collections.singletonList(
            ApiResponseError.builder().errorMessage(errorMessage).build()))
        .isSuccessful(successStatus)
        .payload(data)
        .build();
  }

  public static <T> ApiResponse<T> create(Exception e) {
    List<ApiResponseError> errors = new ArrayList<>();

    PSQLException psqlException = extractPLSQException(e);
    if (psqlException != null && psqlException.getServerErrorMessage() != null) {
      errors.add(ApiResponseError.builder()
          .errorMessage(
              psqlException.getServerErrorMessage()
                  .getMessage())
          .build());
    } else {
      errors.add(ApiResponseError.builder().errorMessage(e.getMessage()).build());
    }
    return ApiResponse
        .<T>builder()
        .responseId(AuditFilterConfig.getCurrentRequestId())
        .timestamp(ZonedDateTime.now())
        .httpStatusCode(HttpStatus.INTERNAL_SERVER_ERROR.value())
        .errors(errors)
        .isSuccessful(false)
        .payload(null)
        .build();
  }

  private static PSQLException extractPLSQException(Exception e) {
    if (e instanceof QueryTimeoutException
        && ((QueryTimeoutException) e).getRootCause() instanceof PSQLException
        && Objects.equals(Objects.requireNonNull(
            ((PSQLException) ((QueryTimeoutException) e).getRootCause()).getServerErrorMessage())
        .getSQLState(), POSTGRES_QUERY_CANCELED_ERR_CODE)) {
      return ((PSQLException) ((QueryTimeoutException) e).getRootCause());
    }
    return null;
  }

  public static <T> ApiResponse<T> merge(
      ApiResponse<T> mainResponse,
      List<ApiResponseBase> responses
  ) {
    for (ApiResponseBase apiResponse : responses) {
      if (apiResponse != null && apiResponse.getErrors() != null && !apiResponse.getErrors()
          .isEmpty()) {
        if (mainResponse.getErrors() == null) {
          mainResponse.setErrors(new ArrayList<>());
        }
        mainResponse.getErrors().addAll(apiResponse.getErrors());
      }
    }
    return mainResponse;
  }
}