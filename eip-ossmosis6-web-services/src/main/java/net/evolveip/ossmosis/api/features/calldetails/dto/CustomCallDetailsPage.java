package net.evolveip.ossmosis.api.features.calldetails.dto;

import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

public class CustomCallDetailsPage<T> extends PageImpl<T> {

  private final long detailCount;

  public CustomCallDetailsPage(List<T> content, Pageable pageable, long total, long detailCount) {
    super(content, pageable, total);
    this.detailCount = detailCount;
  }

  public CustomCallDetailsPage(Page<T> page, long detailCount) {
    super(page.getContent(), page.getPageable(), page.getTotalElements());
    this.detailCount = detailCount;
  }

  public long getDetailCount() {
    return detailCount;
  }
}
