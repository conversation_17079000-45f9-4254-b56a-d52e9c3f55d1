package net.evolveip.ossmosis.api.features.group.entity;

import jakarta.persistence.Embeddable;
import java.io.Serial;
import java.io.Serializable;
import jdk.jfr.Label;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import net.evolveip.ossmosis.api.utils.file.annotations.Exportable;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Embeddable
@Exportable
public class Address implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  @Label("Address Line 1")
  private String addressLine1;

  @Label("Address Line 2")
  private String addressLine2;

  @Label("City")
  private String city;

  @Label("State or Province")
  private String stateOrProvince;

  @Label("State or Province Display Name")
  private String stateOrProvinceDisplayName;

  @Label("Zip or Postal Code")
  private String zipOrPostalCode;

  @Label("Country")
  private String country;

}
