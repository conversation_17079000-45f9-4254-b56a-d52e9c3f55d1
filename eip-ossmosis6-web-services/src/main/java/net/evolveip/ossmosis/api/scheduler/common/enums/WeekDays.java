package net.evolveip.ossmosis.api.scheduler.common.enums;

import java.util.Calendar;

public enum WeekDays {
  SUNDAY(Calendar.SUNDAY),
  MONDAY(Calendar.MONDAY),
  TUESDAY(Calendar.TUESDAY),
  WEDNESDAY(Calendar.WEDNESDAY),
  THURSDAY(Calendar.THURSDAY),
  FRIDAY(Calendar.FRIDAY),
  SATURDAY(Calendar.SATURDAY);

  private final int calendarDay;

  WeekDays(int calendarDay) {
    this.calendarDay = calendarDay;
  }

  public int getCalendarDay() {
    return calendarDay;
  }
}
