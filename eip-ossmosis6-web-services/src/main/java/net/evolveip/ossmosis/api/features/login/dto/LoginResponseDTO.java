package net.evolveip.ossmosis.api.features.login.dto;

import java.time.ZonedDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class LoginResponseDTO {

  private Long loginId;

  private String loginEmail;

  private String loginNameFirst;

  private String loginNameLast;

  private Boolean active = true;

  private Boolean locked = false;

  private String loginGroup;

  private String loginPhoneNumber;

  private String loginPrimaryEnterpriseId;

  private List<LoginRoleResponseDTO> roles;

  private ZonedDateTime dateCreated;

  private ZonedDateTime dateUpdated;

  private List<LoginResourcePermissionResponseDTO> userPermissionList;
}
