package net.evolveip.ossmosis.api.features.phonenumber.mapper;

import net.evolveip.ossmosis.api.features.enterprise.mapper.EnterpriseMapper;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.HttpResponsePhoneNumberDTO;
import net.evolveip.ossmosis.api.features.phonenumber.entity.PhoneNumber;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = {CarrierMapper.class,
    EnterpriseMapper.class,
    PhoneNumberStatusMapper.class}, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface PhoneNumberMapper {

  @Mapping(source = "enterprise.enterpriseId", target = "enterpriseId")
  @Mapping(source = "carrier.carrierId", target = "carrierId")
  @Mapping(source = "carrier.carrierName", target = "carrierName")
  @Mapping(source = "status.statusId", target = "statusId")
  @Mapping(source = "status.statusName", target = "statusName")
  @Mapping(source = "serviceAddress", target = "serviceAddress")
  @Mapping(source = "assignmentType", target = "assignmentType")
  @Mapping(source = "assignmentId", target = "assignmentId")
  @Mapping(source = "assignmentName", target = "assignmentName")
  @Mapping(source = "callingLineIdName", target = "callingLineIdName")
  @Mapping(source = " e911Address", target = "e911Address")
  HttpResponsePhoneNumberDTO toHttpResponsePhoneNumberDto(PhoneNumber phoneNumber);
}