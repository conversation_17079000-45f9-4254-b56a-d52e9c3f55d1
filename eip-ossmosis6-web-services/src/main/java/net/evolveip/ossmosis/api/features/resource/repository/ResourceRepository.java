package net.evolveip.ossmosis.api.features.resource.repository;

import java.util.Collection;
import java.util.List;
import net.evolveip.ossmosis.api.features.resource.entity.Resource;
import net.evolveip.ossmosis.api.features.resource.entity.view.ResourceView;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface ResourceRepository extends JpaRepository<Resource, Integer> {

  List<Resource> findByResourceNameIn(Collection<String> resourceName);

  @Query("select r from Resource r left join fetch r.assignablePermissions")
  List<ResourceView> findAllResourceViews();


}