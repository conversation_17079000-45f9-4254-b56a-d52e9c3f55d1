package net.evolveip.ossmosis.api.scheduler.service.email;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.List;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.MimeMessageHelper;

public interface BaseEmailService<M extends MimeMessageHelper> extends ListConverter<String, File> {

  default void addAttachment(M helper, File attachment) throws MessagingException {
    FileSystemResource resource = new FileSystemResource(attachment);
    helper.addAttachment(attachment.getName(), resource);
  }

  default void addAttachment(M helper, List<File> attachmentFiles) throws MessagingException {
    for (File file : attachmentFiles) {
      addAttachment(helper, file);
    }
  }

  default void addAttachment(M helper, File... attachmentFiles)
      throws MessagingException {
    addAttachment(helper, toList(attachmentFiles));
  }

  default MimeMessageHelper createMimeMessageHelper(MimeMessage message) throws MessagingException {
    return new MimeMessageHelper(message, true, StandardCharsets.UTF_8.toString());
  }

  default void createHeader(M helper, String fromEmail, String[] toEmail,
      String subject, String bodyText, boolean html) throws MessagingException {
    helper.setFrom(fromEmail);
    helper.setTo(toEmail);
    helper.setSubject(subject);
    helper.setText(bodyText, html);
  }
}