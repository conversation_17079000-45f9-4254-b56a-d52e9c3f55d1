package net.evolveip.ossmosis.api.scheduler.job;

import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import net.evolveip.ossmosis.api.scheduler.common.TimeUtils;
import net.evolveip.ossmosis.api.scheduler.common.dto.ZonedDateTimeRangeDTO;

public interface CalculateDataWindow extends TimeUtils {

  List<ChronoUnit> lessThanADay = new ArrayList<>(
      Arrays.asList(ChronoUnit.NANOS, ChronoUnit.MICROS, ChronoUnit.MILLIS, ChronoUnit.SECONDS,
          ChronoUnit.MINUTES, ChronoUnit.HOURS));
  List<ChronoUnit> aDayOrMoreThanADay = new ArrayList<>(
      Arrays.asList(ChronoUnit.DAYS, ChronoUnit.WEEKS, ChronoUnit.MONTHS, ChronoUnit.YEARS));

  default ZonedDateTimeRangeDTO getZonedDateTimeRange(
      Integer dataWindowOffset,
      ChronoUnit dataWindowOffsetUnits,
      LocalTime dataWindowEndTime,
      Integer dataWindow,
      ChronoUnit dataWindowUnits,
      ZonedDateTime currentDateTime) {

    // convert the currentDateTime to UTC, if necessary:
    ZonedDateTime currentDateTimeUTC = TimeUtils.toUTC(currentDateTime);

    // compute the endRangeDateTime, use the offset
    ZonedDateTime endRangeDateTime = null;
    if (lessThanADay.contains(dataWindowOffsetUnits)) {
      endRangeDateTime = currentDateTimeUTC.minus(dataWindowOffset,
          dataWindowOffsetUnits);
    } else if (aDayOrMoreThanADay.contains(dataWindowOffsetUnits)) {
      ZonedDateTime endRangeMinusOffset = currentDateTimeUTC.minus(dataWindowOffset,
          dataWindowOffsetUnits);
      endRangeDateTime = endRangeMinusOffset.with(dataWindowEndTime);
    }

    ZonedDateTime startRangeDateTime = endRangeDateTime
        .minus(dataWindow, dataWindowUnits);

    return ZonedDateTimeRangeDTO
        .builder()
        .startRangeDateTime(startRangeDateTime)
        .endRangeDateTime(endRangeDateTime)
        .build();
  }
}