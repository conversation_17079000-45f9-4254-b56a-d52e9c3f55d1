package net.evolveip.ossmosis.api.scheduler.validator.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.lang.reflect.Method;
import java.util.Arrays;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidEnumWithDefault;

public class EnumWithDefaultValidator implements ConstraintValidator<ValidEnumWithDefault, String> {

  private Class<? extends Enum<?>> enumClass;

  @Override
  public void initialize(ValidEnumWithDefault constraintAnnotation) {
    this.enumClass = constraintAnnotation.enumClass();
  }

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {
    if (value == null) {
      return true;
    }

    // First check if it matches any enum name
    boolean isValidName = Arrays.stream(enumClass.getEnumConstants())
        .anyMatch(e -> e.name().equalsIgnoreCase(value));

    if (isValidName) {
      return true;
    }

    // Then check if it matches any enumId (assuming enums have getEnumId method)
    try {
      Method getEnumIdMethod = enumClass.getMethod("getEnumId");
      boolean isValidId = Arrays.stream(enumClass.getEnumConstants())
          .anyMatch(e -> {
            try {
              String enumId = (String) getEnumIdMethod.invoke(e);
              return enumId.equalsIgnoreCase(value);
            } catch (Exception ex) {
              return false;
            }
          });

      if (isValidId) {
        return true;
      }
    } catch (NoSuchMethodException e) {
      return false;
    }
    return false;
  }
}