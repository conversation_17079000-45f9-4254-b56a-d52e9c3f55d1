package net.evolveip.ossmosis.api.features.enterprise.service;

import net.evolveip.ossmosis.api.features.common.ValidationContext;

public interface EnterpriseValidationMethods {

  Boolean isEnterpriseIdValid(String enterpriseId);
  void validateEnterpriseId(String enterpriseId);
  Boolean isEnterpriseNameValid(String enterpriseName);
  void validateEnterpriseName(String enterpriseName);
  void checkIfCurrentUserHasAccessToEnterpriseId(String enterpriseId);
  void checkIfBillingIdAlreadyInUse(String enterpriseId, String billingId, ValidationContext context);
  void checkIfEnterpriseIdOrBillingIdFormatIsValid(String id, String message);
  void checkIfEnterpriseIdExistsWithADifferentPlatformId(String enterpriseId, Integer platformId);
  void checkIfEnterpriseNameExistsWithADifferentEnterpriseId(String name, String enterpriseId);
  void userCanAssignParentIdForEnterprise(String enterpriseId, String parentEnterpriseId);
  void checkIfCanMoveEnterpriseToTopLevel(String enterpriseId, String parentEnterpriseId);
  void checkIfEnterpriseIdAlreadyAChildEnterpriseId(String enterpriseId, String parentEnterpriseId);




}