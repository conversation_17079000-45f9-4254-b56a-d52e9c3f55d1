package net.evolveip.ossmosis.api.features.enterprise.repository;

import jakarta.transaction.Transactional;
import java.util.List;
import java.util.Optional;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.platform.entity.Platform;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface EnterpriseRepository extends JpaRepository<Enterprise, String> {

  @Query(value = "from Enterprise e join fetch e.platform where e.enterpriseId in (:enterpriseAccessIds)")
  List<Enterprise> findAllEnterprisesForUser(List<String> enterpriseAccessIds);

  @Query(value = "select count(e) from Enterprise e where e.enterpriseId in (:enterpriseAccessIds) and (lower(e.enterpriseId) like %:filterValue% or lower(e.enterpriseName) like %:filterValue% or lower(e.parentEnterprise.enterpriseId) like %:filterValue% or lower(e.billingId) like %:filterValue% or lower(e.platform.platformName) like %:filterValue% or lower(e.accountCountry) like %:filterValue%)")
  long findFilteredCountAllEnterprisesForUser(List<String> enterpriseAccessIds, String filterValue);

  @Query(value = "from Enterprise e join fetch e.platform where e.enterpriseId in (:enterpriseAccessIds) and (lower(e.enterpriseId) like %:filterValue% or lower(e.enterpriseName) like %:filterValue% or lower(e.parentEnterprise.enterpriseId) like %:filterValue% or lower(e.billingId) like %:filterValue% or lower(e.platform.platformName) like %:filterValue% or lower(e.accountCountry) like %:filterValue%)")
  Page<Enterprise> findAllByFilteredPage(List<String> enterpriseAccessIds, String filterValue, Pageable pageable);

  @Query("select rr from Enterprise rr join fetch rr.platform where rr.enterpriseName = :enterpriseName")
  Optional<Enterprise> findEnterpriseByEnterpriseName(String enterpriseName);

  @Query("select rr from Enterprise rr join fetch rr.platform where rr.enterpriseId = :enterpriseId")
  Optional<Enterprise> findEnterpriseByEnterpriseId(String enterpriseId);

  List<Enterprise> findEnterpriseByBillingId(String billingId);

  @Query(nativeQuery = true, value = "select * from partnerprovider.fn_auth_get_enterprise_access_list_for_login(:loginId)")
  List<Enterprise> findEnterprisesByLoginIdForAccess(Long loginId);

  @Query(nativeQuery = true, value = "select * from partnerprovider.fn_enterprise_update_tree_closure(:childEnterpriseId, :parentEnterpriseId)")
  Boolean updateTreeClosure(String childEnterpriseId, String parentEnterpriseId);

  @Query(value = "select count(*) > 0 from EnterpriseTreeClosure e "
      + "where e.ancestorEnterprise.enterpriseId = :enterpriseId and e.descendantEnterprise.enterpriseId = :parentEnterpriseId")
  Boolean isParentEnterpriseAlreadyChild(String enterpriseId, String parentEnterpriseId);

  // query used to update an Enterprise entity where the incoming billingId is looked up in
  // the existing range of billingIds, excluding the billingId of the existing entity:
  @Query(nativeQuery = true, value = "select count(e.billing_id) from partnerprovider.enterprise e where e.billing_id != :existingBillingId and e.billing_id=:candidateBillingId")
  Integer countByBillingId(String candidateBillingId, String existingBillingId);

  @Query(value = "select count(*) > 0 from EnterpriseTreeClosure e where e.ancestorEnterprise.enterpriseId = :enterpriseId and e.descendantEnterprise.enterpriseId != :enterpriseId")
  Boolean doesEnterpriseHaveChildren(String enterpriseId);

  @Modifying
  @Transactional
  @Query(value = "delete from EnterpriseTreeClosure e where e.ancestorEnterprise.enterpriseId = :enterpriseId or e.descendantEnterprise.enterpriseId = :enterpriseId")
  void deleteNonParentEnterpriseTree(String enterpriseId);

  @Query(value = "select count(rr.billingId) from Enterprise rr where rr.billingId=:candidateBillingId")
  Integer countByBillingId(String candidateBillingId);

  List<Enterprise> findFirst10ByPlatform(Platform platform);
}