package net.evolveip.ossmosis.api.features.login.mapper;

import java.util.List;
import net.evolveip.ossmosis.api.features.login.dto.LoginResourcePermissionResponseDTO;
import net.evolveip.ossmosis.api.features.login.entity.LoginResourcePermission;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = ComponentModel.SPRING, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface LoginResourcePermissionMapper {

  LoginResourcePermissionMapper INSTANCE = Mappers.getMapper(
      LoginResourcePermissionMapper.class);


  @Mapping(source = "permission.permissionId", target = "permissionId")
  @Mapping(source = "resource.resourceId", target = "resourceId")
  LoginResourcePermissionResponseDTO toLoginResourcePermissionResponseDTO(
      LoginResourcePermission loginResourcePermission);


  List<LoginResourcePermissionResponseDTO> entitiesToDTOs(
      List<LoginResourcePermission> loginResourcePermissions);

}