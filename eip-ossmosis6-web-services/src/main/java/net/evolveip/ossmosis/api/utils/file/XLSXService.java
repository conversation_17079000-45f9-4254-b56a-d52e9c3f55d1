package net.evolveip.ossmosis.api.utils.file;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import jdk.jfr.Label;
import net.evolveip.ossmosis.api.config.csv.CSVTempLocationPropertiesConfig;
import net.evolveip.ossmosis.api.utils.file.annotations.Exportable;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;


@Service
public class XLSXService<T> {

  final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss z");

  CellStyle cellStyle;

  public File generateXlxs(List<T> data, CSVTempLocationPropertiesConfig tempDir, String fileName, boolean getSubLists, String timezone)
      throws IOException {

    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

    try (Workbook workbook = new XSSFWorkbook()) {
      Class<?> dataClass = data.get(0).getClass();

      // Keep track of columns that have no data in them to hide at the end
      List<Integer> emptyColumns = new ArrayList<>();

      Sheet sheet = workbook.createSheet();

      // Define the first row and generate the table headers
      Row mainRow = sheet.createRow(0);
      generateTableHeader(sheet, mainRow, dataClass, getSubLists, emptyColumns);

      // Get the total number of columns
      int totalColumns = mainRow.getLastCellNum();

      // Go to the next row and generate the table data
      mainRow = sheet.createRow(1);
      generateTableData(sheet, mainRow, data, getSubLists, timezone, emptyColumns);

      // Resize the table columns
      for (int i = 0; i < totalColumns; i++) {
        sheet.autoSizeColumn((short) i);
      }

      // Hide any columns that have no data in any entries to keep the table clean
      for (Integer col : emptyColumns) {
        sheet.setColumnHidden(col, true);
      }

      workbook.write(outputStream);
    }

    File file = new File(buildFilePath(tempDir.getPath(), fileName));

    try (FileOutputStream fos = new FileOutputStream(file)) {
      outputStream.writeTo(fos);
    }

    return file;
  }

  /***
   * Generate and add the header title cells
   * @param sheet The xlsx sheet
   * @param row The current row
   * @param dataClass The data class type
   * @param getSubLists If additional list objects should also be added
   * @param emptyColumns Add each column index to this list to keep track of columns without data
   */
  private void generateTableHeader(Sheet sheet, Row row, Class<?> dataClass, boolean getSubLists, List<Integer> emptyColumns) {
    Font boldFont = getBoldFont(sheet.getWorkbook());
    CellStyle headerStyle = getCellStyle(sheet.getWorkbook(), boldFont);
    Cell headerCell;
    List<String> headerStrings = new ArrayList<>();
    getDataClassHeaders(headerStrings, dataClass, getSubLists, null);

    for (int i = 0; i < headerStrings.size(); i++) {
      headerCell = row.createCell(i);
      headerCell.setCellStyle(headerStyle);
      headerCell.setCellValue(headerStrings.get(i));
      emptyColumns.add(i);
    }
  }

  /***
   * Recursive method to get all field labels for the dataClass, and any additional field headers for lists/object types in the original dataClass
   * @param headerStrings List of strings containing the field labels for all required fields
   * @param dataClass The current dataClass to retrieve the fields from
   * @param getSubLists If additional list objects should also be added
   */
  private void getDataClassHeaders(List<String> headerStrings, Class<?> dataClass, boolean getSubLists, Label currentLabel) {
    List<Field> headers = Arrays.stream(dataClass.getDeclaredFields()).toList();
    for (Field field : headers) {
      Label fieldLabel = field.getAnnotation(Label.class);

      // Check if the field type is a collection
      if (Collection.class.isAssignableFrom(field.getType())) {

        if (getSubLists) {
          // Get the parameter type of the collection
          ParameterizedType listType = (ParameterizedType) field.getGenericType();
          Class<?> classType = (Class<?>) listType.getActualTypeArguments()[0];

          // If the classType is exportable then retrieve field labels of that type
          if (classType.getAnnotation(Exportable.class) != null) {
            getDataClassHeaders(headerStrings, classType, getSubLists, fieldLabel);
            fieldLabel = null;
          }
        } else {

          // If not exporting additional list objects, skip the field
          fieldLabel = null;
        }
      } else if(field.getType().getAnnotation(Exportable.class) != null) {
        Class<?> fieldType = field.getType();
        getDataClassHeaders(headerStrings, fieldType, getSubLists, fieldLabel);
        fieldLabel = null;
      }

      if (fieldLabel != null) {
        String headerValue = "";
        if (currentLabel != null) {
          headerValue += currentLabel.value() + " - ";
        }
        headerValue +=fieldLabel.value();
        headerStrings.add(headerValue);
      }
    }
  }

  /***
   * Method to loop through data object and generate the table data
   * @param sheet The xlsx sheet
   * @param currentRow The current row
   * @param data The data being added to the sheet
   * @param getSubLists If additional list object should also be added
   * @param timezone The timezone to display any date/times in
   * @param emptyColumns Keeps track of any columns that are empty to be hidden at the end
   */
  private void generateTableData(Sheet sheet, Row currentRow, List<?> data, boolean getSubLists, String timezone, List<Integer> emptyColumns) {
    if (!data.isEmpty()) {
      for (Object item : data) {
        generateTableObject(sheet, currentRow, 0, item, getSubLists, timezone, emptyColumns);
        Row newRow = sheet.getRow(sheet.getLastRowNum());
        currentRow = newRow.getPhysicalNumberOfCells() > 0 ? sheet.createRow(sheet.getLastRowNum() + 1) : sheet.getRow(sheet.getLastRowNum());
      }
    }
  }

  /***
   * Recursive method to retrieve the field data for the Object and add it to the xlsx sheet
   * @param sheet The xlsx sheet
   * @param currentRow The current row
   * @param startIndex The starting colum index, used in recursive method to keep sub lists in the correct column placement
   * @param data The data being added to the sheet
   * @param getSubLists If additional list object should also be added
   * @param timezone The timezone to display any date/times in
   * @param emptyColumns Keeps track of any columns that are empty to be hidden at the end
   */
  private void generateTableObject(Sheet sheet, Row currentRow, int startIndex, Object data, boolean getSubLists, String timezone, List<Integer> emptyColumns) {
    List<Field> fieldList = Arrays.stream(data.getClass().getDeclaredFields()).toList();
    int currentColumnIndex = startIndex;
    for (Field field : fieldList) {
      field.setAccessible(true);
      try {
        Object value = field.get(data);
        if (value == null) value = "";

        // Check if the field type is a collection
        if (Collection.class.isAssignableFrom(field.getType())) {
          if (getSubLists) {

            // Get the type of the collection
            ParameterizedType listType = (ParameterizedType) field.getGenericType();
            Class<?> classType = (Class<?>) listType.getActualTypeArguments()[0];

            // See if the classType is exportable
            if (classType.getAnnotation(Exportable.class) != null) {
              List<?> listValue = (List<?>) value;

              // Store current row number
              int originalRow = currentRow.getRowNum();

              // Loop through the sub-list, recursively call this method with those objects and increment the row
              for (Object subItem : listValue) {
                generateTableObject(sheet, currentRow, currentColumnIndex, subItem, getSubLists, timezone, emptyColumns);
                Row newRow = sheet.getRow(currentRow.getRowNum() + 1);
                currentRow = newRow == null ? sheet.createRow(currentRow.getRowNum() + 1) : newRow;
              }
              // Reset the row to the original value
              currentRow = sheet.getRow(originalRow);

              // Increment the columnIndex by the number of fields in the list class type
              currentColumnIndex = currentColumnIndex + classType.getDeclaredFields().length;
              value = null;

              // Check if the List is a primitive wrapper type
            } else if (getPrimitiveWrapperTypes().contains(classType)) {
              List<?> listValue = (List<?>) value;

              // Store current row number
              int originalRow = currentRow.getRowNum();

              // Loop through the sub-list, adding each value to the sheet and incrementing the row
              for (Object subItem : listValue) {
                if (subItem == null) subItem = "";
                if (subItem != "") emptyColumns.remove((Integer) currentColumnIndex);
                generateTableCell(sheet, currentRow, currentColumnIndex, subItem);
                Row newRow = sheet.getRow(currentRow.getRowNum() + 1);
                currentRow = newRow == null ? sheet.createRow(currentRow.getRowNum() + 1) : newRow;
              }

              // Reset the row to the original value
              currentRow = sheet.getRow(originalRow);
              currentColumnIndex = currentColumnIndex + 1;
              value = null;
            }
          } else {
            // If list is not exportable or primitive then skip exporting it
            value = null;
          }

          // Check if the field is an exportable object type and recursively call this method to add all the objects field values
        } else if(field.getType().getAnnotation(Exportable.class) != null) {
          generateTableObject(sheet, currentRow, currentColumnIndex, value, getSubLists, timezone, emptyColumns);

          // Increment the columnIndex by the number of fields in the class type
          currentColumnIndex = currentColumnIndex + field.getType().getDeclaredFields().length;
          value = null;
        }

        // For any standard value
        if (value != null) {

          // If the value exists remove the column from the emptyColumns list
          if (value != "") emptyColumns.remove((Integer) currentColumnIndex);

          // Format date/time to chosen timezone
          if (value instanceof ZonedDateTime) {
            value = ((ZonedDateTime) value).withZoneSameInstant(ZoneId.of(timezone)).format(formatter);
          }
          generateTableCell(sheet, currentRow, currentColumnIndex, value);
          currentColumnIndex++;
        }
      } catch (IllegalAccessException e) {
        throw new RuntimeException(e);
      }
    }
  }

  /***
   * Writes the provided value to a cell given the current row and column
   * @param sheet The xlsx sheet
   * @param currentRow The current row
   * @param columnIndex The column index for the cell
   * @param value The value to be added to the cell
   */
  private void generateTableCell(Sheet sheet, Row currentRow, int columnIndex, Object value) {
    Font normalFont = getGenericFont(sheet.getWorkbook());
    CellStyle dataStyle = getCellStyle(sheet.getWorkbook(), normalFont);
    Cell dataCell = currentRow.createCell(columnIndex);
    dataCell.setCellStyle(dataStyle);
    dataCell.setCellValue(value.toString());
  }

  /***
   * Returns a set of primitive wrapper types
   * @return a set of wrapper class types
   */
  private Set<Class<?>> getPrimitiveWrapperTypes() {
    Set<Class<?>> wrapperSet = new HashSet<>();
    wrapperSet.add(Boolean.class);
    wrapperSet.add(Character.class);
    wrapperSet.add(Byte.class);
    wrapperSet.add(Short.class);
    wrapperSet.add(Integer.class);
    wrapperSet.add(Long.class);
    wrapperSet.add(Float.class);
    wrapperSet.add(Double.class);
    wrapperSet.add(String.class);
    return wrapperSet;
  }

  /***
   * Returns a bold font type
   * @param workbook The xlsx workbook
   * @return a bold font type
   */
  private Font getBoldFont(Workbook workbook) {
    Font font = workbook.createFont();
    font.setBold(true);
    font.setFontHeight((short) (10 * 20));
    font.setFontName("Calibri");
    font.setColor(IndexedColors.BLACK.getIndex());
    return font;
  }

  /***
   * Returns a generic font type
   * @param workbook The xlsx workbook
   * @return a generic font type
   */
  private Font getGenericFont(Workbook workbook) {
    Font font = workbook.createFont();
    font.setFontHeight((short) (10 * 20));
    font.setFontName("Calibri");
    font.setColor(IndexedColors.BLACK.getIndex());
    return font;
  }

  /***
   * Returns a cell style
   * @param workbook The xlsx workbook
   * @param font The font to be applied to the cell
   * @return the cellStyle object
   */
  private CellStyle getCellStyle(Workbook workbook, Font font) {

    if (this.cellStyle == null) {
      CellStyle cellStyle = workbook.createCellStyle();
      cellStyle.setFont(font);
      cellStyle.setAlignment(HorizontalAlignment.CENTER);
      cellStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
      cellStyle.setBorderTop(BorderStyle.NONE);
      cellStyle.setBorderBottom(BorderStyle.NONE);
      cellStyle.setBorderLeft(BorderStyle.NONE);
      cellStyle.setBorderRight(BorderStyle.NONE);
      this.cellStyle = cellStyle;
    }
    return this.cellStyle;
  }

  /***
   * Builds a full filepath string
   * @param tempDir The directory to store the file temporarily
   * @param fileName The filename of the file
   * @return The full filepath
   */
  private String buildFilePath(String tempDir, String fileName) {
    return tempDir + File.separator + fileName;
  }
}
