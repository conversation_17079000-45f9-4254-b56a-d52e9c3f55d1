package net.evolveip.ossmosis.api.features.group.common.dto;

import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_BLANK_VALIDATION_MESSAGE;
import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_EMPTY_VALIDATION_MESSAGE;
import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_NULL_VALIDATION_MESSAGE;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.features.group.common.enums.EnterpriseCalls;
import net.evolveip.ossmosis.api.features.group.common.enums.ExternalCalls;
import net.evolveip.ossmosis.api.features.group.common.enums.GroupCalls;
import net.evolveip.ossmosis.api.features.group.entity.Address;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidEnum;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidEnumWithDefault;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidInteger;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GroupRequestDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  @NotBlank(message = "groupId" + NOT_BLANK_VALIDATION_MESSAGE)
  @NotNull(message = "groupId" + NOT_NULL_VALIDATION_MESSAGE)
  @NotEmpty(message = "groupId" + NOT_EMPTY_VALIDATION_MESSAGE)
  private String groupId;

  @NotBlank(message = "enterpriseId" + NOT_BLANK_VALIDATION_MESSAGE)
  @NotNull(message = "enterpriseId" + NOT_NULL_VALIDATION_MESSAGE)
  @NotEmpty(message = "enterpriseId" + NOT_EMPTY_VALIDATION_MESSAGE)
  private String enterpriseId;

  @NotBlank(message = "groupName" + NOT_BLANK_VALIDATION_MESSAGE)
  @NotNull(message = "groupName" + NOT_NULL_VALIDATION_MESSAGE)
  @NotEmpty(message = "groupName" + NOT_EMPTY_VALIDATION_MESSAGE)
  private String groupName;

  @NotNull(message = "billingAddress" + NOT_NULL_VALIDATION_MESSAGE)
  private Address billingAddress;

  @NotBlank(message = "timezone" + NOT_BLANK_VALIDATION_MESSAGE)
  private String timezone;

  @NotBlank(message = "contactName" + NOT_BLANK_VALIDATION_MESSAGE)
  private String contactName;
  @NotBlank(message = "contactPhone" + NOT_BLANK_VALIDATION_MESSAGE)
  private String contactPhone;
  @NotBlank(message = "contactEmail" + NOT_BLANK_VALIDATION_MESSAGE)
  private String contactEmail;
  @NotBlank(message = "callingLineIdName" + NOT_BLANK_VALIDATION_MESSAGE)
  private String callingLineIdName;
  @NotBlank(message = "callingLineIdNumber" + NOT_BLANK_VALIDATION_MESSAGE)
  private String callingLineIdNumber;
  @ValidInteger(message = "extensionLength" + NOT_NULL_VALIDATION_MESSAGE)
  private String extensionLength;
  @ValidInteger(message = "localCarrierId" + NOT_NULL_VALIDATION_MESSAGE)
  private String localCarrierId;

//  @ValidEnumWithDefault(enumClass = ExternalCalls.class)
  @ValidEnum(enumClass = ExternalCalls.class)
  private String externalCallsPolicy;
  @ValidEnumWithDefault(enumClass = EnterpriseCalls.class)
  private String enterpriseCallsPolicy;
  @ValidEnumWithDefault(enumClass = GroupCalls.class)
  private String groupCallsPolicy;
}