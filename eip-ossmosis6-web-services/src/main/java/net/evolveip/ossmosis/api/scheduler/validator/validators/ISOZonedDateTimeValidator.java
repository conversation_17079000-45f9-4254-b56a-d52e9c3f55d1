package net.evolveip.ossmosis.api.scheduler.validator.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ISOZonedDateTime;

public class ISOZonedDateTimeValidator implements ConstraintValidator<ISOZonedDateTime, String> {

  private static final DateTimeFormatter ISO_ZONED_DATE_TIME_FORMATTER = DateTimeFormatter.ISO_ZONED_DATE_TIME;

  @Override
  public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
    if (s == null || s.isEmpty()) {
      return false;
    }
    try {
      ZonedDateTime.parse(s, ISO_ZONED_DATE_TIME_FORMATTER);
      return true;
    } catch (DateTimeParseException e) {
      return false;
    }

  }
}
