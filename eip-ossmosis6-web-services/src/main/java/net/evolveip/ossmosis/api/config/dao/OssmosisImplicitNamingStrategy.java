package net.evolveip.ossmosis.api.config.dao;

import org.hibernate.boot.model.naming.Identifier;
import org.hibernate.boot.model.naming.ImplicitForeignKeyNameSource;
import org.hibernate.boot.model.naming.ImplicitNamingStrategyJpaCompliantImpl;
import org.hibernate.boot.model.naming.ImplicitUniqueKeyNameSource;

public class OssmosisImplicitNamingStrategy extends
    ImplicitNamingStrategyJpaCompliantImpl {


  @Override
  public Identifier determineForeignKeyName(ImplicitForeignKeyNameSource source) {
    return Identifier.toIdentifier(source.getTableName().getCanonicalName().toLowerCase() + "_"
        + source.getReferencedTableName().getCanonicalName().toLowerCase() + "_fKey");
  }

  @Override
  public Identifier determineUniqueKeyName(ImplicitUniqueKeyNameSource source) {
    StringBuilder uniqueKeyName = new StringBuilder(
        source.getTableName().getCanonicalName().toLowerCase() + "_");
    for (Identifier identifier : source.getColumnNames()) {
      uniqueKeyName.append(identifier.getCanonicalName().toLowerCase().replace("_", ""))
          .append("_");
    }
    uniqueKeyName.append("uniKey");
    return Identifier.toIdentifier(uniqueKeyName.toString());
  }
}
