package net.evolveip.ossmosis.api.features.phonenumber.common.constants;

public class CarrierConstants {

  public static final String ATT = "att";
  public static final String AVOXI = "avoxi";
  public static final String BANDWIDTH = "bandwidth";
  public static final String BANDWIDTH_OFF = "bandwidthOff";
  public static final String COLT = "colt";
  public static final String LEVEL3 = "level3";
  public static final String PEERLESS = "peerless";
  public static final String TELNIX = "telnix";
  public static final String TMOBILE = "tmobile";
  public static final String USCELLULAR = "uscellular";
  public static final String VERIZON = "verizon";
  public static final String VOIP_INNOVATIONS = "voipInnovations";
  public static final String VOXBONE = "voxbone";

  public static final String LABEL_ATT = "ATT";
  public static final String LABEL_AVOXI = "Avoxi";
  public static final String LABEL_BANDWIDTH = "Bandwidth";
  public static final String LABEL_BANDWIDTH_OFF = "Bandwidth (Off-Net)";
  public static final String LABEL_COLT = "Colt";
  public static final String LABEL_LEVEL3 = "Level 3";
  public static final String LABEL_PEERLESS = "Peerless";
  public static final String LABEL_TELNIX = "Telnix";
  public static final String LABEL_TMOBILE = "T-Mobile";
  public static final String LABEL_USCELLULAR = "U.S. Cellular";
  public static final String LABEL_VERIZON = "Verizon";
  public static final String LABEL_VOIP_INNOVATIONS = "Voip Innovations";
  public static final String LABEL_VOXBONE = "Voxbone";
}