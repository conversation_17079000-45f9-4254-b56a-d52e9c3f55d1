package net.evolveip.ossmosis.api.scheduler.listener;

import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobKey;
import org.quartz.Trigger;
import org.quartz.TriggerListener;

@Slf4j
public class TriggersListener implements TriggerListener {

  @Override
  public String getName() {
    return "TriggersListener";
  }

  @Override
  public void triggerFired(Trigger trigger, JobExecutionContext context) {
    final JobKey jobKey = trigger.getJobKey();
    logAction("started", trigger, jobKey);
  }

  /**
   * @Content : if this return value is true, request to JobListener jobExecutionVetoed()
   */
  @Override
  public boolean vetoJobExecution(Trigger trigger, JobExecutionContext context) {
    logger.info("check Trigger health");
    return false;
  }

  @Override
  public void triggerMisfired(Trigger trigger) {
    final JobKey jobKey = trigger.getJobKey();
    logAction("misfired", trigger, jobKey);
  }

  @Override
  public void triggerComplete(Trigger trigger, JobExecutionContext context,
      Trigger.CompletedExecutionInstruction triggerInstructionCode) {
    final JobKey jobKey = trigger.getJobKey();
    logAction("completed", trigger, jobKey);
  }

  private void logAction(String action, Trigger trigger, JobKey jobKey) {
    logger.info("Trigger {} at [{}] :: jobKey: [{}]", action, trigger.getStartTime(), jobKey);
  }
}