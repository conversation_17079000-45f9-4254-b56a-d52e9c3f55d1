package net.evolveip.ossmosis.api.features.login.entity;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.login.constant.LoginConstants;
import org.hibernate.annotations.ColumnDefault;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Table(name = "login", uniqueConstraints = {@UniqueConstraint(columnNames = {"login_email"})})
public class Login {

  @Id
  @Column(name = "login_id", nullable = false)
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long loginId;

  @Column(name = "date_created", nullable = false, updatable = false)
  @EqualsAndHashCode.Exclude
  private ZonedDateTime dateCreated;

  @Column(name = "date_updated", nullable = false)
  @ColumnDefault("CURRENT_TIMESTAMP")
  @EqualsAndHashCode.Exclude
  private ZonedDateTime dateUpdated;

  @Column(name = "login_email", length = LoginConstants.LOGIN_EMAIL_MAX_LENGTH, nullable = false)
  private String loginEmail;

  @Column(name = "login_name_first", length = LoginConstants.LOGIN_NAME_MAX_LENGTH, nullable = false)
  private String loginNameFirst;

  @Column(name = "login_name_last", length = LoginConstants.LOGIN_NAME_MAX_LENGTH, nullable = false)
  private String loginNameLast;

  @Column(name = "active", nullable = false)
  @Builder.Default
  private Boolean active = true;

  @Column(name = "locked", nullable = false)
  @Builder.Default
  private Boolean locked = false;

  @Column(name = "login_group", length = LoginConstants.LOGIN_GROUP_MAX_LENGTH, nullable = false)
  private String loginGroup;

  @Column(name = "login_phone_number", length = LoginConstants.LOGIN_PHONE_NUMBER_MAX_LENGTH)
  private String loginPhoneNumber;

  @JoinColumn(name = "login_primary_enterprise_id", nullable = false)
  @ManyToOne(targetEntity = Enterprise.class, fetch = FetchType.LAZY)
  private Enterprise loginPrimaryEnterprise;

  @OneToMany(mappedBy = "login", fetch = FetchType.LAZY)
  private Set<LoginRole> roles;

  @OneToMany(mappedBy = "login", fetch = FetchType.LAZY)
  private List<LoginResourcePermission> userPermissionList;
}