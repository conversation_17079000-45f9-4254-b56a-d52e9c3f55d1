package net.evolveip.ossmosis.api.features.enterprise.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import net.evolveip.ossmosis.api.features.common.BaseEntity;
import net.evolveip.ossmosis.api.features.platform.entity.Platform;

@Entity
@Builder
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Table(name = "enterprise", uniqueConstraints = {@UniqueConstraint(columnNames = {"billing_id"}),
    @UniqueConstraint(columnNames = {"enterprise_name"}),})
public class Enterprise extends BaseEntity {

  @Id
  @Column(name = "enterprise_id", nullable = false)
  private String enterpriseId;

  @Column(name = "billing_id", nullable = false)
  private String billingId;

  @Column(name = "enterprise_name", length = 100, nullable = false)
  private String enterpriseName;

  @Column(name = "account_country", nullable = false)
  private String accountCountry;

  @JoinColumn(name = "platform_id", nullable = false)
  @ManyToOne(targetEntity = Platform.class, fetch = FetchType.EAGER)
  private Platform platform;

  @JoinColumn(name = "parent_enterprise_id")
  @ManyToOne(targetEntity = Enterprise.class, fetch = FetchType.EAGER)
  private Enterprise parentEnterprise;

}