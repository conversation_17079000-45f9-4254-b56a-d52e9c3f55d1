package net.evolveip.ossmosis.api.utils.file;

import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Font;
import com.itextpdf.text.Font.FontFamily;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.Phrase;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import jdk.jfr.Label;
import net.evolveip.ossmosis.api.config.csv.CSVTempLocationPropertiesConfig;
import net.evolveip.ossmosis.api.utils.file.annotations.Exportable;

public class PDFService<T> {

  final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss z");

  int fontSize;

  public File generatePdf(List<T> data, CSVTempLocationPropertiesConfig tempDir, String fileName, boolean getSubLists, String timezone)
      throws IOException, DocumentException {

    Document document = new Document(PageSize.A4.rotate(), 0, 0, 36, 36);
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    PdfWriter.getInstance(document, outputStream);
    document.open();

    Class<?> dataClass = data.get(0).getClass();
    PdfPTable table = generateTable(dataClass, getSubLists);

    // Scale the font size based on the number of columns the data type has
    fontSize = 100 / table.getNumberOfColumns();
    if (fontSize < 2) fontSize = 2;
    if (fontSize > 8) fontSize = 8;

    generateTableHeader(table, dataClass, getSubLists);
    generateTableData(table, data, getSubLists, timezone);

    document.add(table);
    document.close();

    File file = new File(buildFilePath(tempDir.getPath(), fileName));

    try (FileOutputStream fos = new FileOutputStream(file)) {
      outputStream.writeTo(fos);
    }

    return file;
  }

  /***
   * Method to create the PdfpTable object by calculating the total number of columns
   * @param dataClass The base dataClass type
   * @param getSubLists If additional list objects should be added
   * @return The newly created PdfpTable object
   */
  private PdfPTable generateTable(Class<?> dataClass, boolean getSubLists) {
    int lengthCounter = 0;
    lengthCounter = lengthCounter + getTableLength(dataClass, getSubLists, lengthCounter);
    return new PdfPTable(lengthCounter);
  }

  /***
   * Recursive method for getting the number of fields in the data class and any child data classes
   * @param dataClass The current dataClass
   * @param getSubLists If additional list objects should be added
   * @param lengthCounter The current length counter
   * @return The new length counter
   */
  private int getTableLength(Class<?> dataClass, boolean getSubLists, int lengthCounter) {
    List<Field> fieldList = Arrays.stream(dataClass.getDeclaredFields()).toList();
    lengthCounter = lengthCounter + fieldList.size();

    // Loop through all the fields to check for child data types
    for (Field field : fieldList) {

      // Check if the field is a collection
      if (Collection.class.isAssignableFrom(field.getType())) {
        if (getSubLists) {

          // Get the collection data type
          ParameterizedType listType = (ParameterizedType) field.getGenericType();
          Class<?> classType = (Class<?>) listType.getActualTypeArguments()[0];

          // If the collection data type is exportable then calculate additional table length
          if (classType.getAnnotation(Exportable.class) != null) {
            lengthCounter = getTableLength(classType, getSubLists, lengthCounter);
            lengthCounter = lengthCounter - 1;
          }
        } else {
          lengthCounter = lengthCounter - 1;
        }

        // Check if the field is exportable and calculate additional table length
      } else if (field.getType().getAnnotation(Exportable.class) != null) {
        lengthCounter = getTableLength(field.getType(), getSubLists, lengthCounter);
        lengthCounter = lengthCounter - 1;
      }
    }
    return lengthCounter;
  }

  /***
   * Method to retrieve and write the column header fields
   * @param table The PdfpTable object
   * @param dataClass The base dataClass type
   * @param getSubLists If additional list objects should be added
   */
  private void generateTableHeader(PdfPTable table, Class<?> dataClass, boolean getSubLists) {
    Font boldFont = new Font(FontFamily.HELVETICA, fontSize, Font.BOLD);
    List<String> headerStrings = new ArrayList<>();
    getDataClassHeaders(headerStrings, dataClass, getSubLists, null);

    for (String header : headerStrings) {
      PdfPCell headerCell = new PdfPCell();
      headerCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
      headerCell.setPhrase(new Phrase(header, boldFont));
      table.addCell(headerCell);
    }
  }

  /***
   * Recursive method to retrieve field labels from the data class and any child data classes
   * @param headerStrings Current list of headers
   * @param dataClass Current dataClass type
   * @param getSubLists If additional list object should be added
   */
  private void getDataClassHeaders(List<String> headerStrings, Class<?> dataClass, boolean getSubLists, Label currentLabel) {

    List<Field> fieldList = Arrays.stream(dataClass.getDeclaredFields()).toList();

    for(Field field : fieldList) {
      Label fieldLabel = field.getAnnotation(Label.class);

      // Check if field type is a collection
      if (Collection.class.isAssignableFrom(field.getType())) {

        if (getSubLists) {

          // Get the data type of the collection
          ParameterizedType listType = (ParameterizedType) field.getGenericType();
          Class<?> classType = (Class<?>) listType.getActualTypeArguments()[0];

          // Check if the data type is exportable and retrieve additional headers
          if (classType.getAnnotation(Exportable.class) != null) {
            getDataClassHeaders(headerStrings, classType, getSubLists, fieldLabel);
            fieldLabel = null;
          }
        } else {

          // If not retrieving sub-lists then skip field
          fieldLabel = null;
        }

        // Check if field type is exportable and retrieve additional headers
      } else if (field.getType().getAnnotation(Exportable.class) != null) {
        Class<?> fieldType = field.getType();
        getDataClassHeaders(headerStrings, fieldType, getSubLists, fieldLabel);
        fieldLabel = null;
      }

      // For any standard value add the header list
      if (fieldLabel != null) {
        String headerValue = "";
        if (currentLabel != null) {
          headerValue += currentLabel.value() + " - ";
        }
        headerValue +=fieldLabel.value();
        headerStrings.add(headerValue);
      }
    }
  }

  /***
   * Method to retrieve and write row data
   * @param table The PdfpTable object
   * @param data The data list
   * @param getSubLists If additional list objects should be added
   * @param timezone The timezone to display any date/times in
   */
  private void generateTableData(PdfPTable table, List<?> data, boolean getSubLists, String timezone) {

    if (!data.isEmpty()) {
      // Loop through each object in the data list
      for (Object item : data) {
        generateTableObject(table, item, 0, getSubLists, timezone);
      }
    }
  }

  /***
   * Recursive method to retrieve field values from the data class and any child data classes
   * @param table
   * @param data
   * @param startIndex
   * @param getSubLists
   * @param timezone
   */
  private void generateTableObject(PdfPTable table, Object data, int startIndex, boolean getSubLists, String timezone) {
    List<Field> fieldList = Arrays.stream(data.getClass().getDeclaredFields()).toList();
    int currentColumnIndex = startIndex;

    // Keep track of list information to be added at the end of each entry since the pdfwriter has to finish a row before going to the next
    int maxListSize = 0;
    List<List<?>> listOfLists = new ArrayList<>();
    List<Integer> listStartIndexes = new ArrayList<>();

    for (Field field : fieldList) {
      field.setAccessible(true);
      try {
        Object value = field.get(data);
        if (value == null) value = "";

        // Check if field type is a collection
        if (Collection.class.isAssignableFrom(field.getType())) {
          if (getSubLists) {

            // Get the data type of the collection
            ParameterizedType listType = (ParameterizedType) field.getGenericType();
            Class<?> classType = (Class<?>) listType.getActualTypeArguments()[0];

            // Check if classType is exportable
            if (classType.getAnnotation(Exportable.class) != null) {
              List<?> listValue = (List<?>) value;

              // If more than 1 object in the list then add to the list to add the rest of the entries at the end
              if (listValue.size() > 1) {
                // Update maxListSize value
                if (listValue.size() > maxListSize) {
                  maxListSize = listValue.size();
                }
                listOfLists.add(listValue);
                listStartIndexes.add(currentColumnIndex);
              }

              // Add the first entry to the current row and update the column index
              generateTableObject(table, listValue.get(0), currentColumnIndex, getSubLists, timezone);
              currentColumnIndex = currentColumnIndex + listValue.get(0).getClass().getDeclaredFields().length - 1;
              value = null;

              // Check if the List is a primitive wrapper type
            } else if (getPrimitiveWrapperTypes().contains(classType)) {
              List<?> listValue = (List<?>) value;

              // If more than 1 object in the list then add to the list to add the rest of the entries at the end
              if (listValue.size() > 1) {
                // Update maxListSize value
                if (listValue.size() > maxListSize) {
                  maxListSize = listValue.size();
                }
                listOfLists.add(listValue);
                listStartIndexes.add(currentColumnIndex);
              }

              // Add first value to the current row
              generateTableCell(table, listValue.get(0), timezone);
              value = null;
            }
          } else {

            // If not adding sub-list values then skip field
            value = null;
          }

          // Check if field type is exportable
        } else if (field.getType().getAnnotation(Exportable.class) != null) {

          // Add field object to current row and update column index
          generateTableObject(table, value, currentColumnIndex, getSubLists, timezone);
          currentColumnIndex = currentColumnIndex + value.getClass().getDeclaredFields().length - 1;
          value = null;
        }

        // For any standard value
        if (value != null) {
          generateTableCell(table, value, timezone);
        }
      } catch (IllegalAccessException e) {
        throw new RuntimeException(e);
      }
      currentColumnIndex = currentColumnIndex + 1;
    }

    // Before going to the next base data list entry, add any sub-lists
    if (!listOfLists.isEmpty()) {
      generateTableList(table, listOfLists, listStartIndexes, maxListSize, timezone);
    }
  }

  /***
   * Method to add additional rows containing list data
   * @param table The PdfpTable object
   * @param listOfLists The list of lists that need to be added to additional rows
   * @param listStartIndexes The starting column index for each list
   * @param maxListSize The largest list size
   * @param timezone The timezone to display date/time in
   */
  private void generateTableList(PdfPTable table, List<List<?>> listOfLists, List<Integer> listStartIndexes, int maxListSize, String timezone) {

    // Loop through the largest list size
    for (int i = 1; i < maxListSize; i++ ) {
      int currentIndex = 0;

      // Get each list object
      for (List<?> list : listOfLists) {

        // Get the starting column index of the current list
        int startIndex = listStartIndexes.get(listOfLists.indexOf(list));

        // Pad any cells before the starting index
        while (currentIndex < startIndex) {
          generateTableCell(table, "", timezone);
          currentIndex = currentIndex + 1;
        }

        // If the list is not empty add the current object to the current row and increment the currentIndex
        if (i < list.size()) {
          generateTableObject(table, list.get(i), startIndex, true, timezone);
          currentIndex = currentIndex + list.get(i).getClass().getDeclaredFields().length;

          // If the list is empty pad the cells where the next entry would be
        } else {
          Class<?> classType = list.get(0).getClass();
          while (currentIndex < startIndex + classType.getDeclaredFields().length) {
            generateTableCell(table, "", timezone);
            currentIndex = currentIndex + 1;
          }
        }
      }

      // Pad any cells after all the lists are done for the current row to get to the next one
      while (currentIndex < table.getNumberOfColumns()) {
        generateTableCell(table, "", timezone);
        currentIndex = currentIndex + 1;
      }
    }
  }

  /***
   * Method to create and add the next data cell with the given value
   * @param table The PdfpTable object
   * @param value The value of the cell
   * @param timezone The timezone to display the date/time in
   */
  private void generateTableCell(PdfPTable table, Object value, String timezone) {
    Font normalFont = new Font(FontFamily.HELVETICA, fontSize);
    PdfPCell dataCell = new PdfPCell();
    if (value instanceof ZonedDateTime) {
      value = ((ZonedDateTime) value).withZoneSameInstant(ZoneId.of(timezone)).format(formatter);
    }
    dataCell.setPhrase(new Phrase(value.toString(), normalFont));
    table.addCell(dataCell);
  }

  /***
   * Returns a set of primitive wrapper types
   * @return a set of wrapper class types
   */
  private Set<Class<?>> getPrimitiveWrapperTypes() {
    Set<Class<?>> wrapperSet = new HashSet<>();
    wrapperSet.add(Boolean.class);
    wrapperSet.add(Character.class);
    wrapperSet.add(Byte.class);
    wrapperSet.add(Short.class);
    wrapperSet.add(Integer.class);
    wrapperSet.add(Long.class);
    wrapperSet.add(Float.class);
    wrapperSet.add(Double.class);
    wrapperSet.add(String.class);
    return wrapperSet;
  }

  /***
   * Builds a full filepath string
   * @param tempDir The directory to store the file temporarily
   * @param fileName The filename of the file
   * @return The full filepath
   */
  private String buildFilePath(String tempDir, String fileName) {
    return tempDir + File.separator + fileName;
  }
}
