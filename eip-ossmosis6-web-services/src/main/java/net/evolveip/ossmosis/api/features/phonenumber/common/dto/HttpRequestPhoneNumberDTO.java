package net.evolveip.ossmosis.api.features.phonenumber.common.dto;

import net.evolveip.ossmosis.api.features.group.entity.Address;

public interface HttpRequestPhoneNumberDTO {

  String getPhoneNumberId();

  String getCountryCode();

  String getPhoneNumber();

  String getEnterpriseId();

  String getAccountNumber();

  String getBtnCountryCode();

  String getBtnPhoneNumber();

  String getCarrierId();

  String getStatusId();

  String getPin();

  Address getServiceAddress();

  String getPString();

  String getAssignmentType();

  String getAssignmentId();

  String getAssignmentName();

  String getCallingLineIdName();

  Address getE911Address();
}