package net.evolveip.ossmosis.api.features.phonenumber.validator.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Arrays;
import net.evolveip.ossmosis.api.features.phonenumber.common.enums.CountryCode;
import net.evolveip.ossmosis.api.features.phonenumber.validator.annotations.ValidEnumDialingCode;

public class DialingCodeValidator implements ConstraintValidator<ValidEnumDialingCode, String> {

  private Class<? extends Enum<?>> enumClass;

  @Override
  public void initialize(ValidEnumDialingCode constraintAnnotation) {
    enumClass = constraintAnnotation.enumClass();
  }

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {
    if (value == null || enumClass == null) {
      return false;
    }

    return Arrays.stream(enumClass.getEnumConstants())
        .anyMatch(e -> {
          if (e instanceof CountryCode) {
            return ((CountryCode) e).getDialingCode().equals(value);
          }
          return false;
        });
  }
}