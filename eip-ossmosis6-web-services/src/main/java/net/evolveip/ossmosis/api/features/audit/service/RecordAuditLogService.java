package net.evolveip.ossmosis.api.features.audit.service;

import java.time.ZonedDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.config.auth.OssmosisJwtAuthenticationConverter;
import net.evolveip.ossmosis.api.features.audit.entity.RecordAuditLog;
import net.evolveip.ossmosis.api.features.audit.entity.RequestAuditLog;
import net.evolveip.ossmosis.api.features.audit.repository.RecordAuditLogRepository;
import net.evolveip.ossmosis.api.features.login.dto.LoginInfoResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RecordAuditLogService {


  private final RecordAuditLogRepository recordAuditLogRepository;

  @Autowired
  public RecordAuditLogService(RecordAuditLogRepository recordAuditLogRepository) {
    this.recordAuditLogRepository = recordAuditLogRepository;
  }


  public void updateRecordAuditLogForCreateLoginOnFirstRequest(RequestAuditLog requestAuditLog,
      LoginInfoResponseDTO newLogin) {
    try {
      if (OssmosisJwtAuthenticationConverter.firstTimeInSystem() && newLogin != null) {
        List<RecordAuditLog> logs = this.recordAuditLogRepository.findByTimestampIsAfterAndTimestampBeforeAndTableNameAndRecordIdAndOp(
            ZonedDateTime.now().minusMinutes(3), ZonedDateTime.now(), "login",
            newLogin.getLoginResponseDTO().getLoginId().toString(), "INSERT");
        if (logs.size() == 1) {
          RecordAuditLog recordAuditLog = logs.get(0);
          recordAuditLog.setRequestAuditLog(requestAuditLog);
          recordAuditLogRepository.save(recordAuditLog);
        } else {
          logger.error("RecordAuditLog not update properly.");
        }
      } else {
        logger.error("RecordAuditLog not update properly.");
      }
    } catch (Exception e) {
      logger.error(e.getMessage());
    }
  }
}