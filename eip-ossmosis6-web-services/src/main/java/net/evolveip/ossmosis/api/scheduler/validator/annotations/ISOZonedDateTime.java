package net.evolveip.ossmosis.api.scheduler.validator.annotations;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import net.evolveip.ossmosis.api.scheduler.validator.validators.ISOZonedDateTimeValidator;

@Documented
@Constraint(validatedBy = ISOZonedDateTimeValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.PARAMETER, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ISOZonedDateTime {

  String message() default "Invalid ISO_ZONED_DATE_TIME format";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
