package net.evolveip.ossmosis.api.features.login.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.enterprise.service.EnterpriseValidationService;
import net.evolveip.ossmosis.api.features.login.dto.LoginRequestDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginResponseDTO;
import net.evolveip.ossmosis.api.features.login.entity.Login;
import net.evolveip.ossmosis.api.features.login.entity.view.LoginView;
import net.evolveip.ossmosis.api.features.login.mapper.LoginMapper;
import net.evolveip.ossmosis.api.features.login.mapper.LoginViewMapper;
import net.evolveip.ossmosis.api.features.login.repository.LoginRepository;
import net.evolveip.ossmosis.api.features.role.constant.RoleConstants;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.response.ApiResponseBase;
import net.evolveip.ossmosis.api.utils.service.AuthorizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class LoginService {

  private final LoginRepository loginRepository;
  private final LoginResourcePermissionService loginResourcePermissionService;
  private final LoginRoleService loginRoleService;
  private final EnterpriseValidationService validationService;
  private final AuthorizationService authorizationService;
  private final LoginMapper loginMapper;
  private final LoginViewMapper loginViewMapper;
  private final ObjectMapper jacksonObjectMapper;

  @Autowired
  public LoginService(
      LoginRepository loginRepository,
      LoginResourcePermissionService loginResourcePermissionService,
      LoginRoleService loginRoleService,
      AuthorizationService authorizationService,
      LoginMapper loginMapper,
      LoginViewMapper loginViewMapper,
      ObjectMapper jacksonObjectMapper,
      EnterpriseValidationService validationService) {
    this.loginResourcePermissionService = loginResourcePermissionService;
    this.loginRoleService = loginRoleService;
    this.authorizationService = authorizationService;
    this.loginMapper = loginMapper;
    this.loginRepository = loginRepository;
    this.loginViewMapper = loginViewMapper;
    this.jacksonObjectMapper = jacksonObjectMapper;
    this.validationService = validationService;
  }

  public ApiResponse<LoginResponseDTO> processCreateLogin(LoginRequestDTO loginRequestDTO,
      String enterpriseIdPathParam) {
    try {
      ApiResponse<LoginResponseDTO> enterpriseCheckResponse = checkEnterprisePathParamMatchesLoginEnterpriseId(
          enterpriseIdPathParam, loginRequestDTO);
      if (enterpriseCheckResponse != null) {
        return enterpriseCheckResponse;
      }
      String loginJson = this.jacksonObjectMapper.writeValueAsString(List.of(loginRequestDTO));
      Login login = loginRepository.createLogins(loginJson,
          authorizationService.getLoggedInUserInfo().getLoginResponseDTO()
              .getLoginId())
          .get(0);

      List<ApiResponseBase> responses = List.of();
      if (authorizationService.getLoggedInUserInfo().getAuthorities()
          .contains(RoleConstants.ROLES_CREATE)) {
        responses = mergeRolesAndPermissions(loginRequestDTO, login);
      }
      LoginResponseDTO responseDTO = this.loginMapper.toLoginResponseDTO(login);
      logger.info("Successfully created login " + loginRequestDTO.getLoginEmail());
      return ApiResponse.merge(ApiResponse.create(responseDTO), responses);
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }

  public ApiResponse<LoginResponseDTO> createLoginForFirstTimeOnApi(
      LoginRequestDTO loginRequestDTO) {
    try {
      Login login = loginMapper.toLogin(loginRequestDTO);
      login.setDateCreated(ZonedDateTime.now());
      login.setDateUpdated(ZonedDateTime.now());
      Login createdLogin = loginRepository.save(login);

      LoginResponseDTO responseDTO = this.loginMapper.toLoginResponseDTO(createdLogin);
      logger.info(
          "Successfully created login on first API call: " + loginRequestDTO.getLoginEmail());
      return ApiResponse.create(responseDTO);
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }

  public ApiResponse<List<LoginResponseDTO>> processCreateLogins(
      List<LoginRequestDTO> loginRequestDTOs, String enterpriseIdPathParam) {
    try {
      ApiResponse<List<LoginResponseDTO>> enterpriseCheckResponse;
      for (LoginRequestDTO loginRequestDTO : loginRequestDTOs) {
        enterpriseCheckResponse = checkEnterprisePathParamMatchesLoginEnterpriseId(
            enterpriseIdPathParam, loginRequestDTO);
        if (enterpriseCheckResponse != null) {
          return enterpriseCheckResponse;
        }
      }
      String loginJson = this.jacksonObjectMapper.writeValueAsString(loginRequestDTOs);
      List<Login> logins = loginRepository.createLogins(loginJson,
          authorizationService.getLoggedInUserInfo().getLoginResponseDTO()
              .getLoginId());

      List<ApiResponseBase> apiResponses = new ArrayList<>();
      StringBuilder createdEmails = new StringBuilder();
      for (Login login : logins) {
        createdEmails.append(login.getLoginEmail()).append(", ");
        List<LoginRequestDTO> loginRequestDTO = loginRequestDTOs.stream()
            .filter(loginRequestDTO1 -> loginRequestDTO1.getLoginEmail().equals(
                login.getLoginEmail()))
            .toList();
        if (!loginRequestDTO.isEmpty()) {
          List<ApiResponseBase> responses = List.of();
          if (authorizationService.getLoggedInUserInfo().getAuthorities()
              .contains(RoleConstants.ROLES_CREATE)) {
            responses = mergeRolesAndPermissions(loginRequestDTO.get(0), login);
          }
          apiResponses.addAll(responses);
        }
      }
      List<LoginResponseDTO> responseDTOs = this.loginMapper.entitiesToDTOs(logins);
      logger.info("Successfully created logins " + createdEmails);
      return ApiResponse.merge(ApiResponse.create(responseDTOs), apiResponses);
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }

  public ApiResponse<LoginResponseDTO> updateLogin(LoginRequestDTO loginRequestDTO,
      String enterpriseIdPathParam) {
    try {
      ApiResponse<LoginResponseDTO> enterpriseCheckResponse = checkEnterprisePathParamMatchesLoginEnterpriseId(
          enterpriseIdPathParam, loginRequestDTO);
      if (enterpriseCheckResponse != null) {
        return enterpriseCheckResponse;
      }
      String loginJson = this.jacksonObjectMapper.writeValueAsString(List.of(loginRequestDTO));
      Login login = loginRepository.updateLogins(loginJson,
          authorizationService.getLoggedInUserInfo().getLoginResponseDTO()
              .getLoginId())
          .get(0);
      List<ApiResponseBase> responses = List.of();
      if (authorizationService.getLoggedInUserInfo().getAuthorities()
          .contains(RoleConstants.ROLES_UPDATE)) {
        responses = mergeRolesAndPermissions(loginRequestDTO, login);
      }
      LoginResponseDTO responseDTO = this.loginMapper.toLoginResponseDTO(login);
      logger.info("Successfully updated login " + loginRequestDTO.getLoginEmail());
      return ApiResponse.merge(ApiResponse.create(responseDTO), responses);
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }

  public ApiResponse<LoginResponseDTO> updateLoginEnterprise(Long loginId,
      String currentEnterpriseId, String newEnterpriseId) {
    try {
      validationService.validateEnterpriseId(currentEnterpriseId);
      validationService.validateEnterpriseId(newEnterpriseId);

      Optional<Login> existingLogin = loginRepository.findLoginByLoginIdAndLoginPrimaryEnterpriseEnterpriseId(loginId,
          currentEnterpriseId);

      if (existingLogin.isEmpty()) {
        String message = String.format("loginId [%s] or enterpriseId [%s] does not exists", loginId,
            currentEnterpriseId);
        logger.info(message);
        return ApiResponse.create(null, false, message, HttpStatus.BAD_REQUEST);
      }

      int modifiedRows = loginRepository.updateLoginEnterprise(loginId, newEnterpriseId);

      if (modifiedRows == 0) {
        String message = String.format("loginId [%s] not updated", loginId);
        return ApiResponse.create(null, false, message, HttpStatus.NOT_MODIFIED);
      }

      Optional<Login> login = loginRepository.findById(loginId);

      if (login.isEmpty()) {
        String message = String.format("loginId [%s] not found", loginId);
        return ApiResponse.create(null, false, message, HttpStatus.BAD_REQUEST);
      }

      LoginResponseDTO loginResponseDTO = loginMapper.toLoginResponseDTO(login.get());
      logger.info("Successfully updated login to [%s]", newEnterpriseId);
      return ApiResponse.create(loginResponseDTO);
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }

  public ApiResponse<List<LoginResponseDTO>> updateLogins(List<LoginRequestDTO> loginRequestDTOs,
      String enterpriseIdPathParam) {
    try {
      validationService.validateEnterpriseId(enterpriseIdPathParam);
      ApiResponse<List<LoginResponseDTO>> enterpriseCheckResponse;
      for (LoginRequestDTO loginRequestDTO : loginRequestDTOs) {
        enterpriseCheckResponse = checkEnterprisePathParamMatchesLoginEnterpriseId(
            enterpriseIdPathParam, loginRequestDTO);
        if (enterpriseCheckResponse != null) {
          return enterpriseCheckResponse;
        }
      }
      String loginJson = this.jacksonObjectMapper.writeValueAsString(loginRequestDTOs);
      List<Login> logins = loginRepository.updateLogins(loginJson,
          authorizationService.getLoggedInUserInfo().getLoginResponseDTO()
              .getLoginId());

      List<ApiResponseBase> apiResponses = new ArrayList<>();
      StringBuilder updatedEmails = new StringBuilder();
      for (Login login : logins) {
        updatedEmails.append(login.getLoginEmail()).append(", ");
        List<LoginRequestDTO> loginRequestDTO = loginRequestDTOs.stream()
            .filter(loginRequestDTO1 -> loginRequestDTO1.getLoginEmail().equals(
                login.getLoginEmail()))
            .toList();
        if (!loginRequestDTO.isEmpty()) {
          List<ApiResponseBase> responses = List.of();
          if (authorizationService.getLoggedInUserInfo().getAuthorities()
              .contains(RoleConstants.ROLES_UPDATE)) {
            responses = mergeRolesAndPermissions(loginRequestDTO.get(0), login);
          }
          apiResponses.addAll(responses);
        }
      }
      List<LoginResponseDTO> responseDTOs = this.loginMapper.entitiesToDTOs(logins);

      logger.info("Successfully updated logins " + updatedEmails);
      return ApiResponse.merge(ApiResponse.create(responseDTOs), apiResponses);
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }

  public ApiResponse<LoginResponseDTO> processGetByEmail(String enterpriseId, String email) {
    try {
      validationService.validateEnterpriseId(enterpriseId);
      Optional<LoginView> existingLogin = loginRepository.findLoginByLoginEmail(email,
          enterpriseId);
      if (existingLogin.isEmpty()) {
        String message = String.format("loginEmail [%s] does not exists", email);
        logger.info(message);
        return ApiResponse.create(null, false, message, HttpStatus.NO_CONTENT);
      }
      return ApiResponse.create(loginViewMapper.toLoginResponseDTO(existingLogin.get()));
    } catch (Exception e) {
      String message = String.format(e.getMessage());
      logger.error(message);
      return ApiResponse.create(e);
    }
  }

  public ApiResponse<LoginResponseDTO> processGetById(String enterpriseId, Long loginId) {
    try {
      validationService.validateEnterpriseId(enterpriseId);
      Optional<Login> existingLogin = loginRepository.findLoginByLoginIdAndLoginPrimaryEnterpriseEnterpriseId(
          loginId, enterpriseId);
      if (existingLogin.isEmpty()) {
        String message = String.format("loginId [%s] does not exists", loginId);
        logger.info(message);
        return ApiResponse.create(null, false, message, HttpStatus.NO_CONTENT);
      }
      return ApiResponse.create(loginMapper.toLoginResponseDTO(existingLogin.get()));
    } catch (Exception e) {
      String message = String.format(e.getMessage());
      logger.error(message);
      return ApiResponse.create(e);
    }
  }

  public Page<LoginResponseDTO> processGet(String enterpriseId, String filterValue, final Pageable pageable) {
    try {
      validationService.validateEnterpriseId(enterpriseId);
      String filterValueLowerCase = filterValue.toLowerCase();
      long totalElements = loginRepository.findFilteredCountForEnterpriseWithDataPopulated(enterpriseId, filterValueLowerCase);
      Page<LoginView> page = loginRepository.findAllByFilteredPage(enterpriseId, filterValueLowerCase, pageable);

      return new PageImpl<>(page.getContent().stream().map(loginViewMapper::toLoginResponseDTO).collect(
          Collectors.toList()), pageable, totalElements);
    } catch (Exception e) {
      String message = String.format(e.getMessage());
      logger.error(message);
      return new PageImpl<>(new ArrayList<>(), pageable, 0);
    }
  }

  public ApiResponse<List<LoginResponseDTO>> processGetListByEnterpriseId(String enterpriseId) {
    try {
      validationService.validateEnterpriseId(enterpriseId);
      List<LoginView> loginList = loginRepository.findAllForEnterpriseWithDataPopulated(
          enterpriseId);
      if (loginList.isEmpty()) {
        String message = "no logins found";
        logger.info(message);
        return ApiResponse.create(new ArrayList<>(), false, message, HttpStatus.NO_CONTENT);
      } else {
        return ApiResponse.create(
            loginList.stream().map(loginViewMapper::toLoginResponseDTO)
                .collect(Collectors.toList()));
      }
    } catch (Exception e) {
      String message = String.format(e.getMessage());
      logger.error(message);
      return ApiResponse.create(e);
    }
  }

  public ApiResponse<Boolean> processDeleteByLoginId(Long loginId, String enterpriseIdPathParam) {
    try {
      validationService.validateEnterpriseId(enterpriseIdPathParam);
      if (loginRepository.findLoginByLoginIdAndLoginPrimaryEnterpriseEnterpriseId(loginId,
          enterpriseIdPathParam).isPresent()) {
        loginRepository.deleteById(loginId);
        boolean check = loginRepository.existsById(loginId);
        if (check) {
          String message = String.format("loginId [%s] not removed", loginId);
          return ApiResponse.create(false, false, message, HttpStatus.NOT_MODIFIED);
        } else {
          return ApiResponse.create(true);
        }
      } else {
        String message = String.format("loginId [%s] not found", loginId);
        return ApiResponse.create(false, false, message, HttpStatus.NOT_FOUND);
      }
    } catch (Exception e) {
      String message = String.format(e.getMessage());
      logger.error(message);
      return ApiResponse.create(e);
    }
  }

  private List<ApiResponseBase> mergeRolesAndPermissions(LoginRequestDTO loginRequestDTO,
      Login login) {
    validationService.validateEnterpriseId(loginRequestDTO.getLoginPrimaryEnterpriseId());
    ApiResponse<Boolean> permissionResp = ApiResponse.create(true);
    if (loginRequestDTO.getUserPermissionList() != null) {
      permissionResp = loginResourcePermissionService.mergePermissionSet(
          loginRequestDTO, login);
    }
    ApiResponse<Boolean> roleResp = ApiResponse.create(true);
    if (loginRequestDTO.getRoles() != null) {
      roleResp = loginRoleService.mergeRoleSet(
          loginRequestDTO, login);
    }
    return List.of(permissionResp, roleResp);
  }

  public static <E> ApiResponse<E> checkEnterprisePathParamMatchesLoginEnterpriseId(
      String enterpriseId,
      LoginRequestDTO login) {
    if (login.getLoginPrimaryEnterpriseId() == null || !login.getLoginPrimaryEnterpriseId()
        .equals(enterpriseId)) {
      String message = "Error path enterpriseId does not match login enterpriseId: " + enterpriseId;
      logger.error(message);
      return ApiResponse.create(null, false, message, HttpStatus.FORBIDDEN);
    }
    return null;
  }
}
