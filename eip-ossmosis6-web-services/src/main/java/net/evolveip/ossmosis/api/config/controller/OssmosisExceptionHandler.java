package net.evolveip.ossmosis.api.config.controller;

import jakarta.validation.ConstraintViolationException;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.broadsoft.utils.exceptions.BroadsoftConnectionException;
import net.evolveip.ossmosis.api.broadsoft.utils.exceptions.BroadsoftNotFoundException;
import net.evolveip.ossmosis.api.broadsoft.utils.exceptions.BroadsoftUnknownException;
import net.evolveip.ossmosis.api.redsky.utils.exceptions.RedskyAddressValidationException;
import net.evolveip.ossmosis.api.redsky.utils.exceptions.RedskyConnectionException;
import net.evolveip.ossmosis.api.redsky.utils.exceptions.RedskyNotFoundException;
import net.evolveip.ossmosis.api.redsky.utils.exceptions.RedskyUnknownException;
import net.evolveip.ossmosis.api.utils.exceptions.DuplicateFoundException;
import net.evolveip.ossmosis.api.utils.exceptions.NotFoundException;
import net.evolveip.ossmosis.api.utils.exceptions.OssmosisDataIntegrityViolation;
import net.evolveip.ossmosis.api.utils.exceptions.ReferencedException;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.response.ApiResponseError;
import net.snowflake.client.jdbc.internal.org.bouncycastle.openssl.EncryptionException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@RestControllerAdvice
public class OssmosisExceptionHandler {

  @ExceptionHandler(Exception.class)
  public ResponseEntity<ApiResponse<Boolean>> handleGenericException(Exception ex) {
    HttpStatus statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    logger.error(ex.getMessage(), ex);
    return new ResponseEntity<>(
        ApiResponse.create(false, false, ex.getMessage(), statusCode),
        new HttpHeaders(),
        statusCode);
  }

  @ExceptionHandler(MethodArgumentNotValidException.class)
  public ResponseEntity<ApiResponse<Boolean>> handleValidationErrors(
      MethodArgumentNotValidException ex) {
    List<ApiResponseError> errors = new ArrayList<>();

    for (ObjectError error : ex.getBindingResult().getAllErrors()) {
      ApiResponseError apiResponseError;
      if (error instanceof FieldError) {
        apiResponseError = ApiResponseError
            .builder()
            .errorField(((FieldError) error).getField())
            .errorMessage(error.getDefaultMessage())
            .build();
      } else {
        apiResponseError = ApiResponseError
            .builder()
            .errorField(error.getObjectName())
            .errorMessage(error.getDefaultMessage())
            .build();
      }
      errors.add(apiResponseError);
    }
    HttpStatus statusCode = HttpStatus.BAD_REQUEST;
    return new ResponseEntity<>(
        ApiResponse.create(false, false, errors, statusCode),
        new HttpHeaders(),
        statusCode);
  }

  @ExceptionHandler(AccessDeniedException.class)
  public ResponseEntity<ApiResponse<Boolean>> handleAccessDenied(
      AccessDeniedException ex) {
    HttpStatus statusCode = HttpStatus.FORBIDDEN;
    return new ResponseEntity<>(
        ApiResponse.create(false, false, ex.getMessage(), statusCode),
        new HttpHeaders(),
        statusCode);
  }

  @ExceptionHandler(ConstraintViolationException.class)
  public ResponseEntity<ApiResponse<Boolean>> handleConstraintValidationErrors(
      ConstraintViolationException ex) {
    List<ApiResponseError> errors = ex.getConstraintViolations().stream().map(
        constraintViolation -> ApiResponseError.builder()
            .errorMessage(constraintViolation.getMessage())
            .build()).toList();
    HttpStatus statusCode = HttpStatus.BAD_REQUEST;
    return new ResponseEntity<>(
        ApiResponse.create(false, false, errors, statusCode),
        new HttpHeaders(),
        statusCode);
  }

  @ExceptionHandler({NotFoundException.class, DuplicateFoundException.class, ReferencedException.class, IllegalArgumentException.class, EncryptionException.class, OssmosisDataIntegrityViolation.class})
  public ResponseEntity<ApiResponse<Boolean>> handleResourcesNotFoundErrors(
      RuntimeException ex) {
    logger.debug(ex.getMessage());
    HttpStatus statusCode = HttpStatus.BAD_REQUEST;
    return new ResponseEntity<>(
        ApiResponse.create(false, false, ex.getMessage(), statusCode),
        new HttpHeaders(),
        statusCode);
  }

  @ExceptionHandler({BroadsoftConnectionException.class, BroadsoftNotFoundException.class, BroadsoftUnknownException.class})
  public ResponseEntity<ApiResponse<Boolean>> handleBroadsoftErrors(RuntimeException ex) {
    logger.debug(ex.getMessage());
    HttpStatus statusCode = null;
    if (ex instanceof BroadsoftConnectionException) statusCode = HttpStatus.SERVICE_UNAVAILABLE;
    else if (ex instanceof BroadsoftNotFoundException) statusCode = HttpStatus.BAD_REQUEST;
    else statusCode = HttpStatus.BAD_GATEWAY;
    return new ResponseEntity<>(ApiResponse.create(false, false, ex.getMessage(), statusCode), new HttpHeaders(), statusCode);
  }

  @ExceptionHandler({RedskyConnectionException.class, RedskyNotFoundException.class, RedskyAddressValidationException.class,
      RedskyUnknownException.class})
  public ResponseEntity<ApiResponse<Boolean>> handleRedskyErrors(RuntimeException ex) {
    logger.debug(ex.getMessage());
    HttpStatus statusCode = null;
    if (ex instanceof RedskyConnectionException) statusCode = HttpStatus.SERVICE_UNAVAILABLE;
    else if ((ex instanceof RedskyNotFoundException) || (ex instanceof RedskyAddressValidationException)) statusCode = HttpStatus.BAD_REQUEST;
    else statusCode = HttpStatus.BAD_GATEWAY;
    return new ResponseEntity<>(ApiResponse.create(false, false, ex.getMessage(), statusCode), new HttpHeaders(), statusCode);
  }
}