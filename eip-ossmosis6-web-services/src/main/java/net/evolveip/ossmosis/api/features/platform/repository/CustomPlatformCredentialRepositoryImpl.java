package net.evolveip.ossmosis.api.features.platform.repository;

import jakarta.persistence.EntityManager;
import java.sql.CallableStatement;
import java.util.List;
import java.util.Optional;
import net.evolveip.ossmosis.api.features.platform.constant.PlatformCredentialConstants;
import net.evolveip.ossmosis.api.features.platform.entity.*;
import org.hibernate.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class CustomPlatformCredentialRepositoryImpl {

  private final PlatformCredentialRepository platformCredentialRepository;
  private final EntityManager entityManager;

  @Value(value = "${spring.datasource.column.encryption.key}")
  private String encryptionKey;

  private final String variableName = "var.encryption_key";

  @Autowired
  public CustomPlatformCredentialRepositoryImpl(
      PlatformCredentialRepository platformCredentialRepository,
      EntityManager entityManager) {
    this.platformCredentialRepository = platformCredentialRepository;
    this.entityManager = entityManager;
  }

  public PlatformCredential savePlatformCredential(PlatformCredential platformCredential) {
    setupEncryptionKey();
    return platformCredentialRepository.save(platformCredential);
  }

  public List<PlatformCredential> getPlatformCredentialsByPlatformId(Integer platformId) {
    setupEncryptionKey();
    return platformCredentialRepository.findAllByPlatformPlatformId(platformId);
  }

  private void setupEncryptionKey() {
    Session curSession = entityManager.unwrap(Session.class);
    curSession.doWork(connection -> {
      CallableStatement ps = connection.prepareCall(
          "{call partnerprovider.fn_setup_secret(?,?)}");
      ps.setObject(1, this.variableName);
      ps.setObject(2, this.encryptionKey);
      ps.execute();
    });
  }

  public boolean existsById(int platformCredentialId) {
    return platformCredentialRepository.existsById(platformCredentialId);
  }

  public <T extends PlatformCredential> Optional<T> findCredentialByPlatformIdAndType(Integer platformId, String type) {
    setupEncryptionKey();
    Class<T> entityClass = getEntityClass(type);
    if (entityClass == null) {
      return Optional.empty();
    }
    return platformCredentialRepository.findByPlatformPlatformIdAndType(platformId, entityClass);
  }

  @SuppressWarnings("unchecked")
  private <T extends PlatformCredential> Class<T> getEntityClass(String type) {
    switch (type) {
      case PlatformCredentialConstants.BROADSOFT_AS_CRED_TYPE:
        return (Class<T>) BroadsoftASCredential.class;
      case PlatformCredentialConstants.BROADSOFT_NS_CRED_TYPE:
        return (Class<T>) BroadsoftNSCredential.class;
      case PlatformCredentialConstants.DUBBER_CRED_TYPE:
        return (Class<T>) DubberCredential.class;
      case PlatformCredentialConstants.REDSKY_CRED_TYPE:
        return (Class<T>) RedskyCredential.class;
      case PlatformCredentialConstants.WEBEX_CRED_TYPE:
        return (Class<T>) WebexCredential.class;
      default:
        return null;
    }
  }
}
