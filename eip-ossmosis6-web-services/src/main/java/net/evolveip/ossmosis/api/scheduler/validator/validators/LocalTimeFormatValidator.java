package net.evolveip.ossmosis.api.scheduler.validator.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidLocalTime;

public class LocalTimeFormatValidator implements ConstraintValidator<ValidLocalTime, String> {

  private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");

  @Override
  public void initialize(ValidLocalTime constraintAnnotation) {
  }

  @Override
  public boolean isValid(String timeField, ConstraintValidatorContext context) {
    if (timeField == null) {
      return false;
    }
    try {
      LocalTime.parse(timeField, formatter);
      return true;
    } catch (DateTimeParseException e) {
      return false;
    }
  }
}