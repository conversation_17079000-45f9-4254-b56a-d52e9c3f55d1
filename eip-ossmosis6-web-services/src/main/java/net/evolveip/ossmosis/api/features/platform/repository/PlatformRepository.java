package net.evolveip.ossmosis.api.features.platform.repository;

import net.evolveip.ossmosis.api.features.platform.entity.Platform;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface PlatformRepository extends JpaRepository<Platform, Integer> {

    Optional<Platform> findByPlatformName(String platformName);

    Optional<Platform> findByPlatformId(Integer platformId);

    @Query(value = "select count(p) from Platform p where lower(cast(p.platformId as string)) like %:filterValue% or lower(p.platformName) like %:filterValue%")
    long findFilteredCount(String filterValue);

    @Query(value = "select p from Platform p where lower(cast(p.platformId as string)) like %:filterValue% or lower(p.platformName) like %:filterValue%")
    Page<Platform> findAllByFilteredPage(String filterValue, Pageable pageable);
}