package net.evolveip.ossmosis.api.features.enterprise.dto;

import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_BLANK_VALIDATION_MESSAGE;
import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_EMPTY_VALIDATION_MESSAGE;
import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_NULL_VALIDATION_MESSAGE;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformRequestDTO;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Builder
public class EnterpriseRequestDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  @NotBlank(message = "enterpriseId" + NOT_BLANK_VALIDATION_MESSAGE)
  @NotNull(message = "enterpriseId" + NOT_NULL_VALIDATION_MESSAGE)
  @NotEmpty(message = "enterpriseId" + NOT_EMPTY_VALIDATION_MESSAGE)
  private String enterpriseId;

  @NotBlank(message = "billingId" + NOT_BLANK_VALIDATION_MESSAGE)
  @NotNull(message = "billingId" + NOT_NULL_VALIDATION_MESSAGE)
  @NotEmpty(message = "billingId" + NOT_EMPTY_VALIDATION_MESSAGE)
  private String billingId;

  @NotBlank(message = "enterpriseName" + NOT_BLANK_VALIDATION_MESSAGE)
  @NotNull(message = "enterpriseName" + NOT_NULL_VALIDATION_MESSAGE)
  @NotEmpty(message = "enterpriseName" + NOT_EMPTY_VALIDATION_MESSAGE)
  private String enterpriseName;

  @NotBlank(message = "accountCountry" + NOT_BLANK_VALIDATION_MESSAGE)
  @NotNull(message = "accountCountry" + NOT_NULL_VALIDATION_MESSAGE)
  @NotEmpty(message = "accountCountry" + NOT_EMPTY_VALIDATION_MESSAGE)
  private String accountCountry;

  @NotNull(message = "platformId" + NOT_NULL_VALIDATION_MESSAGE)
  private Integer platformId;

  @NotBlank(message = "parentEnterpriseId" + NOT_BLANK_VALIDATION_MESSAGE)
  @NotNull(message = "parentEnterpriseId" + NOT_NULL_VALIDATION_MESSAGE)
  @NotEmpty(message = "parentEnterpriseId" + NOT_EMPTY_VALIDATION_MESSAGE)
  private String parentEnterpriseId;
}