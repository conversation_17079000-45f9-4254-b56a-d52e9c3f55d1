package net.evolveip.ossmosis.api.scheduler.common.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.scheduler.common.enums.JobType;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidBoolean;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidEnum;

@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HttpRequestCreateJobDefinition {

  @NotNull
  @Valid
  @ValidEnum(enumClass = JobType.class)
  private String jobType;

  @NotNull
  @ValidBoolean
  private String enabled;

  @NotNull
  private String createdBy;

  @NotNull
  private String createdTimestamp;

  @NotNull
  private String updatedBy;

  @NotNull
  private String updatedTimestamp;

  @NotNull
  @Valid
  private HttpRequestCreateCallDetailReport jobData;
}
