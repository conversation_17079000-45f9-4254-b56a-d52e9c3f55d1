package net.evolveip.ossmosis.api.features.phonenumber.controller;

import java.util.List;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.PhoneNumberValidationRequestDTO;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.PhoneNumberValidationResponseDTO;
import net.evolveip.ossmosis.api.features.phonenumber.service.PhoneNumbersValidationService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/phonenumber/validate")
public class PhoneNumbersValidationResource {

  private final PhoneNumbersValidationService phoneNumbersValidationService;

  @Autowired
  public PhoneNumbersValidationResource(
      PhoneNumbersValidationService phoneNumbersValidationService) {
    this.phoneNumbersValidationService = phoneNumbersValidationService;
  }

  @PostMapping
  public ResponseEntity<ApiResponse<PhoneNumberValidationResponseDTO>> doValidateExternalPhoneNumbersList(
      @RequestBody List<PhoneNumberValidationRequestDTO> phoneNumbers) {
    ApiResponse<PhoneNumberValidationResponseDTO> apiResponse =
        phoneNumbersValidationService.processValidateExternalPhoneNumbersList(phoneNumbers);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }
}