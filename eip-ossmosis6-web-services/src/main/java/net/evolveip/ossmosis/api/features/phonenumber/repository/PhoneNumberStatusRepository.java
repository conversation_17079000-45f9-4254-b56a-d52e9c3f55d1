package net.evolveip.ossmosis.api.features.phonenumber.repository;

import java.util.Optional;
import java.util.Set;
import net.evolveip.ossmosis.api.features.phonenumber.entity.PhoneNumberStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PhoneNumberStatusRepository extends JpaRepository<PhoneNumberStatus, Integer> {

  Optional<PhoneNumberStatus> findPhoneNumberStatusByStatusName(String statusName);

  @Query(value = "SELECT (COUNT(pns.statusId) = :size) " +
      "FROM PhoneNumberStatus pns WHERE pns.statusId IN :statusIdsSet")
  boolean allOrNonePhoneNumberStatusIdExistByStatusId(
      @Param("statusIdsSet") Set<Integer> statusIdsSet,
      @Param("size") int size
  );
}