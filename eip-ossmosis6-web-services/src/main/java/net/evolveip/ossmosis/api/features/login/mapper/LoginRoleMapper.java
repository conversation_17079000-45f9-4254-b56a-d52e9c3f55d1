package net.evolveip.ossmosis.api.features.login.mapper;


import net.evolveip.ossmosis.api.features.login.dto.LoginRoleRequestDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginRoleResponseDTO;
import net.evolveip.ossmosis.api.features.login.entity.LoginRole;
import net.evolveip.ossmosis.api.features.role.mapper.RoleMapper;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = ComponentModel.SPRING, uses = {
    RoleMapper.class}, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface LoginRoleMapper {

  LoginRoleMapper INSTANCE = Mappers.getMapper(LoginRoleMapper.class);

  @Mapping(ignore = true, target = "loginRoleId")
  @Mapping(target = "dateCreated", expression = "java(java.time.ZonedDateTime.now())")
  @Mapping(target = "dateUpdated", expression = "java(java.time.ZonedDateTime.now())")
  @Mapping(source = "loginId", target = "login.loginId")
  @Mapping(source = "roleId", target = "role.roleId")
  LoginRole toEntity(LoginRoleRequestDTO loginRoleRequestDTO);

  @Mapping(source = "dateCreated", target = "dateCreated")
  @Mapping(source = "dateUpdated", target = "dateUpdated")
  @Mapping(source = "login.loginId", target = "loginId")
  @Mapping(source = "role.roleId", target = "roleId")
  LoginRoleResponseDTO fromEntity(LoginRole loginRole);
}