package net.evolveip.ossmosis.api.scheduler.service.trigger;


import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.ANSWER_STATUS;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.CALL_TYPE;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.DATA_WINDOW;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.DATA_WINDOW_END_TIME;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.DATA_WINDOW_OFFSET;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.DATA_WINDOW_OFFSET_UNITS;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.DATA_WINDOW_UNITS;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.DIRECTION;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.EXTERNAL_ONLY;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.GROUP_BY;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.GROUP_NUMBER;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.JOB_BUILDER_DESCRIPTION;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.REPORT_TITLE;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.USER_NUMBER;
import static net.evolveip.ossmosis.api.scheduler.common.constants.CallDetailReportConstants.USER_TYPE;
import static net.evolveip.ossmosis.api.scheduler.common.constants.EmailDefinitionConstants.ENTERPRISE_ID;
import static net.evolveip.ossmosis.api.scheduler.common.constants.EmailDefinitionConstants.ENTERPRISE_NAME;
import static net.evolveip.ossmosis.api.scheduler.common.constants.EmailDefinitionConstants.FROM_EMAIL;
import static net.evolveip.ossmosis.api.scheduler.common.constants.EmailDefinitionConstants.TO_EMAIL;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobDefinitionConstants.CREATED_BY;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobDefinitionConstants.CREATED_TIMESTAMP;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobDefinitionConstants.JOB_TYPE;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobDefinitionConstants.UPDATED_BY;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobDefinitionConstants.UPDATED_TIMESTAMP;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.CRON_JOB_EXPRESSION;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.USER_TIME_ZONE;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.FREQUENCY;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.FREQUENCY_UNITS;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.REPORT_RUN_TIME;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.SCHEDULE_DEFINITION;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.SCHEDULE_TYPE;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.SELECTED_MONTH_RUN_DAYS;
import static net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants.SELECTED_WEEK_RUN_DAYS;

import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import net.evolveip.ossmosis.api.scheduler.common.constants.JobScheduleDefinitionConstants;
import net.evolveip.ossmosis.api.scheduler.common.dto.CallDetailReportDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.CallDetailReportRunnerDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.CronScheduleDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.CronScheduleJobDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.EmailDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.JobDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.SimpleScheduleDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.SimpleScheduleJobDTO;
import net.evolveip.ossmosis.api.scheduler.job.BaseJobDetail;
import net.evolveip.ossmosis.api.scheduler.job.CallDetailReportRunner;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.springframework.stereotype.Service;

@Service
public class JobDetailService implements BaseJobDetail {


  public JobDetail createJobDetail(SimpleScheduleJobDTO entryDTO, String enterpriseName) {
    EmailDefinitionDTO emailDefinitionDTO = entryDTO.getEmailDefinition();
    SimpleScheduleDefinitionDTO scheduleDefinitionDTO = entryDTO.getJobScheduleDefinition();
    JobDefinitionDTO jobDefinitionDTO = entryDTO.getJobDefinition();
    CallDetailReportDTO jobData = jobDefinitionDTO.getJobData();

    JobDataMap scheduleDefinitionJobDataMap = createScheduleDefinitionMap(scheduleDefinitionDTO);
    JobDataMap jobDataMap = builJobDataMap(
        emailDefinitionDTO,
        scheduleDefinitionJobDataMap,
        jobDefinitionDTO,
        jobData,
        enterpriseName);

    return JobBuilder
        .newJob(CallDetailReportRunner.class)
        .withIdentity(generateJobId(), emailDefinitionDTO.getEnterpriseId())
        .withDescription(JOB_BUILDER_DESCRIPTION)
        .usingJobData(jobDataMap)
        .storeDurably()
        .build();
  }

  public JobDetail createJobDetail(CronScheduleJobDTO entryDto, String enterpriseName) {
    EmailDefinitionDTO emailDefinitionDTO = entryDto.getEmailDefinition();
    CronScheduleDefinitionDTO scheduleDefinitionDTO = entryDto.getJobScheduleDefinition();
    JobDefinitionDTO jobDefinitionDTO = entryDto.getJobDefinition();
    CallDetailReportDTO jobData = jobDefinitionDTO.getJobData();

    JobDataMap scheduleDefinitionJobDataMap = createScheduleDefinitionMap(scheduleDefinitionDTO);
    JobDataMap jobDataMap = builJobDataMap(
        emailDefinitionDTO,
        scheduleDefinitionJobDataMap,
        jobDefinitionDTO,
        jobData,
        enterpriseName);

    return JobBuilder
        .newJob(CallDetailReportRunner.class)
        .withIdentity(generateJobId(), emailDefinitionDTO.getEnterpriseId())
        .withDescription(JOB_BUILDER_DESCRIPTION)
        .usingJobData(jobDataMap)
        .storeDurably()
        .build();
  }

  public CallDetailReportRunnerDTO buildRunnerDTO(JobDataMap jobDataMap) {
    return CallDetailReportRunnerDTO.builder().enterpriseId(jobDataMap.getString(ENTERPRISE_ID))
        .enterpriseName(jobDataMap.getString(ENTERPRISE_NAME))
        .fromEmail(jobDataMap.getString(FROM_EMAIL))
        .toEmail((List<String>) jobDataMap.get(TO_EMAIL))
        .reportTitle(jobDataMap.getString(REPORT_TITLE))
        .answerStatus(jobDataMap.getString(ANSWER_STATUS))
        .externalOnly(jobDataMap.getString(EXTERNAL_ONLY))
        .direction(jobDataMap.getString(DIRECTION))
        .callType(jobDataMap.getString(CALL_TYPE))
        .userType(jobDataMap.getString(USER_TYPE))
        .userNumber((List<String>) jobDataMap.get(USER_NUMBER))
        .groupNumber((List<String>) jobDataMap.get(GROUP_NUMBER))
        .groupBy(jobDataMap.getString(GROUP_BY))
        .dataWindowOffset((Integer) jobDataMap.get(DATA_WINDOW_OFFSET))
        .dataWindowOffsetUnits((ChronoUnit) jobDataMap.get(DATA_WINDOW_OFFSET_UNITS))
        .dataWindowEndTime((LocalTime) jobDataMap.get(DATA_WINDOW_END_TIME))
        .dataWindow((Integer) jobDataMap.get(DATA_WINDOW))
        .dataWindowUnits((ChronoUnit) jobDataMap.get(DATA_WINDOW_UNITS))
        .build();
  }

  // JobDataMap build happens at the creation of a scheduled job:
  private JobDataMap builJobDataMap(
      EmailDefinitionDTO emailRequestDTO,
      JobDataMap scheduleDefinition,
      JobDefinitionDTO jobDefinitionDTO,
      CallDetailReportDTO reportDTO, String enterpriseName) {

    JobDataMap jobDataMap = getJobDataMap();
    jobDataMap.put(SCHEDULE_DEFINITION, scheduleDefinition);

    jobDataMap.put(ENTERPRISE_ID, emailRequestDTO.getEnterpriseId());
    jobDataMap.put(ENTERPRISE_NAME, enterpriseName);
    jobDataMap.put(FROM_EMAIL, emailRequestDTO.getFromEmail());
    jobDataMap.put(TO_EMAIL, emailRequestDTO.getToEmail());
    jobDataMap.put(REPORT_TITLE, reportDTO.getReportTitle());
    jobDataMap.put(JOB_TYPE, jobDefinitionDTO.getJobType());
    jobDataMap.put(CREATED_BY, jobDefinitionDTO.getCreatedBy());
    jobDataMap.put(CREATED_TIMESTAMP, jobDefinitionDTO.getCreatedTimestamp());
    jobDataMap.put(UPDATED_BY, jobDefinitionDTO.getUpdatedBy());
    jobDataMap.put(UPDATED_TIMESTAMP, jobDefinitionDTO.getUpdatedTimestamp());
    jobDataMap.put(ANSWER_STATUS, reportDTO.getAnswerStatus());
    jobDataMap.put(EXTERNAL_ONLY, reportDTO.getExternalOnly());
    jobDataMap.put(DIRECTION, reportDTO.getDirection());
    jobDataMap.put(CALL_TYPE, reportDTO.getCallType());
    jobDataMap.put(USER_TYPE, reportDTO.getUserType());
    jobDataMap.put(USER_NUMBER, reportDTO.getUserNumbers());
    jobDataMap.put(GROUP_NUMBER, reportDTO.getGroupNumbers());
    jobDataMap.put(GROUP_BY, reportDTO.getGroupBy());
    jobDataMap.put(DATA_WINDOW_OFFSET, reportDTO.getDataWindowOffset());
    jobDataMap.put(DATA_WINDOW_OFFSET_UNITS, reportDTO.getDataWindowOffsetUnits());
    jobDataMap.put(DATA_WINDOW_END_TIME, reportDTO.getDataWindowEndTime());
    jobDataMap.put(DATA_WINDOW, reportDTO.getDataWindow());
    jobDataMap.put(DATA_WINDOW_UNITS, reportDTO.getDataWindowUnits());

    return jobDataMap;
  }

  private JobDataMap createScheduleDefinitionMap(
      SimpleScheduleDefinitionDTO scheduleDefinitionDTO) {
    JobDataMap jobDataMap = getJobDataMap();
    jobDataMap.put(SCHEDULE_TYPE, scheduleDefinitionDTO.getScheduleType());
    jobDataMap.put(USER_TIME_ZONE, scheduleDefinitionDTO.getUserTimeZone());
    jobDataMap.put(REPORT_RUN_TIME, scheduleDefinitionDTO.getReportRunTime());
    jobDataMap.put(FREQUENCY, scheduleDefinitionDTO.getFrequency());
    jobDataMap.put(FREQUENCY_UNITS, scheduleDefinitionDTO.getFrequencyUnits());
    jobDataMap.put(SELECTED_WEEK_RUN_DAYS, scheduleDefinitionDTO.getSelectedWeekRunDays());
    jobDataMap.put(SELECTED_MONTH_RUN_DAYS, scheduleDefinitionDTO.getSelectedMonthRunDays());
    return jobDataMap;
  }

  private JobDataMap createScheduleDefinitionMap(
      CronScheduleDefinitionDTO scheduleDefinitionDTO) {
    JobDataMap jobDataMap = getJobDataMap();
    jobDataMap.put(SCHEDULE_TYPE, scheduleDefinitionDTO.getScheduleType());
    jobDataMap.put(CRON_JOB_EXPRESSION, scheduleDefinitionDTO.getCronJobExpression().toString());
    jobDataMap.put(
        JobScheduleDefinitionConstants.USER_TIME_ZONE, scheduleDefinitionDTO.getUserTimeZone().toString());
    return jobDataMap;
  }
}