package net.evolveip.ossmosis.api.scheduler.service.scheduler;

import net.evolveip.ossmosis.api.features.enterprise.service.EnterpriseService;
import net.evolveip.ossmosis.api.scheduler.common.dto.SchedulerJobsStatesDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.SchedulerMetadataDTO;
import net.evolveip.ossmosis.api.scheduler.mapper.MapperSchedulerMetadata;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.quartz.Scheduler;
import org.springframework.stereotype.Component;

@Component
public class SchedulerService extends AbstractSchedulerService {

  public SchedulerService(Scheduler scheduler, EnterpriseService enterpriseService,
      MapperSchedulerMetadata mapper) {
    super(scheduler, enterpriseService, mapper);
  }

  /***
   * Starts the scheduler
   * @return a Map with all metadata entries
   */
  public ApiResponse<SchedulerMetadataDTO> processStart() {
    try {
      scheduler.start();
      return response(getStatus());
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  /***
   * Places the scheduler in standby mode.
   * @return a Map with all metadata entries
   */
  public ApiResponse<SchedulerMetadataDTO> processStandby() {
    try {
      scheduler.standby();
      return response(getStatus());
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  /***
   * Pauses all jobs, scheduler-wise.
   * @return a Map with all jobs states and their respective counters
   */
  public ApiResponse<SchedulerJobsStatesDTO> processPauseAll() {
    try {
      scheduler.pauseAll();
      return response(getJobsStates());
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  /***
   * Resumes all jobs, scheduler-wise.
   * @return a Map with all jobs states and their respective counters
   */
  public ApiResponse<SchedulerJobsStatesDTO> processResumeAll() {
    try {
      scheduler.resumeAll();
      return response(getJobsStates());
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  /***
   * Retrieves and counts all jobs' statuses. It counts for all enterprises.
   * @return a Map with all unique statuses values and their respective counters.
   */
  public ApiResponse<SchedulerJobsStatesDTO> processJobsStatus() {
    try {
      return response(getJobsStates());
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  /***
   * Retrieves the scheduler metadata status.
   * @return a Map with all metadata entries
   */
  public ApiResponse<SchedulerMetadataDTO> processStatus() {
    try {
      return response(getStatus());
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }
}