package net.evolveip.ossmosis.api.config.swagger;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;

@Configuration
public class OssmosisSwaggerConfig {

  @Bean
  public OpenAPI openAPI() {
    String swaggerDocsDesc = "The API used to run OSSmosis 6 front end.";
    String swaggerDocsTitle = "OSSmosis 6 REST API";
    String swaggerDocsVersion = "1.0.0";
    String swaggerDocsSecurityAuth = "Bearer";

    return new OpenAPI()
        .addSecurityItem(new SecurityRequirement()
            .addList(swaggerDocsSecurityAuth))
        .components(new Components()
            .addSecuritySchemes(swaggerDocsSecurityAuth, createAPIKeyScheme()))
        .info(new Info()
            .title(swaggerDocsTitle)
            .description(swaggerDocsDesc)
            .version(swaggerDocsVersion));
  }

  private SecurityScheme createAPIKeyScheme() {
    return new SecurityScheme()
        .type(SecurityScheme.Type.HTTP)
        .bearerFormat("JWT")
        .scheme("bearer");
  }
}
