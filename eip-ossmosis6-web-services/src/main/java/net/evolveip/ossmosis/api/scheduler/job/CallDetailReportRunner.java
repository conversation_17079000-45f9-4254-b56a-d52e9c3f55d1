package net.evolveip.ossmosis.api.scheduler.job;

import static org.apache.commons.lang3.StringUtils.join;

import jakarta.mail.MessagingException;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.TimeZone;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.config.csv.CSVTempLocationPropertiesConfig;
import net.evolveip.ossmosis.api.config.factory.GenericServiceFactory;
import net.evolveip.ossmosis.api.features.calldetails.dto.CallDetailDTO;
import net.evolveip.ossmosis.api.features.calldetails.dto.CallDetailSummaryDTO;
import net.evolveip.ossmosis.api.features.calldetails.dto.CallDetailSummaryListWrapperDTO;
import net.evolveip.ossmosis.api.features.calldetails.service.CallDetailService;
import net.evolveip.ossmosis.api.scheduler.common.TimeUtils;
import net.evolveip.ossmosis.api.scheduler.common.dto.CallDetailReportRunnerDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.ZonedDateTimeRangeDTO;
import net.evolveip.ossmosis.api.scheduler.service.email.EmailService;
import net.evolveip.ossmosis.api.scheduler.service.trigger.JobDetailService;
import net.evolveip.ossmosis.api.utils.file.CSVService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.snowflake.client.jdbc.SnowflakeSQLException;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.springframework.mail.MailSendException;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CallDetailReportRunner implements Job, BaseJobRunner, TimeUtils, CalculateDataWindow {

  private static final String CALL_DETAILS_SUMMARY = "CallDetailsSummary";
  private static final String CALL_DETAILS_LIST = "CallDetailsList";
  private static final String EXTENSION = "csv";
  private final GenericServiceFactory serviceFactory;
  private final EmailService emailService;
  private final CSVTempLocationPropertiesConfig tempDir;
  private final JobDetailService jobDetailService;

  public CallDetailReportRunner(GenericServiceFactory serviceFactory, EmailService emailService,
      CSVTempLocationPropertiesConfig tempDir, JobDetailService jobDetailService) {
    this.serviceFactory = serviceFactory;
    this.emailService = emailService;
    this.tempDir = tempDir;
    this.jobDetailService = jobDetailService;
  }

  @Override
  public void execute(JobExecutionContext context) throws JobExecutionException {

    // get the connection to the reports service
    CallDetailService reportsService;
    try {
      reportsService = serviceFactory.getService(CallDetailService.class);
    } catch (Exception e) {
      logger.debug(e.getMessage());
      throw new JobExecutionException(e);
    }

    JobDetail jobDetail = context.getJobDetail();
    JobKey jobKey = jobDetail.getKey();
    String message = String.format("Executing Job with key: [%s]", jobKey);
    logger.info(message);

    // build the runner DTO:
    JobDataMap jobDataMap = context.getMergedJobDataMap();
    logger.debug(jobDataMap.toString());
    CallDetailReportRunnerDTO runnerDTO = jobDetailService.buildRunnerDTO(jobDataMap);
    TimeZone userTimeZone = getUserTimeZone(jobDataMap);

    ZonedDateTime now = nowEst();
    String nowString = now(now);
    String sessionId = randString(10);

    // compute the moving data window:
    ZonedDateTimeRangeDTO zonedDateTimeRangeDTO = getZonedDateTimeRange(
        runnerDTO.getDataWindowOffset(), runnerDTO.getDataWindowOffsetUnits(),
        runnerDTO.getDataWindowEndTime(), runnerDTO.getDataWindow(), runnerDTO.getDataWindowUnits(),
        now);

    // query snowflake/the reportservices datasource:
    ApiResponse<CallDetailSummaryListWrapperDTO> resultSet;
    try {
      resultSet = reportsService.processGet(
          runnerDTO.getAnswerStatus(), runnerDTO.getExternalOnly(), runnerDTO.getDirection(),
          runnerDTO.getCallType(), runnerDTO.getUserType(),
          zonedDateTimeRangeDTO.getStartRangeDateTime(),
          zonedDateTimeRangeDTO.getEndRangeDateTime(),
          runnerDTO.getUserNumber(), runnerDTO.getGroupNumber(), runnerDTO.getGroupBy(),
          Collections.singletonList(runnerDTO.getEnterpriseId()).toArray(new String[0]));
    } catch (SnowflakeSQLException e) {
      logger.error(e.getMessage());
      throw new JobExecutionException(e);
    }

    // perform a sanitizing check on the resultset:
    CallDetailSummaryListWrapperDTO payload;
    if (!resultSet.isSuccessful()) {
      String errorMessage = join(", ", resultSet.getErrors());
      logger.error(errorMessage);
      throw new RuntimeException(errorMessage);
    } else {
      payload = resultSet.getPayload();
    }

    if (payload.getCallDetails().isEmpty()) {
      try {
        emailService.sendHtmlEmail(runnerDTO.getFromEmail(), runnerDTO.getToEmail(),
            buildEmailSubject(runnerDTO.getReportTitle(), zonedDateTimeRangeDTO, userTimeZone),
            buildEmptyEmailBody(runnerDTO, zonedDateTimeRangeDTO, jobKey, now, userTimeZone));
      } catch (MailSendException | MessagingException e) {
        Throwable ne = ExceptionUtils.getRootCause(e);
        String errorMessage = "Job key: [" + jobKey.toString() + "]\t - ";
        logger.error("{}{}{}", errorMessage, e.getMessage(), ne.getMessage());
      }
    } else {

      // generate the CSV files:
      String enterpriseId = runnerDTO.getEnterpriseId();
      File callDetailsSummaryFile;
      try {
        String callDetailsSummaryFileName = fileNameBuilder(enterpriseId, nowString, sessionId,
            CALL_DETAILS_SUMMARY, EXTENSION);
        CSVService<CallDetailSummaryDTO> csvService = new CSVService<>();
        callDetailsSummaryFile = csvService.generateCsv(payload.getCallDetails(), tempDir,
            callDetailsSummaryFileName, userTimeZone.toZoneId().toString());
      } catch (IOException e) {
        logger.error(e.getMessage());
        throw new JobExecutionException(e);
      }

      File callDetailsListFile;
      try {
        String callDetailsListFileName = fileNameBuilder(enterpriseId, nowString, sessionId,
            CALL_DETAILS_LIST, EXTENSION);
        List<CallDetailDTO> callDetailDTOList = new ArrayList<>();
        for (CallDetailSummaryDTO callDetailSummaryDTO : payload.getCallDetails()) {
          callDetailDTOList.addAll(callDetailSummaryDTO.getCallDetailDTOList());
        }
        CSVService<CallDetailDTO> csvService = new CSVService<>();
        callDetailsListFile = csvService.generateCsv(callDetailDTOList, tempDir,
            callDetailsListFileName, userTimeZone.toZoneId().toString());
      } catch (IOException e) {
        logger.error(e.getMessage());
        throw new JobExecutionException(e);
      }

      // send the emails:
      try {
        emailService.sendHtmlEmailWithAttachment(runnerDTO.getFromEmail(), runnerDTO.getToEmail(),
            buildEmailSubject(runnerDTO.getReportTitle(), zonedDateTimeRangeDTO, userTimeZone),
            buildEmailBody(runnerDTO, zonedDateTimeRangeDTO, jobKey, now, userTimeZone),
            callDetailsSummaryFile, callDetailsListFile);
      } catch (MailSendException | MessagingException e) {
        Throwable ne = ExceptionUtils.getRootCause(e);
        String errorMessage = "Job key: [" + jobKey.toString() + "]\t - ";
        logger.error("{}{}{}", errorMessage, e.getMessage(), ne.getMessage());
      } finally {
        fileDisposal(callDetailsSummaryFile);
        fileDisposal(callDetailsListFile);
      }
    }
  }

  private TimeZone getUserTimeZone(JobDataMap jobDataMap) {
    JobDataMap scheduleDefinition = (JobDataMap) jobDataMap.get("scheduleDefinition");
    String timeZone = scheduleDefinition.get("userTimeZone").toString();
    return TimeZone.getTimeZone(timeZone);
  }

  private void fileDisposal(File file) {
    try {
      Files.delete(file.toPath());
    } catch (IOException e) {
      logger.error("Fail to delete file:\t{}", file.getAbsolutePath(), e);
    }
  }

  @Override
  public String buildEmailBody(CallDetailReportRunnerDTO dto,
      ZonedDateTimeRangeDTO rangeDTO, JobKey jobKey, ZonedDateTime nowDateTime,
      TimeZone userTimeZone) {
    StringBuilder sb = new StringBuilder();

    sb.append("<html>\n"
        + "<head>\n"
        + "\t<style>\n"
        + "\t\t.email-container {\n"
        + "\t\t\tfont-family: Arial, sans-serif;\n"
        + "\t\t\tcolor: #333;\n"
        + "\t\t\tpadding: 20px;\n"
        + "\t\t}\n"
        + "\t\t.email-content {\n"
        + "\t\t\tbackground-color: #F9F9F9;\n"
        + "\t\t\tborder-radius: 10px;\n"
        + "\t\t\tpadding: 20px;\n"
        + "\t\t\tborder: 1px solid #ddd;\n"
        + "\t\t\tfont-size: 18px;\n"
        + "\t\t}\n"
        + "\t\t.header2 {\n"
        + "\t\t\tcolor: #0451A1;\n"
        + "\t\t}\n"
        + "\t\t.header3 {\n"
        + "\t\t\tcolor: #F47920;\n"
        + "\t\t}\n"
        + "\t\t.emptyData {\n"
        + "\t\t\tcolor: #dc3545;\n"
        + "\t\t}\n"
        + "\t</style>\n"
        + "</head>"
        + "<body>\n"
        + "\t<div class=\"email-container\">\n"
        + "\t\t<div class=\"email-content\">"
        + "<h2 class=\"header2\">Hello!</h2>");
    sb.append("<p>This is the <strong>").append(dto.getReportTitle())
        .append("</strong> report that you requested, generated on <strong>")
        .append(convertToEmailDateTime(nowDateTime, userTimeZone)).append("</strong>.</p>");
    sb.append("<h3 class=\"header3\">Report Details:</h3>");
    sb.append("<ul>\n" + "\t\t\t\t<li><strong>Enterprise ID:</strong> ")
        .append(dto.getEnterpriseId()).append("</li>\n")
        .append("\t\t\t\t<li><strong>Job ID:</strong> ").append(jobKey.toString().split("\\.")[1])
        .append("</li>\n")
        .append("\t\t\t\t<li><strong>Call Detail Report for:</strong> ")
        .append(dto.getEnterpriseName()).append("</li>\n")
        .append("\t\t\t\t<li><strong>Data Range:</strong> ")
        .append(convertToEmailDateTime(rangeDTO.getStartRangeDateTime(), userTimeZone))
        .append(" - ")
        .append(convertToEmailDateTime(rangeDTO.getEndRangeDateTime(), userTimeZone))
        .append("</li>\n")
        .append("\t\t\t</ul>");
    sb.append("<h3 class=\"header3\">Report Filters:</h3>");
    sb.append("<ul>\n" + "\t\t\t\t<li><strong>Answered Status:</strong> ")
        .append(dto.getAnswerStatus()).append("</li>\n")
        .append("\t\t\t\t<li><strong>External Only:</strong> ").append(dto.getExternalOnly())
        .append("</li>\n").append("\t\t\t\t<li><strong>Direction:</strong> ")
        .append(dto.getDirection()).append("</li>\n")
        .append("\t\t\t\t<li><strong>Call Type:</strong> ").append(dto.getCallType())
        .append("</li>\n").append("\t\t\t\t<li><strong>User Type:</strong> ")
        .append(dto.getUserType()).append("</li>\n");
    sb.append("\t\t\t\t<li><strong>User Numbers: </strong>");
    if (!dto.getUserNumber().isEmpty()) {
      for (String userNumber : dto.getUserNumber()) {
        sb.append(userNumber).append(", ");
      }
      sb.delete(sb.length() - 2, sb.length());
    } else {
      sb.append("N/A");
    }
    sb.append("</li>\n");
    sb.append("\t\t\t\t<li><strong>Group Numbers: </strong>");
    if (!dto.getGroupNumber().isEmpty()) {
      for (String groupNumber : dto.getGroupNumber()) {
        sb.append(groupNumber).append(", ");
      }
      sb.delete(sb.length() - 2, sb.length());
    } else {
      sb.append("N/A");
    }
    sb.append("</li>\n");
    sb.append("\t\t\t</ul>");
    sb.append("<p>Please do not respond to this email, as this inbox is not monitored.</p>");
    sb.append("<br><p>Thank you</p></div></div></body></html>");

    return sb.toString();
  }

  private String buildEmptyEmailBody(CallDetailReportRunnerDTO dto,
      ZonedDateTimeRangeDTO rangeDTO, JobKey jobKey, ZonedDateTime nowDateTime,
      TimeZone userTimeZone) {
    StringBuilder sb = new StringBuilder();

    String string = buildEmailBody(dto, rangeDTO, jobKey, nowDateTime, userTimeZone);
    String[] parts = string.split("<h3 class=\"header3\">Report Details:</h3>");
    sb.append(parts[0]);
    sb.append("<br/><h2 class=\"emptyData\"><strong>There was no data found for the given filters.</strong></h2><br/>");
    sb.append("<h3 class=\"header3\">Report Details:</h3>");
    sb.append(parts[1]);
    return sb.toString();
  }
}
