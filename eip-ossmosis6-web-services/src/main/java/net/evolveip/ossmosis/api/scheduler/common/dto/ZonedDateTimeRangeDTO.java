package net.evolveip.ossmosis.api.scheduler.common.dto;

import java.time.ZonedDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class ZonedDateTimeRangeDTO {

  private ZonedDateTime startRangeDateTime;
  private ZonedDateTime endRangeDateTime;
}