package net.evolveip.ossmosis.api.scheduler.mapper;

import java.text.ParseException;
import java.time.ZoneId;
import net.evolveip.ossmosis.api.scheduler.common.dto.CronScheduleDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateCronScheduleDefinition;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;
import org.quartz.CronExpression;

@Mapper(componentModel = ComponentModel.SPRING, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface MapperCronScheduleDefinition {

  MapperCronScheduleDefinition INSTANCE = Mappers.getMapper(MapperCronScheduleDefinition.class);

  @Named("convertToCronExpression")
  static CronExpression toCronExpression(String value) throws ParseException {
    return new CronExpression(value);
  }

  @Named("convertToZoneId")
  static ZoneId toZoneId(String value) {
    return ZoneId.of(value);
  }

  @Mapping(source = "cronJobExpression", target = "cronJobExpression", qualifiedByName = "convertToCronExpression")
  @Mapping(target = "cronJobExpressionString", ignore = true)
  @Mapping(source = "userTimeZone", target = "userTimeZone", qualifiedByName = "convertToZoneId")
  CronScheduleDefinitionDTO toDto(HttpRequestCreateCronScheduleDefinition entryHttpCreateRequest);
}