package net.evolveip.ossmosis.api.scheduler.service.scheduler;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import net.evolveip.ossmosis.api.features.enterprise.service.EnterpriseService;
import net.evolveip.ossmosis.api.scheduler.common.dto.SchedulerJobsStatesDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.SchedulerMetadataDTO;
import net.evolveip.ossmosis.api.scheduler.mapper.MapperSchedulerMetadata;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger.TriggerState;
import org.quartz.TriggerKey;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractSchedulerService extends AbstractService {

  private final MapperSchedulerMetadata mapper;

  @Autowired
  public AbstractSchedulerService(Scheduler scheduler,
      EnterpriseService enterpriseService,
      MapperSchedulerMetadata mapper) {
    super(scheduler, enterpriseService);
    this.mapper = mapper;
  }

  // retrieves and counts all jobs/triggers:
  protected SchedulerJobsStatesDTO getJobsStates() throws SchedulerException {
    Map<String, Integer> jobsStatuses = new HashMap<>();
    for (TriggerState enumValue : TriggerState.values()) {
      jobsStatuses.put(enumValue.name(), 0);
    }
    Set<TriggerKey> triggerKeys = scheduler.getTriggerKeys(GroupMatcher.anyGroup());
    for (TriggerKey triggerKey : triggerKeys) {
      TriggerState triggerState = scheduler.getTriggerState(triggerKey);
      jobsStatuses.compute(triggerState.name(), (k, v) -> v + 1);
    }
    return SchedulerJobsStatesDTO
        .builder()
        .normal(jobsStatuses.get("NORMAL"))
        .paused(jobsStatuses.get("PAUSED"))
        .complete(jobsStatuses.get("COMPLETE"))
        .blocked(jobsStatuses.get("BLOCKED"))
        .error(jobsStatuses.get("ERROR"))
        .none(jobsStatuses.get("NONE"))
        .build();
  }

  protected SchedulerMetadataDTO getStatus() throws SchedulerException {
    return mapper.toDto(scheduler.getMetaData());
  }
}
