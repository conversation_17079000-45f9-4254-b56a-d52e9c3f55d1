package net.evolveip.ossmosis.api.scheduler.validator.annotations;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import net.evolveip.ossmosis.api.scheduler.validator.validators.LocalDateTimeValidator;

@Constraint(validatedBy = LocalDateTimeValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.PARAMETER, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidLocalDateTime {

  String message() default "Invalid local datetime format: [${validatedValue}]";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};

  String pattern() default "yyyy-MM-dd'T'HH:mm:ss";
}