package net.evolveip.ossmosis.api.scheduler.common.dto;

import jakarta.validation.Valid;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Valid
public class CallDetailReportDTO {

  private String reportTitle;
  private String answerStatus;
  private String externalOnly;
  private String direction;
  private String callType;
  private String userType;
  private List<String> userNumbers;
  private List<String> groupNumbers;
  private String groupBy;
  private Integer dataWindowOffset;
  private ChronoUnit dataWindowOffsetUnits;
  private LocalTime dataWindowEndTime;
  private Integer dataWindow;
  private ChronoUnit dataWindowUnits;
}