package net.evolveip.ossmosis.api.features.enterprise.dto;

import java.time.ZonedDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformResponseDTO;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Builder
public class EnterpriseResponseDTO {

  private String enterpriseId;
  private ZonedDateTime dateCreated;
  private ZonedDateTime dateUpdated;
  private String billingId;
  private String enterpriseName;
  private String accountCountry;
  private PlatformResponseDTO platform;
  private String parentEnterpriseId;
}