package net.evolveip.ossmosis.api.features.role.entity.view;

import java.time.ZonedDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Value;

public interface RolePermissionView {

  Integer getRoleId();

  ZonedDateTime getDateCreated();

  ZonedDateTime getDateUpdated();

  String getRoleName();

  boolean getDefaultRole();

  @Value("#{target.enterprise?.enterpriseId}")
  String getEnterpriseId();

  List<RoleResourcePermissionRolePermissionView> getPermissionList();

}
