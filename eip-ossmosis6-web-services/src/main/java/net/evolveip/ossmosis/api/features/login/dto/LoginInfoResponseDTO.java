package net.evolveip.ossmosis.api.features.login.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseEmptyViewResponseDTO;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LoginInfoResponseDTO {

  private LoginResponseDTO loginResponseDTO;

  private List<String> authorities;

  private List<EnterpriseEmptyViewResponseDTO> enterprises;

}
