package net.evolveip.ossmosis.api.features.phonenumber.common.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.features.group.entity.Address;
import net.evolveip.ossmosis.api.features.phonenumber.common.enums.CountryCode;
import net.evolveip.ossmosis.api.features.phonenumber.common.enums.PhoneNumberAssignmentType;
import net.evolveip.ossmosis.api.features.phonenumber.validator.annotations.ValidEnumDialingCode;
import net.evolveip.ossmosis.api.features.phonenumber.validator.annotations.ValidEnumPhoneNumberAssignmentType;
import net.evolveip.ossmosis.api.features.phonenumber.validator.annotations.ValidLong;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidInteger;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Valid
public class HttpPutRequestPhoneNumberDTO implements HttpRequestPhoneNumberDTO {

  @ValidLong
  private String phoneNumberId;
  @NotNull
  @ValidEnumDialingCode(enumClass = CountryCode.class)
  private String countryCode;
  @NotNull
  private String phoneNumber;
  private String enterpriseId;
  private String accountNumber;
  @ValidEnumDialingCode(enumClass = CountryCode.class)
  private String btnCountryCode;
  private String btnPhoneNumber;
  @NotNull
  @ValidInteger
  private String carrierId;
  @NotNull
  @ValidInteger
  private String statusId;

  @Size(max = 10, message = "PIN must not exceed 10 characters")
  @Pattern(regexp = "^[a-zA-Z0-9]*$", message = "PIN must be alphanumeric")
  private String pin;

  @NotNull
  private Address serviceAddress;

  @ValidEnumPhoneNumberAssignmentType(enumClass = PhoneNumberAssignmentType.class)
  private String assignmentType;

  private String assignmentId;
  private String assignmentName;
  private String callingLineIdName;
  private Address e911Address;



  public String getPString() {
    return String.format("%s.%s", countryCode, phoneNumber);
  }
}