package net.evolveip.ossmosis.api.features.role.dto;

import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_NULL_VALIDATION_MESSAGE;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import net.evolveip.ossmosis.api.utils.validators.annotations.ListsNotEmpty;

@Getter
@AllArgsConstructor
@ListsNotEmpty(message = "The assignLoginIds and removeLoginIds lists cannot be null or both empty at the same time")
public class RoleToLoginsRequestDTO {

  @NotNull(message = "roleId" + NOT_NULL_VALIDATION_MESSAGE)
  private Integer roleId;
  private List<Long> assignLoginIds;
  private List<Long> removeLoginIds;
}