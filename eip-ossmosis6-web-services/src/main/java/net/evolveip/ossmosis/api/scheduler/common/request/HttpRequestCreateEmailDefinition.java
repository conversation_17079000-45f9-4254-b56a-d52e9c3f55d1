package net.evolveip.ossmosis.api.scheduler.common.request;

import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_NULL_VALIDATION_MESSAGE;

import jakarta.persistence.ElementCollection;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.utils.validators.annotations.EnterpriseId;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Valid
@Builder
public class HttpRequestCreateEmailDefinition {

  @EnterpriseId
  @NotNull(message = "enterpriseId" + NOT_NULL_VALIDATION_MESSAGE)
  private String enterpriseId;

  @ElementCollection
  @NotEmpty(message = "toEmail list cannot be empty")
  private List<@Email(message = "Invalid email: [${validatedValue}]") String> toEmail;
}
