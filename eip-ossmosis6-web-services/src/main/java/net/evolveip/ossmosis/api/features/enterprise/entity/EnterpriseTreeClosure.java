package net.evolveip.ossmosis.api.features.enterprise.entity;

import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Builder
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Table(name = "enterprise_tree_closure")
public class EnterpriseTreeClosure {

  @EmbeddedId
  EnterpriseTreeClosureId enterpriseTreeClosureId;

  @MapsId("ancestorEnterpriseId")
  @JoinColumn(name = "ancestor_enterprise_id", nullable = false)
  @ManyToOne(targetEntity = Enterprise.class, fetch = FetchType.LAZY)
  private Enterprise ancestorEnterprise;

  @MapsId("descendantEnterpriseId")
  @JoinColumn(name = "descendant_enterprise_id", nullable = false)
  @ManyToOne(targetEntity = Enterprise.class, fetch = FetchType.LAZY)
  private Enterprise descendantEnterprise;

}
