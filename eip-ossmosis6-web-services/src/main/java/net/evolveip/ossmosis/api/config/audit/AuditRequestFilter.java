package net.evolveip.ossmosis.api.config.audit;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.UUID;
import lombok.NonNull;
import net.evolveip.ossmosis.api.config.auth.OssmosisJwtAuthenticationConverter;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

@Component
public class AuditRequestFilter extends OncePerRequestFilter {

  @Override
  protected void doFilterInternal(@NonNull final HttpServletRequest request,
      @NonNull final HttpServletResponse response, final FilterChain chain)
      throws ServletException, IOException {
    try {
      //important to clear the values stored in thread context
      OssmosisJwtAuthenticationConverter.clearFirstTimeInSystem();
      final String token = UUID.randomUUID().toString();
      MDC.put(AuditFilterConfig.DEFAULT_MDC_UUID_TOKEN_KEY, token);
      chain.doFilter(request, response);
    } finally {
      MDC.remove(AuditFilterConfig.DEFAULT_MDC_UUID_TOKEN_KEY);
    }
  }

  @Override
  protected boolean isAsyncDispatch(@NonNull final HttpServletRequest request) {
    return false;
  }

  @Override
  protected boolean shouldNotFilterErrorDispatch() {
    return false;
  }
}