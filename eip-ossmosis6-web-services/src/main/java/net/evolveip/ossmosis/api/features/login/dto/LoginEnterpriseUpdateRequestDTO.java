package net.evolveip.ossmosis.api.features.login.dto;

import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_BLANK_VALIDATION_MESSAGE;
import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_NULL_VALIDATION_MESSAGE;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import net.evolveip.ossmosis.api.utils.validators.annotations.EnterpriseId;

public class LoginEnterpriseUpdateRequestDTO {

  @NotNull(message = "loginId" + NOT_NULL_VALIDATION_MESSAGE)
  public Long loginId;

  @NotBlank(message = "currentEnterpriseId" + NOT_BLANK_VALIDATION_MESSAGE)
  @EnterpriseId
  public String currentEnterpriseId;

  @NotBlank(message = "newEnterpriseId" + NOT_BLANK_VALIDATION_MESSAGE)
  @EnterpriseId
  public String newEnterpriseId;
}
