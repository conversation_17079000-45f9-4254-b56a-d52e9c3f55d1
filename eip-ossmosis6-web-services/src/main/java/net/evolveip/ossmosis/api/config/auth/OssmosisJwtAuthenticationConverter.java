package net.evolveip.ossmosis.api.config.auth;

import static java.util.stream.Collectors.toSet;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseEmptyViewResponseDTO;
import net.evolveip.ossmosis.api.features.enterprise.service.EnterpriseValidationService;
import net.evolveip.ossmosis.api.features.login.dto.LoginInfoResponseDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginRequestDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginResponseDTO;
import net.evolveip.ossmosis.api.features.login.entity.view.LoginView;
import net.evolveip.ossmosis.api.features.login.mapper.LoginViewMapper;
import net.evolveip.ossmosis.api.features.login.service.LoginService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.service.AuthorizationService;
import net.evolveip.ossmosis.api.utils.service.JwtService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.security.oauth2.server.resource.authentication.JwtGrantedAuthoritiesConverter;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class OssmosisJwtAuthenticationConverter implements
    Converter<Jwt, AbstractAuthenticationToken> {

  private final JwtService jwtService;
  private final LoginService userService;
  private final EnterpriseValidationService validationService;
  private final AuthorizationService authorizationService;
  private final LoginViewMapper loginViewMapper;

  private static final String FIRST_TIME_IN_SYSTEM_TAG = "o6-first-time";

  @Override
  public AbstractAuthenticationToken convert(@NonNull Jwt source) {
    return createJwtAuthFromJwtSource(source);
  }

  public JwtAuthenticationToken createJwtAuthFromJwtSource(Jwt jwtSource) {
    try {
      String tokenValue = jwtSource.getTokenValue();

      final String userEmail = jwtService.extractUserEmail(tokenValue);
      Optional<LoginView> login = authorizationService.loadUserByUserEmail(userEmail);
      if (login.isEmpty()) {
        String enterpriseId = jwtService.extractEnterpriseId(tokenValue);
        if (StringUtils.isEmpty(enterpriseId) || !validationService.isEnterpriseIdValid(
            enterpriseId)) {
          String message =
              "Incoming enterprise is empty, null or a non-existent value " + enterpriseId;
          logger.error(message);
          return new JwtAuthenticationToken(jwtSource);
        }

        String firstName = jwtService.extractFirstName(tokenValue);
        String lastName = jwtService.extractLastName(tokenValue);
        LoginRequestDTO newLogin = LoginRequestDTO.builder()
            .loginEmail(userEmail)
            .loginNameFirst(firstName)
            .loginNameLast(lastName)
            .active(true)
            .locked(false)
            .loginGroup("")
            .loginPhoneNumber("")
            .loginPrimaryEnterpriseId(enterpriseId)
            .build();
        ApiResponse<LoginResponseDTO> response = userService.createLoginForFirstTimeOnApi(newLogin);
        if (response.isSuccessful() && response.getPayload() != null) {
          login = authorizationService.loadUserByUserEmail(userEmail);
          if (login.isPresent()) {
            MDC.put(FIRST_TIME_IN_SYSTEM_TAG, "true");
          }
        }
      }
      UserDetails userDetails = login.map(
          this::createUserFromLoginEntity
      ).orElseGet(() -> new User(
          " ",
          " ",
          true,
          true,
          true,
          true,
          new ArrayList<>())
      );

      if (login.isPresent() && jwtService.isTokenValid(tokenValue, userDetails)) {
        SecurityContext context = SecurityContextHolder.createEmptyContext();
        UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
            userDetails, null, userDetails.getAuthorities());
        context.setAuthentication(authToken);
        SecurityContextHolder.setContext(context);
        JwtAuthenticationToken jwtAuthenticationToken = new JwtAuthenticationToken(jwtSource,
            Stream.concat(new JwtGrantedAuthoritiesConverter().convert(jwtSource)
                    .stream(), userDetails.getAuthorities().stream())
                .collect(toSet()));
        List<EnterpriseEmptyViewResponseDTO> enterpriseList = this.authorizationService.getEnterpriseAccessListForCurrentUser(
            login.get());
        jwtAuthenticationToken.setDetails(
            LoginInfoResponseDTO.builder()
                .loginResponseDTO(loginViewMapper.toLoginResponseDTO(
                    login.get()))
                .authorities(
                    jwtAuthenticationToken.getAuthorities().stream()
                        .map(GrantedAuthority::getAuthority).collect(
                            Collectors.toList()))
                .enterprises(enterpriseList).build());
        return jwtAuthenticationToken;
      }
    } catch (Exception e) {
      logger.error("Error loading user details for jwt token: " + e.getMessage());
    }
    return new JwtAuthenticationToken(jwtSource);
  }

  private User createUserFromLoginEntity(LoginView value) {

    return new User(
        value.getLoginEmail(),
        "",
        value.getActive(),
        true,
        true,
        !value.getLocked(),
        this.authorizationService.getAuthorities(value));
  }

  public static boolean firstTimeInSystem() {
    return MDC.get(FIRST_TIME_IN_SYSTEM_TAG) != null && MDC.get(FIRST_TIME_IN_SYSTEM_TAG)
        .equals("true");
  }

  public static void clearFirstTimeInSystem() {
    MDC.remove(FIRST_TIME_IN_SYSTEM_TAG);
  }
}