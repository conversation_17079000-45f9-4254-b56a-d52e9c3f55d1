package net.evolveip.ossmosis.api.features.audit.service;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.audit.dto.RequestAuditLogDTO;
import net.evolveip.ossmosis.api.features.audit.entity.RequestAuditLog;
import net.evolveip.ossmosis.api.features.audit.mapper.RequestAuditLogMapper;
import net.evolveip.ossmosis.api.features.audit.repository.RequestAuditLogRepository;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.service.AuthorizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RequestAuditLogService {

  private final RequestAuditLogRepository requestAuditLogRepository;

  private final RequestAuditLogMapper requestAuditLogMapper;
  private final AuthorizationService authorizationService;

  @Autowired
  public RequestAuditLogService(RequestAuditLogRepository requestAuditLogRepository,
      RequestAuditLogMapper requestAuditLogMapper, AuthorizationService authorizationService) {
    this.requestAuditLogRepository = requestAuditLogRepository;
    this.requestAuditLogMapper = requestAuditLogMapper;
    this.authorizationService = authorizationService;
  }


  public ApiResponse<RequestAuditLog> addUpdateRequestToLog(RequestAuditLog requestAuditLog) {
    try {
      RequestAuditLog r = requestAuditLogRepository.save(requestAuditLog);
      return ApiResponse.create(r);
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }

  public ApiResponse<List<RequestAuditLogDTO>> getRequestAuditLogInfo(ZonedDateTime startDate,
      ZonedDateTime endDate, String enterpriseId, String location, String method, String email,
      String service) {
    try {
      List<String> enterpriseIdList = authorizationService.getEnterpriseAccessStringListForCurrentUser();
      if (!enterpriseId.isEmpty()) {
        enterpriseIdList = enterpriseIdList.stream().filter((entId) -> entId.equals(enterpriseId))
            .toList();
        if (enterpriseIdList.isEmpty()) {
          return ApiResponse.create(null, false,
              "Permission denied for enterprise: " + enterpriseId, HttpStatus.FORBIDDEN);
        }
      }
      return ApiResponse.create(
          requestAuditLogMapper.entitiesToDTOs(requestAuditLogRepository.findAllBySearchParameters(
              startDate,
              endDate,
              email,
              service,
              method,
              enterpriseIdList
          )));
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }

  public Page<RequestAuditLogDTO> getRequestAuditLogInfoPageable(ZonedDateTime startDate,
      ZonedDateTime endDate, String enterpriseId, String location, String method, String email,
      String service, final Pageable pageable) {
    try {
      List<String> enterpriseIdList = authorizationService.getEnterpriseAccessStringListForCurrentUser();
      if (!enterpriseId.isEmpty()) {
        enterpriseIdList = enterpriseIdList.stream().filter((entId) -> entId.equals(enterpriseId))
            .toList();
      }
      Page<RequestAuditLog> page = requestAuditLogRepository.findAllBySearchParametersPageable(startDate, endDate, email, service, method, enterpriseIdList, pageable);

      List<RequestAuditLogDTO> list = requestAuditLogMapper.entitiesToDTOs(page.getContent());

      return new PageImpl<>(list, pageable, page.getTotalElements());
    } catch (Exception e) {
      logger.error(e.getMessage());
      return new PageImpl<>(new ArrayList<>(), pageable, 0);
    }
  }
}