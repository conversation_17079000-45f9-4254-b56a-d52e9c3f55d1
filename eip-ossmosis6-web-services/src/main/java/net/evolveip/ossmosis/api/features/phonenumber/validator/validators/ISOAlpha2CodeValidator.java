package net.evolveip.ossmosis.api.features.phonenumber.validator.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Arrays;
import net.evolveip.ossmosis.api.features.phonenumber.common.enums.CountryCode;
import net.evolveip.ossmosis.api.features.phonenumber.validator.annotations.ValidEnumAlpha2Code;

public class ISOAlpha2CodeValidator implements ConstraintValidator<ValidEnumAlpha2Code, String> {

  private Class<? extends Enum<?>> enumClass;

  @Override
  public void initialize(ValidEnumAlpha2Code constraintAnnotation) {
    enumClass = constraintAnnotation.enumClass();
  }

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {
    if (value == null || enumClass == null) {
      return false;
    }

    return Arrays.stream(enumClass.getEnumConstants())
        .anyMatch(e -> {
          if (e instanceof CountryCode) {
            return ((CountryCode) e).getIsoAlpha2Code().equals(value);
          }
          return false;
        });
  }
}