package net.evolveip.ossmosis.api.features.role.mapper;

import java.time.ZonedDateTime;
import java.util.List;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.enterprise.repository.EnterpriseRepository;
import net.evolveip.ossmosis.api.features.role.dto.RoleRequestDTO;
import net.evolveip.ossmosis.api.features.role.dto.RoleResponseDTO;
import net.evolveip.ossmosis.api.features.role.entity.Role;
import org.mapstruct.AfterMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(
    componentModel = ComponentModel.SPRING,
    uses = {RoleResourcePermissionMapper.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface RoleMapper {

  @Mapping(source = "roleId", target = "roleId")
  @Mapping(source = "dateCreated", target = "dateCreated")
  @Mapping(source = "dateUpdated", target = "dateUpdated")
  @Mapping(source = "roleName", target = "roleName")
  @Mapping(source = "enterprise.enterpriseId", target = "enterpriseId")
  @Mapping(source = "permissionList", target = "permissionList")
  @Mapping(source = "defaultRole", target = "readOnly")
  RoleResponseDTO toDTO(Role role);


  Role toRole(
      RoleRequestDTO roleDTO,
      @MappingTarget Role role,
      @Context String enterpriseId,
      @Context EnterpriseRepository enterpriseRepository);

  @AfterMapping
  default void afterToRole(
      RoleRequestDTO roleDTO,
      @MappingTarget Role role,
      @Context String enterpriseId,
      @Context EnterpriseRepository enterpriseRepository) {
    Enterprise enterprise = enterpriseRepository.findEnterpriseByEnterpriseId(enterpriseId).get();
    role.setEnterprise(enterprise);
    if (role.getDateCreated() == null) {
      role.setDateCreated(ZonedDateTime.now());
    }
    if (role.getDateUpdated() == null) {
      role.setDateUpdated(ZonedDateTime.now());
    }
    if (role.getDefaultRole() == null) {
      role.setDefaultRole(false);
    }
  }

  List<RoleResponseDTO> entitiesToDTOs(List<Role> roles);
}