package net.evolveip.ossmosis.api.features.role.controller;

import jakarta.validation.Valid;
import java.util.List;
import net.evolveip.ossmosis.api.features.enterprise.constant.EnterpriseConstants;
import net.evolveip.ossmosis.api.features.login.constant.LoginConstants;
import net.evolveip.ossmosis.api.features.role.constant.RoleConstants;
import net.evolveip.ossmosis.api.features.role.dto.RoleRequestDTO;
import net.evolveip.ossmosis.api.features.role.dto.RoleResponseDTO;
import net.evolveip.ossmosis.api.features.role.service.RoleService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "{" + EnterpriseConstants.ENTERPRISE_ID_PARAM + "}/roles")
@Validated
public class RoleController {

  private final RoleService roleService;

  @Autowired
  public RoleController(RoleService roleService) {
    this.roleService = roleService;
  }

  @Secured(RoleConstants.ROLES_READ)
  @GetMapping
  public ResponseEntity<Page<RoleResponseDTO>> doGet(
      @PathVariable() String enterpriseId,
      @RequestParam(value = "filterValue", required = false, defaultValue = "") String filterValue,
      @SortDefault(sort = "id", direction = Direction.ASC) @PageableDefault(size = 15) final Pageable pageable) {
    Page<RoleResponseDTO> page = roleService.processGet(enterpriseId, filterValue, pageable);
    return ResponseEntity.ok(page);
  }

  @Secured(RoleConstants.ROLES_READ)
  @GetMapping("/list")
  public ResponseEntity<ApiResponse<List<RoleResponseDTO>>> doGet(
      @PathVariable() String enterpriseId) {
    ApiResponse<List<RoleResponseDTO>> apiResponse = ApiResponse.create(
        roleService.processGetAll(enterpriseId));
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(RoleConstants.ROLES_CREATE)
  @PostMapping
  public ResponseEntity<ApiResponse<RoleResponseDTO>> doPost(
      @PathVariable() String enterpriseId,
      @Valid @RequestBody RoleRequestDTO roleRequestDTO) {
    ApiResponse<RoleResponseDTO> apiResponse = ApiResponse.create(
        roleService.processCreate(enterpriseId, roleRequestDTO));
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(RoleConstants.ROLES_UPDATE)
  @PutMapping
  public ResponseEntity<ApiResponse<RoleResponseDTO>> doPut(
      @PathVariable() String enterpriseId,
      @Valid @RequestBody RoleRequestDTO role) {
    ApiResponse<RoleResponseDTO> apiResponse = ApiResponse.create(
        roleService.processUpdate(enterpriseId, role));
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(RoleConstants.ROLES_DELETE)
  @DeleteMapping("/{roleId}")
  public ResponseEntity<ApiResponse<Boolean>> doDelete(
      @PathVariable() String enterpriseId,
      @PathVariable() int roleId,
      @RequestParam(value = "forceDelete", defaultValue = "false") boolean forceDelete) {
    ApiResponse<Boolean> apiResponse = roleService.deleteRole(roleId, enterpriseId, forceDelete);
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }
}