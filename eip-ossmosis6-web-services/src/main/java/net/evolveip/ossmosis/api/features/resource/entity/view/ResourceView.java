package net.evolveip.ossmosis.api.features.resource.entity.view;

import java.time.ZonedDateTime;
import java.util.List;

public interface ResourceView {

  Integer getResourceId();

  ZonedDateTime getDateCreated();

  ZonedDateTime getDateUpdated();

  String getResourceName();

  List<ResourcePermissionView> getAssignablePermissions();

  default List<Integer> getAssignablePermissionIds() {
    return getAssignablePermissions().stream().map(ResourcePermissionView::getPermissionId)
        .toList();
  }

}
