package net.evolveip.ossmosis.api.features.phonenumber.validator.annotations;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import net.evolveip.ossmosis.api.features.phonenumber.validator.validators.LongValidator;

@Constraint(validatedBy = LongValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE,
    ElementType.CONSTRUCTOR, ElementType.PARAMETER, ElementType.TYPE_USE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidLong {

  String message() default "Invalid long value: [${validatedValue}]";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}