package net.evolveip.ossmosis.api.features.platform.entity;

import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import net.evolveip.ossmosis.api.features.platform.constant.PlatformCredentialConstants;

@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@DiscriminatorValue(PlatformCredentialConstants.WEBEX_CRED_TYPE)
public class WebexCredential extends PlatformCredential {

  @Column(name = "client_id", length = 255)
  private String clientId;

  @Column(name = "redirect_uri", length = 255)
  private String redirectUri;

  @Column(name = "grant_type", length = 50)
  private String grantType;
} 