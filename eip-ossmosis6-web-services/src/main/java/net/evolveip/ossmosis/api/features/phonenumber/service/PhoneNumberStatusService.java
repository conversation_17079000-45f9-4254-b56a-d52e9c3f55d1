package net.evolveip.ossmosis.api.features.phonenumber.service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.CarrierDTO;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.PhoneNumberStatusDTO;
import net.evolveip.ossmosis.api.features.phonenumber.entity.Carrier;
import net.evolveip.ossmosis.api.features.phonenumber.entity.PhoneNumberStatus;
import net.evolveip.ossmosis.api.features.phonenumber.mapper.CarrierMapper;
import net.evolveip.ossmosis.api.features.phonenumber.mapper.PhoneNumberStatusMapper;
import net.evolveip.ossmosis.api.features.phonenumber.repository.CarrierRepository;
import net.evolveip.ossmosis.api.features.phonenumber.repository.PhoneNumberStatusRepository;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PhoneNumberStatusService
    extends BasePhoneNumberStatusService {

  @Autowired
  public PhoneNumberStatusService(
      PhoneNumberStatusRepository phoneNumberStatusRepository,
      PhoneNumberStatusMapper mapper) {
    super(phoneNumberStatusRepository, mapper);
  }

  public ApiResponse<List<PhoneNumberStatusDTO>> processGet() {
    try {
      List<PhoneNumberStatus> statuses = phoneNumberStatusRepository.findAll();
      if (statuses.isEmpty()) {
        return notFoundResponse();
      }
      List<PhoneNumberStatusDTO> statusDTOS = statuses.stream().map(mapper::toPhoneNumberStatusDTO)
          .collect(Collectors.toList());
      return response(statusDTOS);
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  public ApiResponse<PhoneNumberStatusDTO> getPhoneNumberStatusByPhoneNumberStatusId(
      Integer statusId) {
    try {
      Optional<PhoneNumberStatus> optionalPhoneNumberStatus = phoneNumberStatusRepository.findById(
          statusId);
      if (optionalPhoneNumberStatus.isEmpty()) {
        return notFoundResponse(statusId);
      }
      return response(mapper.toPhoneNumberStatusDTO(optionalPhoneNumberStatus.get()));
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  public ApiResponse<PhoneNumberStatusDTO> getPhoneNumberStatusByPhoneNumberStatus(
      String statusName) {
    try {
      Optional<PhoneNumberStatus> optionalPhoneNumberStatus = phoneNumberStatusRepository.findPhoneNumberStatusByStatusName(
          statusName);
      if (optionalPhoneNumberStatus.isEmpty()) {
        return notFoundResponse(statusName);
      }
      return response(mapper.toPhoneNumberStatusDTO(optionalPhoneNumberStatus.get()));
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }
}