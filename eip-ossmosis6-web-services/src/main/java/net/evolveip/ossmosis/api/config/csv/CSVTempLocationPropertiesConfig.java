package net.evolveip.ossmosis.api.config.csv;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

/***
 * Extracts the value of the temporary location and makes it available as a bean
 * Used for generating csv files before sending emails
 */
@Data
@ConfigurationProperties(prefix = "spring.application.csv")
@Configuration
@Validated
public class CSVTempLocationPropertiesConfig {

  @NotNull
  private String tempDir;

  @Bean
  public String getPath() {
    return tempDir;
  }
}