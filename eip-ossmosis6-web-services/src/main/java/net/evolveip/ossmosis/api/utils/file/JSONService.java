package net.evolveip.ossmosis.api.utils.file;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import lombok.RequiredArgsConstructor;
import net.evolveip.ossmosis.api.config.csv.CSVTempLocationPropertiesConfig;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class JSONService<T> {

  final ObjectMapper objectMapper;

  final public T parseJSON(InputStream stream, Class<T> dataClass) throws IOException {

    try (Reader reader = new BufferedReader(new InputStreamReader(stream))) {
      return objectMapper.readValue(reader, dataClass);
    }
  }

  final public File generateJSON(T data, CSVTempLocationPropertiesConfig tempDir, String fileName) throws IOException {

    File file = new File(buildFilePath(tempDir.getPath(), fileName));
    try (BufferedWriter writer = new BufferedWriter(new FileWriter(file))) {
      objectMapper.writeValue(writer, data);
    }
    return file;
  }

  private String buildFilePath(String tempFolderPath, String fileName) {
    return tempFolderPath + File.separator + fileName;
  }
}
