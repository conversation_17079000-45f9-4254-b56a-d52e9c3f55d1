package net.evolveip.ossmosis.api.features.login.entity.view;

import java.time.ZonedDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Value;

public interface LoginView {

  Long getLoginId();

  ZonedDateTime getDateCreated();

  ZonedDateTime getDateUpdated();

  String getLoginEmail();

  String getLoginNameFirst();

  String getLoginNameLast();

  Boolean getActive();

  Boolean getLocked();

  String getLoginGroup();

  String getLoginPhoneNumber();

  @Value("#{target.loginPrimaryEnterprise.enterpriseId}")
  String getLoginPrimaryEnterpriseId();

  List<LoginRoleView> getRoles();

  List<LoginResourcePermissionLoginPermissionView> getUserPermissionList();

}
