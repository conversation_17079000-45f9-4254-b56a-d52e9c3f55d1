package net.evolveip.ossmosis.api.scheduler.mapper;

import java.time.LocalTime;
import java.time.ZoneId;
import net.evolveip.ossmosis.api.scheduler.common.dto.SimpleScheduleDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateSimpleScheduleDefinition;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

@Mapper(
    componentModel = ComponentModel.SPRING,
    injectionStrategy = InjectionStrategy.CONSTRUCTOR
)
public interface MapperSimpleScheduleDefinition {

  MapperSimpleScheduleDefinition INSTANCE = Mappers.getMapper(MapperSimpleScheduleDefinition.class);

  @Named("convertToZoneId")
  static ZoneId toZoneId(String value) {
    return ZoneId.of(value);
  }

  @Named("convertToLocalTime")
  static LocalTime toLocalTime(String value) {
    return LocalTime.parse(value);
  }

  @Named("convertToInteger")
  static Integer toInteger(String value) {
    return Integer.parseInt(value);
  }

  @Mapping(source = "userTimeZone", target = "userTimeZone", qualifiedByName = "convertToZoneId")
  @Mapping(source = "reportRunTime", target = "reportRunTime", qualifiedByName = "convertToLocalTime")
  @Mapping(source = "frequency", target = "frequency", qualifiedByName = "convertToInteger")
  @Mapping(source = "frequencyUnits", target = "frequencyUnits")
  @Mapping(source = "selectedWeekRunDays", target = "selectedWeekRunDays")
  @Mapping(source = "selectedMonthRunDays", target = "selectedMonthRunDays")
  SimpleScheduleDefinitionDTO toDTO(
      HttpRequestCreateSimpleScheduleDefinition entryHttpCreateRequest);
}