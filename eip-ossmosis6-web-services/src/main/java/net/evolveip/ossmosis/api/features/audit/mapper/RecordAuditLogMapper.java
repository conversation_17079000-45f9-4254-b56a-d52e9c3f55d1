package net.evolveip.ossmosis.api.features.audit.mapper;

import java.util.List;
import net.evolveip.ossmosis.api.features.audit.dto.RecordAuditLogDTO;
import net.evolveip.ossmosis.api.features.audit.entity.RecordAuditLog;
import net.evolveip.ossmosis.api.features.login.mapper.LoginMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;

@Mapper(componentModel = ComponentModel.SPRING, uses = {LoginMapper.class})
public interface RecordAuditLogMapper {


  @Mapping(source = "recordAuditLogId", target = "recordAuditLogId")
  @Mapping(source = "recordId", target = "recordId")
  @Mapping(source = "oldRecordId", target = "oldRecordId")
  @Mapping(source = "op", target = "op")
  @Mapping(source = "timestamp", target = "timestamp")
  @Mapping(source = "tableName", target = "tableName")
  @Mapping(source = "record", target = "record")
  @Mapping(source = "oldRecord", target = "oldRecord")
  RecordAuditLogDTO map(RecordAuditLog recordAuditLog);

  List<RecordAuditLogDTO> entitiesToDTOs(List<RecordAuditLog> recordAuditLogs);

}
