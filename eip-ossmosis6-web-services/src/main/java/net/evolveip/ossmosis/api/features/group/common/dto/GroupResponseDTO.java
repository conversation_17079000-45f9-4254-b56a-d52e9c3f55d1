package net.evolveip.ossmosis.api.features.group.common.dto;

import java.time.ZonedDateTime;
import jdk.jfr.Label;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.features.group.entity.Address;
import net.evolveip.ossmosis.api.utils.file.annotations.Exportable;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Exportable
public class GroupResponseDTO {

  @Label("Group ID")
  private String groupId;

  @Label("Date Created")
  private ZonedDateTime dateCreated;

  @Label("Date Updated")
  private ZonedDateTime dateUpdated;

  @Label("Enterprise ID")
  private String enterpriseId;

  @Label("Group Name")
  private String groupName;

  @Label("Billing Address")
  private Address billingAddress;

  @Label("Timezone")
  private String timezone;

  private String contactName;
  private String contactPhone;
  private String contactEmail;
  private String callingLineIdName;
  private String callingLineIdNumber;
  private String extensionLength;
  private String localCarrierId;
  private String externalCallsPolicy;
  private String enterpriseCallsPolicy;
  private String groupCallsPolicy;
}
