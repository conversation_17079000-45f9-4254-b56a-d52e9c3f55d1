package net.evolveip.ossmosis.api.features.role.service;

import jakarta.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.permission.entity.Permission;
import net.evolveip.ossmosis.api.features.permission.service.PermissionService;
import net.evolveip.ossmosis.api.features.resource.entity.Resource;
import net.evolveip.ossmosis.api.features.resource.service.ResourceService;
import net.evolveip.ossmosis.api.features.role.constant.RoleConstants;
import net.evolveip.ossmosis.api.features.role.dto.RoleRequestDTO;
import net.evolveip.ossmosis.api.features.role.dto.RoleResourcePermissionRequestDTO;
import net.evolveip.ossmosis.api.features.role.entity.Role;
import net.evolveip.ossmosis.api.features.role.entity.RoleResourcePermission;
import net.evolveip.ossmosis.api.features.role.entity.RoleResourcePermissionId;
import net.evolveip.ossmosis.api.features.role.repository.RoleResourcePermissionRepository;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RoleResourcePermissionService {

  private final RoleResourcePermissionRepository roleResourcePermissionRepository;
  private final ResourceService resourceService;
  private final PermissionService permissionService;

  public RoleResourcePermissionService(
      RoleResourcePermissionRepository roleResourcePermissionRepository,
      ResourceService resourceService,
      PermissionService permissionService) {
    this.roleResourcePermissionRepository = roleResourcePermissionRepository;
    this.resourceService = resourceService;
    this.permissionService = permissionService;
  }

  public void createDefaultRoleResourcePermissions(List<Role> roles, List<Resource> resources,
      List<Permission> permissions) {
    List<RoleResourcePermission> roleResourcePermissions = new ArrayList<>();
    if (roleResourcePermissionRepository.count() == 0) {
      for (Role role : roles) {
        Map<String, ArrayList<String>> roleMap = RoleConstants.DEFAULT_ROLE_RESOURCE_PERMISSION_MAP.get(
            role.getRoleName());
        if (roleMap != null) {
          for (Resource resource : resources) {
            for (Permission permission : permissions) {
              if (roleMap.get(resource.getResourceName()) != null && roleMap.get(
                  resource.getResourceName()).contains(permission.getPermissionName())) {
                roleResourcePermissions.add(
                    RoleResourcePermission
                        .builder()
                        .roleResourcePermissionId(
                            new RoleResourcePermissionId(
                                role.getRoleId(),
                                resource.getResourceId(),
                                permission.getPermissionId())
                        )
                        .role(role)
                        .resource(resource)
                        .permission(permission).build());
              }
            }
          }
        }
      }
      this.roleResourcePermissionRepository.saveAll(roleResourcePermissions);
      logger.info("Created default resourceRolePermissions");
    }
  }

  @Transactional
  public void createRoleResourcePermissionsByRole(Role role, RoleRequestDTO roleRequestDTO) {
    List<RoleResourcePermission> candidateRoleResourcePermissions = populateRoleResourcePermissionsList(
        role, roleRequestDTO);
    roleResourcePermissionRepository.saveAllAndFlush(candidateRoleResourcePermissions);
  }

  @Transactional
  public void updateRoleResourcePermissionsByRole(Role role, RoleRequestDTO roleRequestDTO) {
    List<RoleResourcePermission> candidateRoleResourcePermissions = populateRoleResourcePermissionsList(
        role, roleRequestDTO);
    List<RoleResourcePermission> existingRoleResourcePermissions = getResourcePermissionsByRole(
        role);

    // the resource permissions update has to happen only if the existing
    // resource permissions list isn't empty, and the candidate resource permissions list
    // is different from the existing resource permissions list
    if (!existingRoleResourcePermissions.isEmpty()) {
      if (!areRoleResourcePermissionIdsSetsEqual(candidateRoleResourcePermissions,
          existingRoleResourcePermissions)) {
        roleResourcePermissionRepository.deleteAllByRoleRoleId(role.getRoleId());
        roleResourcePermissionRepository.saveAllAndFlush(candidateRoleResourcePermissions);
      }
    } else {
      roleResourcePermissionRepository.saveAllAndFlush(candidateRoleResourcePermissions);
    }
  }

  /***
   * Extract all permissions and resources associated with a role
   * @param role the roleId to filter by.
   * @return a list of resourceIds and permissionIds
   */
  public List<RoleResourcePermission> getResourcePermissionsByRole(Role role) {
    return getResourcePermissionsByRoleId(role.getRoleId());
  }

  /***
   * Extract all permissions and resources associated with a role
   * @param roleId the roleId to filter by.
   * @return a list of resourceIds and permissionIds
   */
  public List<RoleResourcePermission> getResourcePermissionsByRoleId(Integer roleId) {
    return roleResourcePermissionRepository.findAllByRoleId(roleId);
  }

  private List<RoleResourcePermission> populateRoleResourcePermissionsList(Role role,
      RoleRequestDTO roleRequestDTO) {
    List<RoleResourcePermission> roleResourcePermissionList = new ArrayList<>();
    for (RoleResourcePermissionRequestDTO roleResourcePermissionRequestDTO : roleRequestDTO.getPermissionList()) {

      int resourceId = roleResourcePermissionRequestDTO.getResourceId();
      Resource resource = resourceService.getResourceByResourceId(resourceId);

      int permissionId = roleResourcePermissionRequestDTO.getPermissionId();
      Permission permission = permissionService.getPermissionByPermissionId(permissionId);

      RoleResourcePermission roleResourcePermission = buildRoleResourcePermission(role, resource,
          permission);
      roleResourcePermissionList.add(roleResourcePermission);
    }
    return roleResourcePermissionList;
  }

  private RoleResourcePermission buildRoleResourcePermission(Role role, Resource resource,
      Permission permission) {
    return RoleResourcePermission.builder().roleResourcePermissionId(
            new RoleResourcePermissionId(role.getRoleId(), resource.getResourceId(),
                permission.getPermissionId())).role(role).resource(resource).permission(permission)
        .build();
  }

  public boolean areRoleResourcePermissionIdsSetsEqual(List<RoleResourcePermission> candidate,
      List<RoleResourcePermission> existing) {
    Set<RoleResourcePermissionId> candidateRoleResourcePermissionIds = candidate.stream()
        .map(RoleResourcePermission::getRoleResourcePermissionId).collect(Collectors.toSet());
    Set<RoleResourcePermissionId> existingRoleResourcePermissionIds = existing.stream()
        .map(RoleResourcePermission::getRoleResourcePermissionId).collect(Collectors.toSet());
    return candidateRoleResourcePermissionIds.equals(existingRoleResourcePermissionIds);
  }
}