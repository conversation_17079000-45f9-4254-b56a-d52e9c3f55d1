package net.evolveip.ossmosis.api.utils.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import net.evolveip.ossmosis.api.features.role.dto.RoleToLoginsRequestDTO;
import net.evolveip.ossmosis.api.utils.validators.annotations.ListsNotEmpty;

public class ListsNotEmptyValidator implements
    ConstraintValidator<ListsNotEmpty, RoleToLoginsRequestDTO> {

  @Override
  public boolean isValid(RoleToLoginsRequestDTO dto, ConstraintValidatorContext context) {
    if (dto.getAssignLoginIds() == null || dto.getRemoveLoginIds() == null) {
      return false;
    } else {
      boolean assignIsEmpty = dto.getAssignLoginIds().isEmpty();
      boolean removeIsEmpty = dto.getRemoveLoginIds().isEmpty();
      return !(assignIsEmpty && removeIsEmpty);
    }
  }
}