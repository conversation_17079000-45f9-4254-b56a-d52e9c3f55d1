package net.evolveip.ossmosis.api.config.auth;

import java.util.Base64;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtException;
import org.springframework.stereotype.Component;

@Component
public class DynamicJwtDecoder implements JwtDecoder {

  private final DynamicJwtDecoderResolver resolver;

  public DynamicJwtDecoder(DynamicJwtDecoderResolver resolver) {
    this.resolver = resolver;
  }

  @Override
  public Jwt decode(String token) throws JwtException {
    Jwt unverifiedJwt = parseToken(token);
    JwtDecoder appropriateDecoder = resolver.resolve(unverifiedJwt);
    return appropriateDecoder.decode(token);
  }

  private Jwt parseToken(String token) {
    String[] parts = token.split("\\.");
    if (parts.length != 3) {
      throw new JwtException("Invalid token format");
    }
    String payload = new String(Base64.getUrlDecoder().decode(parts[1]));

    return Jwt.withTokenValue(token)
        .header("alg", "RS256")
        .claim("tenant", extractTenant(payload))
        .claim("app", extractAppName(payload))
        .build();
  }

  private String extractTenant(String payload) {
    int tenantStart = payload.indexOf("\"tenant\":\"") + 10;
    int tenantEnd = payload.indexOf("\"", tenantStart);
    return payload.substring(tenantStart, tenantEnd);
  }

  private String extractAppName(String payload) {
    int appNameStart = payload.indexOf("\"app\":\"") + 7;
    int appNameEnd = payload.indexOf("\"", appNameStart);
    return payload.substring(appNameStart, appNameEnd);
  }
}