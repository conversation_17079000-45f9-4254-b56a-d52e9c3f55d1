package net.evolveip.ossmosis.api.features.permission.constant;

import java.util.ArrayList;
import java.util.List;

public final class PermissionConstants {

  public static final String PERMISSION_NAME_CREATE = "create";
  public static final String PERMISSION_NAME_UPDATE = "update";
  public static final String PERMISSION_NAME_READ = "read";
  public static final String PERMISSION_NAME_DELETE = "delete";
  public static final String PERMISSION_NAME_FULL_ACCESS = "full access";

  public static final ArrayList<String> permissionNameList = new ArrayList<>(
      List.of(PERMISSION_NAME_CREATE,
          PERMISSION_NAME_UPDATE, PERMISSION_NAME_READ, PERMISSION_NAME_DELETE,
          PERMISSION_NAME_FULL_ACCESS));

  public static final ArrayList<String> crudPermissionNameList = new ArrayList<>(
      List.of(PERMISSION_NAME_CREATE,
          PERMISSION_NAME_UPDATE, PERMISSION_NAME_READ, PERMISSION_NAME_DELETE));

}
