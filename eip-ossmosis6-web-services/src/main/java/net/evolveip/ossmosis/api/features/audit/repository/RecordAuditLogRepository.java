package net.evolveip.ossmosis.api.features.audit.repository;


import java.time.ZonedDateTime;
import java.util.List;
import net.evolveip.ossmosis.api.features.audit.entity.RecordAuditLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface RecordAuditLogRepository extends JpaRepository<RecordAuditLog, Long> {


  List<RecordAuditLog> findByTimestampIsAfterAndTimestampBeforeAndTableNameAndRecordIdAndOp(
      ZonedDateTime timestamp,
      ZonedDateTime timestamp2, String tableName, String recordId, String op);


}