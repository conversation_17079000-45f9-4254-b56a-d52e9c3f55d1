package net.evolveip.ossmosis.api.features.role.service;


import jakarta.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.enterprise.service.RootEnterpriseService;
import net.evolveip.ossmosis.api.features.role.constant.RoleConstants;
import net.evolveip.ossmosis.api.features.role.entity.Role;
import net.evolveip.ossmosis.api.features.role.repository.RoleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DefaultRoleService {

  private final RoleRepository roleRepository;
  private final RootEnterpriseService rootEnterpriseService;

  @Autowired
  public DefaultRoleService(RoleRepository roleRepository,
      RootEnterpriseService rootEnterpriseService) {
    this.roleRepository = roleRepository;
    this.rootEnterpriseService = rootEnterpriseService;
  }

  @Transactional
  public void createDefaultRoles() {
    List<Role> existingRoles = getDefaultRoles();
    if (existingRoles.size() != RoleConstants.roleNameList.size()) {

      Enterprise rootEnterprise = rootEnterpriseService.getRootEnterprise();
      List<Role> roleList = new ArrayList<>();
      for (String roleName : RoleConstants.roleNameList) {
        if (existingRoles.stream()
            .noneMatch(role -> role.getRoleName().equals(roleName))) {
          Role role = Role.builder()
              .defaultRole(true)
              .enterprise(rootEnterprise)
              .roleName(roleName).build();
          roleList.add(role);
        }
      }
      if (!roleList.isEmpty()) {
        existingRoles.addAll(this.roleRepository.saveAll(roleList));
        logger.info("Created default roles");
      }
    }
  }

  public List<Role> getDefaultRoles() {
    return this.roleRepository.findByRoleNameIn(RoleConstants.roleNameList);
  }
}