package net.evolveip.ossmosis.api.utils.validators.annotations;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import net.evolveip.ossmosis.api.utils.validators.GroupIdValidator;

@Documented
@Constraint(validatedBy = GroupIdValidator.class)
@Target({ElementType.PARAMETER, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface GroupId {
  String message() default "Invalid group ID";
  Class<?>[] groups() default {};
  Class<? extends Payload>[] payload() default {};
}
