package net.evolveip.ossmosis.api.features.platform.entity;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import net.evolveip.ossmosis.api.features.common.BaseEntity;

@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "platform", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"platform_name"})
})
public class Platform extends BaseEntity {

  @Id
  @Column(name = "platform_id", columnDefinition = "SMALLINT", nullable = false)
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer platformId;

  @Column(name = "platform_name", length = 100, nullable = false)
  @Basic(optional = false)
  private String platformName;

}