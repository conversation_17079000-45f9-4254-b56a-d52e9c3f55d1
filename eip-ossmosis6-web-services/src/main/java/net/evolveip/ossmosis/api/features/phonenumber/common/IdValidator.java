package net.evolveip.ossmosis.api.features.phonenumber.common;

import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.data.jpa.repository.JpaRepository;


/**
 * Interface for extracting a set of invalid IDs from a repository.
 *
 * @param <T>  the entity type
 * @param <ID> the type of the entity's identifier
 */
public interface IdValidator<T, ID> {

  default Set<ID> getInvalidEntries(Collection<ID> candidateIds, JpaRepository<T, ID> repository) {
    return candidateIds
        .stream()
        .filter(entryId -> repository.findById(entryId).isEmpty())
        .collect(Collectors.toUnmodifiableSet());
  }
}