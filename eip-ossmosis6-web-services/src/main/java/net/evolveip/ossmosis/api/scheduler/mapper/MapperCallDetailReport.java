package net.evolveip.ossmosis.api.scheduler.mapper;

import java.time.temporal.ChronoUnit;
import net.evolveip.ossmosis.api.scheduler.common.dto.CallDetailReportDTO;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateCallDetailReport;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = ComponentModel.SPRING, injectionStrategy = InjectionStrategy.CONSTRUCTOR, uses = ChronoUnit.class)
public interface MapperCallDetailReport {

  MapperCallDetailReport INSTANCE = Mappers.getMapper(MapperCallDetailReport.class);

  CallDetailReportDTO toDTO(HttpRequestCreateCallDetailReport entry);
}