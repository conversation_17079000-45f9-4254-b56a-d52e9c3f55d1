package net.evolveip.ossmosis.api.config;

import net.evolveip.ossmosis.api.config.auth.OssmosisEnterprisePathParamInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

  private final OssmosisEnterprisePathParamInterceptor ossmosisEnterprisePathParamInterceptor;

  @Autowired
  public WebConfig(OssmosisEnterprisePathParamInterceptor ossmosisEnterprisePathParamInterceptor) {
    this.ossmosisEnterprisePathParamInterceptor = ossmosisEnterprisePathParamInterceptor;
  }

  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    registry.addInterceptor(ossmosisEnterprisePathParamInterceptor).addPathPatterns("/**");
  }
}