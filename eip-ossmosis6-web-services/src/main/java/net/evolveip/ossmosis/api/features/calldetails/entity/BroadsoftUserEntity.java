package net.evolveip.ossmosis.api.features.calldetails.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Table(name = "BROADSOFTUSERDIMENSION")
@NoArgsConstructor
@AllArgsConstructor
public class BroadsoftUserEntity {

  @Id
  @Column(name = "BUD_BROADSOFTFQUSERID")
  private String id;

  @Column(name = "BUD_USERCATEGORY")
  private String userType;

  @Column(name = "BUD_BROADSOFTUSERDISPLAYNAME")
  private String userDisplayName;
}
