package net.evolveip.ossmosis.api.features.group.common.enums;

import lombok.Getter;

@Getter
public enum ExternalCalls {
  UserPhone("userPhone", "Use user phone number"),
  ConfigurableCLID("configurableCLID", "Use configurable CLID"),
  GroupDepartmentPhoneNumber("groupDepartmentPhoneNumber", "Use group/department phone number"),
  Default(ConfigurableCLID.enumId, ConfigurableCLID.enumLabel);

  private final String enumId;
  private final String enumLabel;

  ExternalCalls(String enumId, String enumLabel) {
    this.enumId = enumId;
    this.enumLabel = enumLabel;
  }
}