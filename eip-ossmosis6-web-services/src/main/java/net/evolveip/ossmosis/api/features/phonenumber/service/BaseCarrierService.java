package net.evolveip.ossmosis.api.features.phonenumber.service;

import java.util.Optional;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.core.BaseGenericService;
import net.evolveip.ossmosis.api.features.phonenumber.common.IdValidator;
import net.evolveip.ossmosis.api.features.phonenumber.entity.Carrier;
import net.evolveip.ossmosis.api.features.phonenumber.mapper.CarrierMapper;
import net.evolveip.ossmosis.api.features.phonenumber.repository.CarrierRepository;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;

@Slf4j
public abstract class BaseCarrierService
    extends BaseGenericService
    implements IdValidator<Carrier, Integer> {

  protected final CarrierRepository carrierRepository;
  protected final CarrierMapper mapper;

  @Autowired
  protected BaseCarrierService(CarrierRepository carrierRepository, CarrierMapper mapper) {
    this.carrierRepository = carrierRepository;
    this.mapper = mapper;
  }

  public Boolean carrierExists(Integer carrierId) {
    Optional<Carrier> carrier = carrierRepository.findById(carrierId);
    return carrier.isPresent();
  }

  public Set<Integer> getInvalidIds(Set<Integer> candidateIds) {
    return getInvalidEntries(candidateIds, carrierRepository);
  }

  public Boolean carrierExists(String carrierName) {
    Optional<Carrier> carrier = carrierRepository.findCarrierByCarrierName(carrierName);
    return carrier.isPresent();
  }

  protected <T> ApiResponse<T> notFoundResponse() {
    String message = "No carriers found";
    logger.info(message);
    return genericEmptyResponse(message);
  }

  protected <T> ApiResponse<T> notFoundResponse(Integer carrierId) {
    String message = String.format("Carrier ID not found: %s ", carrierId);
    logger.info(message);
    return genericEmptyResponse(message);
  }

  protected <T> ApiResponse<T> idsNotFoundResponse(Set<Integer> carrierIds) {
    String message = String.format(
        "Some of the carrier IDs not found: %s", String.join(", ", carrierIds.toString()));
    logger.info(message);
    return genericEmptyResponse(message);
  }

  protected <T> ApiResponse<T> notFoundResponse(String carrierName) {
    String message = String.format("Carrier name not found: %s ", carrierName);
    logger.info(message);
    return genericEmptyResponse(message);
  }

  protected <T> ApiResponse<T> alreadyExistsResponse(String carrierName) {
    String message = "Carrier name already exists: " + carrierName;
    logger.info(message);
    return genericBadResponse(message, HttpStatus.CONFLICT);
  }
}