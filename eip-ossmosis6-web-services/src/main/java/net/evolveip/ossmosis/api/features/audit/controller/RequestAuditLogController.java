package net.evolveip.ossmosis.api.features.audit.controller;

import java.time.ZonedDateTime;
import java.util.List;
import net.evolveip.ossmosis.api.features.audit.constants.AuditLogConstants;
import net.evolveip.ossmosis.api.features.audit.dto.RequestAuditLogDTO;
import net.evolveip.ossmosis.api.features.audit.service.RequestAuditLogService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.validators.annotations.EnterpriseId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/request-audit-logs")
public class RequestAuditLogController {


  private final RequestAuditLogService requestAuditLogService;

  @Autowired
  public RequestAuditLogController(RequestAuditLogService requestAuditLogService) {
    this.requestAuditLogService = requestAuditLogService;
  }

  @Secured(AuditLogConstants.AUDIT_LOGS_READ)
  @GetMapping
  public ResponseEntity<Page<RequestAuditLogDTO>> getRequestAuditLogInfoPage(
      @RequestParam("startDate") ZonedDateTime startDate,
      @RequestParam("endDate") ZonedDateTime endDate,
      @RequestParam(value = "enterpriseId", required = false, defaultValue = "") String enterpriseId,
      @RequestParam(value = "location", required = false, defaultValue = "") String location,
      @RequestParam(value = "method", required = false, defaultValue = "") String method,
      @RequestParam(value = "email", required = false, defaultValue = "") String email,
      @RequestParam(value = "service", required = false, defaultValue = "") String service,
      @SortDefault(sort = "requestId", direction = Direction.ASC) @PageableDefault(size = 15) final
      Pageable pageable
  ) {
    Page<RequestAuditLogDTO> page = this.requestAuditLogService.getRequestAuditLogInfoPageable(startDate, endDate, enterpriseId, location, method, email, service, pageable);
    return ResponseEntity.ok(page);
  }

  @Secured(AuditLogConstants.AUDIT_LOGS_READ)
  @GetMapping("/list")
  public ResponseEntity<ApiResponse<List<RequestAuditLogDTO>>> getRequestAuditLogInfo(
      @RequestParam("startDate") ZonedDateTime startDate,
      @RequestParam("endDate") ZonedDateTime endDate,
      @RequestParam(value = "enterpriseId", required = false, defaultValue = "") String enterpriseId,
      @RequestParam(value = "location", required = false, defaultValue = "") String location,
      @RequestParam(value = "method", required = false, defaultValue = "") String method,
      @RequestParam(value = "email", required = false, defaultValue = "") String email,
      @RequestParam(value = "service", required = false, defaultValue = "") String service
  ) {
    ApiResponse<List<RequestAuditLogDTO>> apiResponse = this.requestAuditLogService.getRequestAuditLogInfo(
        startDate, endDate, enterpriseId,
        location, method, email, service);
    return new ResponseEntity<>(apiResponse,
        HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

}
