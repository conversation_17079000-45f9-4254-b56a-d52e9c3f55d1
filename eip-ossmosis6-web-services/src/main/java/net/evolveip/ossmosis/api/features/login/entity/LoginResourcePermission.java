package net.evolveip.ossmosis.api.features.login.entity;

import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import net.evolveip.ossmosis.api.features.permission.entity.Permission;
import net.evolveip.ossmosis.api.features.resource.entity.Resource;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@Table(name = "login_resource_permission")
public class LoginResourcePermission {

  @EmbeddedId
  LoginResourcePermissionId loginResourcePermissionId;

  @MapsId("loginId")
  @JoinColumn(name = "login_id", nullable = false)
  @ManyToOne(targetEntity = Login.class, fetch = FetchType.LAZY)
  private Login login;

  @MapsId("resourceId")
  @JoinColumn(name = "resource_id", nullable = false)
  @ManyToOne(targetEntity = Resource.class, fetch = FetchType.LAZY)
  private Resource resource;


  @MapsId("permissionId")
  @JoinColumn(name = "permission_id", nullable = false)
  @ManyToOne(targetEntity = Permission.class, fetch = FetchType.LAZY)
  private Permission permission;


}