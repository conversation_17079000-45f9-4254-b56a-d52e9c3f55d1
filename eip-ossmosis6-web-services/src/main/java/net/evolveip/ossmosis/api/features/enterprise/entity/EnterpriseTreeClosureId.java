package net.evolveip.ossmosis.api.features.enterprise.entity;

import jakarta.persistence.Embeddable;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Embeddable
public class EnterpriseTreeClosureId implements Serializable {

  private String ancestorEnterpriseId;

  private String descendantEnterpriseId;
}
