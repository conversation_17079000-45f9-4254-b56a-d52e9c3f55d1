package net.evolveip.ossmosis.api.scheduler.common.dto;

import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.scheduler.common.enums.WeekDays;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SimpleScheduleDefinitionDTO {

  private final String scheduleType = "SimpleSchedule";
  private ZoneId userTimeZone;
  private LocalTime reportRunTime;
  private Integer frequency;
  private ChronoUnit frequencyUnits;
  private Set<WeekDays> selectedWeekRunDays;
  private Set<Integer> selectedMonthRunDays;
}