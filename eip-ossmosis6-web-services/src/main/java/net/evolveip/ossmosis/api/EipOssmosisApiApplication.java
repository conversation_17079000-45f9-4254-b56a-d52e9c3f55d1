package net.evolveip.ossmosis.api;

import java.util.TimeZone;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = {
    "net.evolveip.ossmosis.api",
    "net.evolveip.ossmosis.provisioning"
})
public class EipOssmosisApiApplication {

  public static void main(String[] args) {
    TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
    SpringApplication.run(EipOssmosisApiApplication.class, args);
  }
}