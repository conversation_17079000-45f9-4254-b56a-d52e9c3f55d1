package net.evolveip.ossmosis.api.features.login.dto;

import java.time.ZonedDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Builder
public class LoginRoleResponseDTO {

  private ZonedDateTime dateCreated;
  private ZonedDateTime dateUpdated;
  private Long loginId;
  private Integer roleId;
}
