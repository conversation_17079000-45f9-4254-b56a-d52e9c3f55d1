package net.evolveip.ossmosis.api.scheduler.validator.annotations;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import net.evolveip.ossmosis.api.scheduler.validator.validators.ScheduleDefinitionValidator;

@Constraint(validatedBy = ScheduleDefinitionValidator.class)
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidScheduleDefinition {

  String message() default "selectedWeekRunDays and selectedMonthRunDays cannot be both populated at the same time";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}