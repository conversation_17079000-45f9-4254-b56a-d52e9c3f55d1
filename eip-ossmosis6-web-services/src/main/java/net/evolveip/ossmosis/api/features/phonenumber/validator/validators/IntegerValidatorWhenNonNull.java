package net.evolveip.ossmosis.api.features.phonenumber.validator.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import net.evolveip.ossmosis.api.features.phonenumber.validator.annotations.ValidIntegerWhenNonNull;

public class IntegerValidatorWhenNonNull implements
    ConstraintValidator<ValidIntegerWhenNonNull, String> {

  @Override
  public void initialize(ValidIntegerWhenNonNull constraintAnnotation) {
  }

  @Override
  public boolean isValid(final String value, final ConstraintValidatorContext context) {
    if (value == null) {
      return true;
    }
    try {
      Integer.parseInt(value);
      return true;
    } catch (Exception e) {
      return false;
    }
  }
}