package net.evolveip.ossmosis.api.features.login.repository;

import java.util.List;
import net.evolveip.ossmosis.api.features.login.entity.LoginResourcePermission;
import net.evolveip.ossmosis.api.features.login.entity.LoginResourcePermissionId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface LoginResourcePermissionRepository extends
    JpaRepository<LoginResourcePermission, LoginResourcePermissionId> {

  @Query(nativeQuery = true, value = "SELECT * from partnerprovider.fn_logins_merge_permissions(:loginId, :jsonText)")
  Boolean mergePermissionSet(Long loginId, String jsonText);


  @Query(value = "SELECT lrp from LoginResourcePermission lrp join fetch lrp.resource join fetch lrp.permission join fetch lrp.login where lrp.login.loginId = :loginId")
  List<LoginResourcePermission> findAllByLoginId(long loginId);
}
