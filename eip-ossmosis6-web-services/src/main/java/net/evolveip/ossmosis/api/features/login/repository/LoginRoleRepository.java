package net.evolveip.ossmosis.api.features.login.repository;


import java.util.List;
import java.util.Optional;
import net.evolveip.ossmosis.api.features.login.entity.LoginRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface LoginRoleRepository extends JpaRepository<LoginRole, Long> {

  Optional<List<LoginRole>> findLoginRolesByLoginLoginId(Long loginId);

  @Query(value = "SELECT lr FROM LoginRole lr WHERE lr.login.loginId = :loginId AND lr.role.roleId = :roleId")
  Optional<LoginRole> findByLoginIdAndRoleId(@Param("loginId") Long loginId,
      @Param("roleId") Integer roleId);

  @Query(value = "SELECT lr.login.loginId FROM LoginRole lr LEFT JOIN lr.login WHERE lr.role.roleId = :roleId AND lr.login.loginPrimaryEnterprise.enterpriseId = :enterpriseId")
  List<Long> findAssignedLogins(Integer roleId, String enterpriseId);

  @Query(nativeQuery = true, value = "SELECT * from partnerprovider.fn_logins_merge_roles(:loginId, :jsonText)")
  Boolean mergeRoleSet(Long loginId, String jsonText);

  int deleteAllByRoleRoleId(int roleId);
}