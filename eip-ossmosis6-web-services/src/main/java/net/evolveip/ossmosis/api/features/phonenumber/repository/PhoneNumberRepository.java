package net.evolveip.ossmosis.api.features.phonenumber.repository;

import java.util.Optional;
import net.evolveip.ossmosis.api.features.phonenumber.entity.PhoneNumber;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PhoneNumberRepository extends JpaRepository<PhoneNumber, Long> {

  @Query(value = "SELECT pn FROM PhoneNumber pn " +
      "INNER JOIN pn.carrier c " +
      "INNER JOIN pn.status s " +
      "WHERE pn.enterprise.enterpriseId = :enterpriseId " +
      "AND (lower(pn.countryCode) like %:filterValue% " +
      "OR lower(pn.phoneNumber) like %:filterValue% " +
      "OR lower(pn.accountNumber) like %:filterValue% " +
      "OR lower(pn.serviceAddress.addressLine1) like %:filterValue% " +
      "OR lower(pn.serviceAddress.addressLine2) like %:filterValue% " +
      "OR lower(pn.serviceAddress.city) like %:filterValue% " +
      "OR lower(pn.serviceAddress.stateOrProvince) like %:filterValue% " +
      "OR lower(pn.serviceAddress.zipOrPostalCode) like %:filterValue% " +
      "OR lower(pn.serviceAddress.country) like %:filterValue% " +
      "OR lower(pn.assignmentType) like %:filterValue% " +
      "OR lower(pn.assignmentId) like %:filterValue% " +
      "OR lower(pn.assignmentName) like %:filterValue% " +
      "OR lower(pn.callingLineIdName) like %:filterValue% " +
      "OR lower(pn.e911Address.addressLine1) like %:filterValue% " +
      "OR lower(pn.e911Address.addressLine2) like %:filterValue% " +
      "OR lower(pn.e911Address.city) like %:filterValue% " +
      "OR lower(pn.e911Address.stateOrProvince) like %:filterValue% " +
      "OR lower(pn.e911Address.zipOrPostalCode) like %:filterValue% " +
      "OR lower(pn.e911Address.country) like %:filterValue% " +
      "OR lower(c.carrierName) like %:filterValue% " +
      "OR lower(s.statusName) like %:filterValue%  )")
  Page<PhoneNumber> findAllByEnterpriseIdAndFilterValue(
      @Param("enterpriseId") String enterpriseId,
      @Param("filterValue") String filterValue,
      Pageable pageable);

  Optional<PhoneNumber> findByEnterpriseEnterpriseIdAndPhoneNumberId(String enterpriseId,
      Long phoneNumberId);

  Optional<PhoneNumber> findPhoneNumberByEnterpriseEnterpriseIdAndAssignmentTypeAndAssignmentId(String enterpriseId, String assignmentType, String assignmentId);

  Optional<PhoneNumber> findByCountryCodeAndPhoneNumber(String countryCode, String phoneNumber);
}