package net.evolveip.ossmosis.api.scheduler.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Setter
@Getter
public class TriggerDTO {

  private String jobGroup;
  private String jobName;
  private String startTime;
  private String endTime;
  private String previousFireTime;
  private String nextFireTime;
  private String triggerState;
}