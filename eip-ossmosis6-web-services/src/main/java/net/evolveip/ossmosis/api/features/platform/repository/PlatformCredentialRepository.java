package net.evolveip.ossmosis.api.features.platform.repository;

import java.util.List;
import java.util.Optional;
import net.evolveip.ossmosis.api.features.platform.entity.PlatformCredential;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PlatformCredentialRepository extends JpaRepository<PlatformCredential, Integer> {

  List<PlatformCredential> findAllByPlatformPlatformId(Integer platformId);

  @Query("SELECT p FROM #{#entityName} p WHERE p.platform.platformId = :platformId AND TYPE(p) = :type")
  <T extends PlatformCredential> Optional<T> findByPlatformPlatformIdAndType(@Param("platformId") Integer platformId, @Param("type") Class<? extends PlatformCredential> type);
}