package net.evolveip.ossmosis.api.features.enterprise.service;

import io.temporal.api.enums.v1.WorkflowIdReusePolicy;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowExecutionAlreadyStarted;
import io.temporal.client.WorkflowOptions;
import io.temporal.common.SearchAttributes;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.common.ValidationContext;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseRequestDTO;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseResponseDTO;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.enterprise.mapper.EnterpriseMapper;
import net.evolveip.ossmosis.api.features.enterprise.repository.EnterpriseRepository;
import net.evolveip.ossmosis.api.features.enterprise.util.EnterpriseSearchAttributeUtil;
import net.evolveip.ossmosis.api.features.platform.service.PlatformService;
import net.evolveip.ossmosis.api.utils.exceptions.NotFoundException;
import net.evolveip.ossmosis.api.utils.service.AuthorizationService;
import net.evolveip.ossmosis.provisioning.constants.QueueConstants;
import net.evolveip.ossmosis.provisioning.features.enterprise.workflows.EnterpriseCreateWorkflow;
import net.evolveip.ossmosis.provisioning.features.enterprise.workflows.EnterpriseDeleteWorkflow;
import net.evolveip.ossmosis.provisioning.features.enterprise.workflows.EnterpriseModifyWorkflow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class EnterpriseService {

  private final EnterpriseRepository enterpriseRepository;
  private final EnterpriseMapper mapper;
  private final PlatformService platformService;
  private final AuthorizationService authorizationService;
  private final EnterpriseValidationService validationService;
  private final WorkflowClient workflowClient;
  private final EnterpriseSearchAttributeUtil enterpriseSearchAttributeUtil;

  @Autowired
  public EnterpriseService(EnterpriseRepository enterpriseRepository,
      EnterpriseMapper mapper,
      PlatformService platformService,
      AuthorizationService authorizationService,
      EnterpriseValidationService validationService,
      WorkflowClient workflowClient,
      EnterpriseSearchAttributeUtil enterpriseSearchAttributeUtil) {
    this.enterpriseRepository = enterpriseRepository;
    this.mapper = mapper;
    this.platformService = platformService;
    this.authorizationService = authorizationService;
    this.validationService = validationService;
    this.workflowClient = workflowClient;
    this.enterpriseSearchAttributeUtil = enterpriseSearchAttributeUtil;
  }


  /***
   * Retrieves all enterprises from the repository/database.
   * @return a Page list with all the found enterprises
   */
  public Page<EnterpriseResponseDTO> processGet(String filterValue, final Pageable pageable) {
    try {
      String filterValueLowerCase = filterValue.toLowerCase();
      List<String> enterpriseIds = authorizationService.getEnterpriseAccessStringListForCurrentUser();
      long totalElements = enterpriseRepository.findFilteredCountAllEnterprisesForUser(
          enterpriseIds, filterValueLowerCase);
      Page<Enterprise> page = enterpriseRepository.findAllByFilteredPage(enterpriseIds,
          filterValueLowerCase, pageable);

      return new PageImpl<>(
          page.getContent().stream().map(mapper::toDTO).collect(Collectors.toList()), pageable,
          totalElements);
    } catch (Exception e) {
      logger.error(e.getMessage());
      return new PageImpl<>(new ArrayList<>(), pageable, 0);
    }
  }

  public List<EnterpriseResponseDTO> processGetList() {
    List<Enterprise> enterpriseList = enterpriseRepository.findAllEnterprisesForUser(
        authorizationService.getEnterpriseAccessStringListForCurrentUser());
    if (enterpriseList.isEmpty()) {
      String message = "No enterprises found";
      throw new NotFoundException(message);
    }
    return enterpriseList.stream().map(mapper::toDTO).collect(Collectors.toList());
  }

  /***
   * Retrieves an enterprise by the enterprise id
   * @param enterpriseId an enterprise id to be looked up
   * @return a EnterpriseResponseDTO found in the repository
   */
  public EnterpriseResponseDTO processGetById(String enterpriseId) {
    Enterprise enterprise = enterpriseRepository.findEnterpriseByEnterpriseId(
        enterpriseId).orElseThrow(
        () -> new NotFoundException("enterpriseId [" + enterpriseId + "] does not exists"));
    return mapper.toDTO(enterprise);
  }

  /***
   * Creates an enterpriseDTO based on the incoming DTO
   * @param enterpriseRequestDTO the incoming DTO representing the enterpriseDTO to be created
   * @return an EnterpriseResponseDTO nested object with the enterprise and platform details
   */
  @Transactional
  public void processCreate(
      EnterpriseRequestDTO enterpriseRequestDTO) {
    // Check if the incoming platform exists:
    platformService.validatePlatformId(enterpriseRequestDTO.getPlatformId());

    // validate request:
    validationService.validate(enterpriseRequestDTO, ValidationContext.CREATE);

    SearchAttributes searchAttributes = enterpriseSearchAttributeUtil.buildEnterpriseSearchAttributes(
        enterpriseRequestDTO.getEnterpriseId(), enterpriseRequestDTO.getParentEnterpriseId());

    WorkflowOptions options = WorkflowOptions.newBuilder()
        .setTaskQueue(QueueConstants.PROVISIONING_TASK_QUEUE)
        .setWorkflowId(QueueConstants.PROVISIONING_ENTERPRISE_CREATE_ID + enterpriseRequestDTO.getEnterpriseId())
        .setWorkflowIdReusePolicy(WorkflowIdReusePolicy.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE)
        .setWorkflowRunTimeout(Duration.ofMinutes(10))
        .setTypedSearchAttributes(searchAttributes)
        .build();

    EnterpriseCreateWorkflow workflow = workflowClient.newWorkflowStub(EnterpriseCreateWorkflow.class, options);

    try {
      WorkflowClient.start(workflow::execute, enterpriseRequestDTO);
    } catch (WorkflowExecutionAlreadyStarted e) {
      throw new IllegalStateException("Enterprise Create Request already exists");
    }
  }

  /***
   * Updates an existing enterprise with the incoming candidate enterprise request DTO
   * @param enterpriseRequestDTO the candidate request dto
   * @return an enterprise response dto containing the updated enterprise entity
   */
  @Transactional
  public void processUpdate(
      EnterpriseRequestDTO enterpriseRequestDTO) {
    // Check if the platformId, attached to the incoming dto, exists:
    platformService.validatePlatformId(enterpriseRequestDTO.getPlatformId());

    // validate request:
    validationService.validate(enterpriseRequestDTO, ValidationContext.UPDATE);

    SearchAttributes searchAttributes = enterpriseSearchAttributeUtil.buildEnterpriseSearchAttributes(
        enterpriseRequestDTO.getEnterpriseId(), null);

    WorkflowOptions options = WorkflowOptions.newBuilder()
        .setTaskQueue(QueueConstants.PROVISIONING_TASK_QUEUE)
        .setWorkflowId(QueueConstants.PROVISIONING_ENTERPRISE_MODIFY_ID + enterpriseRequestDTO.getEnterpriseId())
        .setWorkflowIdReusePolicy(WorkflowIdReusePolicy.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE)
        .setWorkflowRunTimeout(Duration.ofMinutes(10))
        .setTypedSearchAttributes(searchAttributes)
        .build();

    EnterpriseModifyWorkflow workflow = workflowClient.newWorkflowStub(EnterpriseModifyWorkflow.class, options);

    try {
      WorkflowClient.start(workflow::execute, enterpriseRequestDTO);
    } catch (WorkflowExecutionAlreadyStarted e) {
      throw new IllegalStateException("Enterprise Modify Request already exists");
    }
  }

  /***
   * Deletes an enterprise by its id from the repository
   * @param enterpriseId the id of the enterprise that needs to be deleted
   * @return an ApiResponse with a success (true) or failure (false) status
   */
  public void processDelete(String enterpriseId) {
    validationService.validateEnterpriseId(enterpriseId);
    validationService.checkIfCurrentUserHasAccessToEnterpriseId(enterpriseId);
    try {

      if (enterpriseRepository.existsById(enterpriseId) && !enterpriseRepository.doesEnterpriseHaveChildren(enterpriseId)) {

        SearchAttributes searchAttributes = enterpriseSearchAttributeUtil.buildEnterpriseSearchAttributes(enterpriseId, null);

        WorkflowOptions options = WorkflowOptions.newBuilder()
            .setTaskQueue(QueueConstants.PROVISIONING_TASK_QUEUE)
            .setWorkflowId(QueueConstants.PROVISIONING_ENTERPRISE_DELETE_ID + enterpriseId)
            .setWorkflowIdReusePolicy(WorkflowIdReusePolicy.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE)
            .setWorkflowRunTimeout(Duration.ofMinutes(10))
            .setTypedSearchAttributes(searchAttributes)
            .build();

        EnterpriseDeleteWorkflow workflow = workflowClient.newWorkflowStub(EnterpriseDeleteWorkflow.class, options);

        try {
          WorkflowClient.start(workflow::execute, enterpriseId);
        } catch (WorkflowExecutionAlreadyStarted e) {
          throw new IllegalStateException("Enterprise Delete Request already exists");
        }
      }
    } catch (DataIntegrityViolationException e) {
      String message =
          "enterpriseId [" + enterpriseId + "] in use in a child table, cannot delete";
      throw new AccessDeniedException(message);
    }
  }

  public Enterprise createEnterprise(EnterpriseRequestDTO enterpriseRequestDTO) {
    Optional<Enterprise> existingEnterprise = enterpriseRepository.findEnterpriseByEnterpriseId(enterpriseRequestDTO.getEnterpriseId());

    if (existingEnterprise.isPresent()) {
        return existingEnterprise.get();
    }

    Enterprise candidateEnterprise = mapper.toEntity(enterpriseRequestDTO);
    Enterprise persistedEnterprise = enterpriseRepository.saveAndFlush(candidateEnterprise);

    enterpriseRepository.updateTreeClosure(persistedEnterprise.getEnterpriseId(),
        persistedEnterprise.getParentEnterprise().getEnterpriseId());

    return persistedEnterprise;
  }

  public Enterprise modifyEnterprise(EnterpriseRequestDTO enterpriseRequestDTO) {
    Enterprise existingEnterprise = enterpriseRepository.findEnterpriseByEnterpriseId(enterpriseRequestDTO.getEnterpriseId()).orElseThrow(() -> new NotFoundException("Enterprise not found"));
    String existingParentEnterpriseId = existingEnterprise.getParentEnterprise().getEnterpriseId();

    if (isUnchanged(existingEnterprise, enterpriseRequestDTO)) {
      return existingEnterprise;
    }

    Enterprise candidateEnterprise = mapper.toEntity(enterpriseRequestDTO);
    Enterprise persistedEnterprise = enterpriseRepository.saveAndFlush(candidateEnterprise);

    String persistedEnterpriseId = persistedEnterprise.getEnterpriseId();
    String persistedParentEnterpriseId = persistedEnterprise.getParentEnterprise().getEnterpriseId();

    if (!existingParentEnterpriseId.equals(persistedEnterpriseId)) {
      enterpriseRepository.updateTreeClosure(persistedEnterpriseId, persistedParentEnterpriseId);
    }

    return persistedEnterprise;
  }

  @Transactional
  public void deleteEnterprise(String enterpriseId) {
    if (!enterpriseRepository.existsById(enterpriseId)) return;
    enterpriseRepository.deleteNonParentEnterpriseTree(enterpriseId);
    enterpriseRepository.deleteById(enterpriseId);
  }

  private boolean isUnchanged(Enterprise existingEntity, EnterpriseRequestDTO newEnterprise) {
    return Objects.equals(existingEntity.getEnterpriseId(), newEnterprise.getEnterpriseId()) &&
        Objects.equals(existingEntity.getBillingId(), newEnterprise.getBillingId()) &&
        Objects.equals(existingEntity.getEnterpriseName(), newEnterprise.getEnterpriseName()) &&
        Objects.equals(existingEntity.getAccountCountry(), newEnterprise.getAccountCountry()) &&
        Objects.equals(existingEntity.getPlatform().getPlatformId(), newEnterprise.getPlatformId()) &&
        Objects.equals(existingEntity.getParentEnterprise().getEnterpriseId(), newEnterprise.getParentEnterpriseId());
  }
}