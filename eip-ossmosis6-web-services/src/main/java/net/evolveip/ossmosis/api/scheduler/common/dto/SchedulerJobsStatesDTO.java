package net.evolveip.ossmosis.api.scheduler.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SchedulerJobsStatesDTO {

  private Integer normal;
  private Integer paused;
  private Integer complete;
  private Integer blocked;
  private Integer error;
  private Integer none;
}