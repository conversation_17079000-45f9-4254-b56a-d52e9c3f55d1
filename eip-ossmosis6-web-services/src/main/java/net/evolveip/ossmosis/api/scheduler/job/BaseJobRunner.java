package net.evolveip.ossmosis.api.scheduler.job;

import java.time.ZonedDateTime;
import java.util.Random;
import java.util.TimeZone;
import net.evolveip.ossmosis.api.scheduler.common.TimeUtils;
import net.evolveip.ossmosis.api.scheduler.common.dto.BaseReportRunnerDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.CallDetailReportRunnerDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.ZonedDateTimeRangeDTO;
import org.quartz.JobKey;

public interface BaseJobRunner extends TimeUtils {

  default String buildEmailSubject(String reportTitle,
      ZonedDateTimeRangeDTO zonedDateTimeRangeDTO, TimeZone userTimeZone) {
    return String.format(
        reportTitle + ". From: " + convertToTimeZone(zonedDateTimeRangeDTO.getStartRangeDateTime(),
            userTimeZone) + " To: "
            + convertToTimeZone(zonedDateTimeRangeDTO.getEndRangeDateTime(), userTimeZone));

  }

  // builds a properly formatted email body, aligned around central vertical line:
  default String buildEmailBody(BaseReportRunnerDTO dto,
      ZonedDateTimeRangeDTO rangeDTO,
      JobKey jobKey,
      ZonedDateTime nowDateTime,
      TimeZone userTimeZone) {
    StringBuilder sb = new StringBuilder();

    sb.append("<html>\n"
        + "<head>\n"
        + "\t<style>\n"
        + "\t\t.email-container {\n"
        + "\t\t\tfont-family: Arial, sans-serif;\n"
        + "\t\t\tcolor: #333;\n"
        + "\t\t\tpadding: 20px;\n"
        + "\t\t}\n"
        + "\t\t.email-content {\n"
        + "\t\t\tbackground-color: #F9F9F9;\n"
        + "\t\t\tborder-radius: 10px;\n"
        + "\t\t\tpadding: 20px;\n"
        + "\t\t\tborder: 1px solid #ddd;\n"
        + "\t\t\tfont-size: 18px;\n"
        + "\t\t}\n"
        + "\t\t.header2 {\n"
        + "\t\t\tcolor: #0451A1;\n"
        + "\t\t}\n"
        + "\t\t.header3 {\n"
        + "\t\t\tcolor: #F47920;\n"
        + "\t\t}\n"
        + "\t</style>\n"
        + "</head>"
        + "<body>\n"
        + "\t<div class=\"email-container\">\n"
        + "\t\t<div class=\"email-content\">"
        + "<h2 class=\"header2\">Hello!</h2>");
    sb.append("<p>This is the <strong>").append(dto.getReportTitle())
        .append("</strong> report that you requested, generated on <strong>")
        .append(convertToEmailDateTime(nowDateTime, userTimeZone)).append("</strong>.</p>");
    sb.append("<p>Please do not respond to this email, as this inbox is not monitored.</p>");
    sb.append("<br><p>Thank you</p></div></div></body></html>");

    return sb.toString();
  }


  @SuppressWarnings("SameParameterValue")
  default String randString(int length) {
    Random rng = new Random();
    String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890";
    char[] text = new char[length];
    for (int i = 0; i < length; i++) {
      text[i] = characters.charAt(rng.nextInt(characters.length()));
    }
    return new String(text);
  }

  default String fileNameBuilder(String enterpriseId, String now, String rand, String pattern,
      String extension) {
    return enterpriseId + "." + pattern + "." + now + "." + rand + "." + extension;
  }

  String buildEmailBody(CallDetailReportRunnerDTO runnerDTO,
      ZonedDateTimeRangeDTO zonedDateTimeRangeDTO, JobKey jobKey, ZonedDateTime now,
      TimeZone userTimeZone);
}