package net.evolveip.ossmosis.api.features.phonenumber.common.dto;

import jakarta.validation.constraints.NotNull;
import java.time.ZonedDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Setter
@Getter
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PhoneNumberStatusDTO {

  @EqualsAndHashCode.Exclude
  private Integer statusId;
  @EqualsAndHashCode.Exclude
  private ZonedDateTime dateCreated;
  @EqualsAndHashCode.Exclude
  private ZonedDateTime dateUpdated;
  @NotNull
  private String statusName;
}