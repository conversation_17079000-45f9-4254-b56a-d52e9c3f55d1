package net.evolveip.ossmosis.api.features.permission.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import java.time.ZonedDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.ColumnDefault;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Table(name = "permission", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"permission_name"})
})
public class Permission {

  @Id
  @Column(name = "permission_id", columnDefinition = "SMALLINT")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer permissionId;

  @Column(name = "date_created", nullable = false, updatable = false)
  private ZonedDateTime dateCreated;

  @Column(name = "date_updated", nullable = false)
  @ColumnDefault("CURRENT_TIMESTAMP")
  private ZonedDateTime dateUpdated;

  @Column(name = "permission_name", length = 100, nullable = false)
  private String permissionName;

}
