package net.evolveip.ossmosis.api.features.platform.entity;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorColumn;
import jakarta.persistence.DiscriminatorType;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Inheritance;
import jakarta.persistence.InheritanceType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import java.io.Serial;
import java.io.Serializable;
import java.time.ZonedDateTime;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import net.evolveip.ossmosis.api.features.platform.constant.PlatformConstants;
import net.evolveip.ossmosis.api.features.platform.constant.PlatformCredentialConstants;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.ColumnTransformer;

@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Table(name = "platform_credential", uniqueConstraints = @UniqueConstraint(columnNames = {
    "platform_id", "credential_type"}))
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "credential_type", discriminatorType = DiscriminatorType.STRING)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "credential_type")
@JsonSubTypes({
    @JsonSubTypes.Type(value = BroadsoftASCredential.class, name = PlatformCredentialConstants.BROADSOFT_AS_CRED_TYPE),
    @JsonSubTypes.Type(value = BroadsoftNSCredential.class, name = PlatformCredentialConstants.BROADSOFT_NS_CRED_TYPE),
    @JsonSubTypes.Type(value = RedskyCredential.class, name = PlatformCredentialConstants.REDSKY_CRED_TYPE),
    @JsonSubTypes.Type(value = DubberCredential.class, name = PlatformCredentialConstants.DUBBER_CRED_TYPE),
    @JsonSubTypes.Type(value = WebexCredential.class, name = PlatformCredentialConstants.WEBEX_CRED_TYPE)
})
public class PlatformCredential implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "platform_credential_id", nullable = false)
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer platformCredentialId;

  @Column(name = "hostname", length = PlatformConstants.PLATFORM_CRED_MAX_LEN_100, nullable = false)
  private String hostname;

  @Column(name = "username", length = PlatformConstants.PLATFORM_CRED_MAX_LEN_100, nullable = false)
  private String username;

  @Setter(AccessLevel.NONE)
  @Column(name = "password", columnDefinition = "bytea")
  @ColumnTransformer(
      read = "pgp_sym_decrypt(password, current_setting('var.encryption_key'), 'compress-algo=1, cipher-algo=aes256')",
      write = "pgp_sym_encrypt(?, current_setting('var.encryption_key'), 'compress-algo=1, cipher-algo=aes256')")
  private String password;

  @Column(name = "port")
  private Integer port;

  @Column(name = "timeout", nullable = false)
  private Integer timeout;

  @Column(name = "requests_per_second")
  private Integer requestsPerSecond;

  @Column(name = "enabled")
  private Boolean enabled;

  @JoinColumn(name = "platform_id", nullable = false)
  @ManyToOne(targetEntity = Platform.class, fetch = FetchType.EAGER)
  private Platform platform;

  @Column(name = "date_created", nullable = false, updatable = false)
  @ColumnDefault("CURRENT_TIMESTAMP")
  private ZonedDateTime dateCreated;

  @Column(name = "date_updated", nullable = false)
  @ColumnDefault("CURRENT_TIMESTAMP")
  private ZonedDateTime dateUpdated;
}
