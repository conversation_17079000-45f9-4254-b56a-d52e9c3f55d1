package net.evolveip.ossmosis.api.features.calldetails.repository;

import java.time.ZonedDateTime;
import java.util.List;
import net.evolveip.ossmosis.api.features.calldetails.dto.CallDetailDTO;
import net.evolveip.ossmosis.api.features.calldetails.dto.CallDetailSummaryDTO;
import org.springframework.data.domain.Pageable;

public interface CustomCallDetailRepository {

  List<CallDetailDTO> getCustomCallDetails(String answerStatus, String externalOnly, String direction, String callType, String userType, ZonedDateTime startDate, ZonedDateTime endDate, List<String> enterpriseIds, List<String> userNumber, List<String> groupNumber, String groupBy);

  long getCustomCallDetailsCount(String answerStatus, String externalOnly, String direction, String callType, String userType, ZonedDateTime startDate, ZonedDateTime endDate, List<String> enterpriseIds, List<String> userNumbers, List<String> groupNumbers, String groupBy);

  long getCustomCallDetailsPageableCount(String answerStatus, String externalOnly, String direction, String callType, String userType, ZonedDateTime startDate, ZonedDateTime endDate, List<String> enterpriseIds, List<String> userNumbers, List<String> groupNumbers, String groupBy);

  List<CallDetailSummaryDTO> getCustomCallDetailsPageList(String answerStatus, String externalOnly, String direction, String callType, String userType, ZonedDateTime startDate, ZonedDateTime endDate, List<String> enterpriseIds, List<String> userNumbers, List<String> groupNumbers, String groupBy, Pageable pageable);

  List<CallDetailDTO> getCustomCallDetailsPageable(String answerStatus, String externalOnly, String direction, String callType, String userType, ZonedDateTime startDate, ZonedDateTime endDate, List<String> enterpriseIds, List<String> userNumbers, List<String> groupNumbers, String groupBy, Pageable pageable);
}
