package net.evolveip.ossmosis.api.features.platform.dto;

import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_BLANK_VALIDATION_MESSAGE;
import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_EMPTY_VALIDATION_MESSAGE;
import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_NULL_VALIDATION_MESSAGE;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Builder
public class PlatformRequestDTO {

  private Integer platformId;
  @NotBlank(message = "platformName" + NOT_BLANK_VALIDATION_MESSAGE)
  @NotNull(message = "platformName" + NOT_NULL_VALIDATION_MESSAGE)
  @NotEmpty(message = "platformName" + NOT_EMPTY_VALIDATION_MESSAGE)
  private String platformName;
}