package net.evolveip.ossmosis.api.scheduler.common.request;

import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_BLANK_VALIDATION_MESSAGE;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.temporal.ChronoUnit;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.scheduler.common.enums.AnswerStatus;
import net.evolveip.ossmosis.api.scheduler.common.enums.CallType;
import net.evolveip.ossmosis.api.scheduler.common.enums.Direction;
import net.evolveip.ossmosis.api.scheduler.common.enums.ExternalOnly;
import net.evolveip.ossmosis.api.scheduler.common.enums.GroupBy;
import net.evolveip.ossmosis.api.scheduler.common.enums.UserType;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidEnum;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidInteger;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidLocalTime;

@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Valid
public class HttpRequestCreateCallDetailReport {

  @Valid
  @NotBlank(message = "reportTitle" + NOT_BLANK_VALIDATION_MESSAGE)
  private String reportTitle;

  @NotBlank(message = "answerStatus" + NOT_BLANK_VALIDATION_MESSAGE)
  @ValidEnum(enumClass = AnswerStatus.class)
  private String answerStatus;

  @NotBlank(message = "externalOnly" + NOT_BLANK_VALIDATION_MESSAGE)
  @ValidEnum(enumClass = ExternalOnly.class)
  private String externalOnly;

  @NotBlank(message = "direction" + NOT_BLANK_VALIDATION_MESSAGE)
  @ValidEnum(enumClass = Direction.class)
  private String direction;

  @NotBlank(message = "callType" + NOT_BLANK_VALIDATION_MESSAGE)
  @ValidEnum(enumClass = CallType.class)
  private String callType;

  @NotBlank(message = "userType" + NOT_BLANK_VALIDATION_MESSAGE)
  @ValidEnum(enumClass = UserType.class)
  private String userType;

  @NotNull(message = "Cannot be null")
  private List<String> userNumbers;

  @NotNull(message = "Cannot be null")
  private List<String> groupNumbers;

  @NotBlank(message = "groupBy" + NOT_BLANK_VALIDATION_MESSAGE)
  @ValidEnum(enumClass = GroupBy.class)
  private String groupBy;

  @ValidInteger
  @Min(value = 0, message = "The value must be positive")
  private String dataWindowOffset;

  @ValidEnum(enumClass = ChronoUnit.class)
  private String dataWindowOffsetUnits;

  @ValidLocalTime
  private String dataWindowEndTime;

  @ValidInteger
  @Min(value = 0, message = "The value must be positive")
  private String dataWindow;

  @ValidEnum(enumClass = ChronoUnit.class)
  private String dataWindowUnits;
}