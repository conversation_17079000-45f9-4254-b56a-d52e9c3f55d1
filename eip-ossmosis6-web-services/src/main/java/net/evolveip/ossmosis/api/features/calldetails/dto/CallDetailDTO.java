package net.evolveip.ossmosis.api.features.calldetails.dto;

import java.time.ZonedDateTime;
import jdk.jfr.Label;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.utils.file.annotations.Exportable;

@Data
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Exportable
public class CallDetailDTO {

  @Label("Id")
  private Long id;

  @Label("User Number")
  private String userNumber;

  @Label("Calling Number")
  private String callingNumber;

  @Label("Called Number")
  private String calledNumber;

  @Label("Call Duration")
  private Long callDuration;

  @Label("Call Direction")
  private String callDirection;

  @Label("To City")
  private String toCity;

  @Label("To State")
  private String toState;

  @Label("From City")
  private String fromCity;

  @Label("From State")
  private String fromState;

  @Label("Group Number")
  private String groupNumber;

  @Label("Group Id")
  private String groupId;

  @Label("Call Category")
  private String callCategory;

  @Label("Call Type")
  private String callType;

  @Label("User Name")
  private String userDisplayName;

  @Label("User Type")
  private String userType;

  @Label("Start Date Time")
  private ZonedDateTime startDateTime;
}
