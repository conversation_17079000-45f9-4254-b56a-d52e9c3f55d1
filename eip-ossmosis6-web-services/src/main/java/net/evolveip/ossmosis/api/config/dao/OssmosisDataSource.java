package net.evolveip.ossmosis.api.config.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import javax.sql.DataSource;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.config.audit.AuditFilterConfig;
import org.postgresql.util.PSQLException;
import org.springframework.jdbc.datasource.DelegatingDataSource;

@Slf4j
public class OssmosisDataSource extends DelegatingDataSource {

  public OssmosisDataSource(DataSource delegate) {
    super(delegate);
  }

  @Override
  @NonNull
  public Connection getConnection() throws SQLException {
    Connection connection = super.getConnection();
    setupAuditRequestId(connection);
    return connection;
  }

  @Override
  @NonNull
  public Connection getConnection(@NonNull String username, @NonNull String password)
      throws SQLException {
    Connection connection = super.getConnection(username, password);
    setupAuditRequestId(connection);
    return connection;
  }

  private void setupAuditRequestId(Connection connection) throws SQLException {
    //calls audit.fn_audit_request_setup.sql function in the db
    //this is how auditing is tracked in the db
    CallableStatement cs = null;
    try {
      cs = connection.prepareCall(
          "{call audit.fn_audit_request_setup(?)}");
      cs.setObject(1, AuditFilterConfig.getCurrentRequestId());
      cs.execute();
    } catch (PSQLException e) {
      //if fn_audit_request_setup does not exist let the exception go.
      if (!e.getMessage().contains("does not exist")) {
        logger.error(e.getMessage());
        throw e;
      }
    } finally {
      if (cs != null) {
        cs.close();
      }
    }
  }
}