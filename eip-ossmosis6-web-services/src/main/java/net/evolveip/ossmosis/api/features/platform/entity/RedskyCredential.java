package net.evolveip.ossmosis.api.features.platform.entity;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import net.evolveip.ossmosis.api.features.platform.constant.PlatformCredentialConstants;

@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
@DiscriminatorValue(PlatformCredentialConstants.REDSKY_CRED_TYPE)
public class RedskyCredential extends PlatformCredential {
    // No additional fields needed, class required to add correct type value into db when saving cred type
} 