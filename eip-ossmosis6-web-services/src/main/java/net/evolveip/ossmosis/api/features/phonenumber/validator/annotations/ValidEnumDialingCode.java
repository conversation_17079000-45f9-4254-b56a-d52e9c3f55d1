package net.evolveip.ossmosis.api.features.phonenumber.validator.annotations;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import net.evolveip.ossmosis.api.features.phonenumber.validator.validators.DialingCodeValidator;

@Constraint(validatedBy = DialingCodeValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidEnumDialingCode {

  String message() default "Invalid dialing code value: [${validatedValue}]";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};

  Class<? extends Enum<?>> enumClass();
}