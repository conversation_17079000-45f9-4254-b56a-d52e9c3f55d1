package net.evolveip.ossmosis.api.scheduler.common.enums;

import lombok.Getter;

@Getter
public enum UserType {
  All("all"),
  AudioConference("audioconference"),
  AutoAttendant("autoattendant"),
  CallCenter("callcenter"),
  ExtensionUser("extentionuser"),
  GroupCalling("groupcalling"),
  HuntGroup("huntgroup"),
  User("user"),
  VoiceMail("voicemail");

  private final String value;

  UserType(String value) {
    this.value = value;
  }
}