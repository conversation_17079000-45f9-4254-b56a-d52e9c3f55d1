package net.evolveip.ossmosis.api.scheduler.service.email;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.io.File;
import java.util.List;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
@Component
@Getter
@Slf4j
public class EmailService implements BaseEmailService<MimeMessageHelper> {

  private final JavaMailSender mailSender;

  @Autowired
  public EmailService(JavaMailSender mailSender) {
    this.mailSender = mailSender;
  }

  // list of toEmails, array with variable number of attachment files:
  public void sendEmail(String fromEmail, List<String> toEmail, String subject,
      String bodyText, File... attachmentFiles) throws MessagingException {
    MimeMessage message = getMimeMessage();
    MimeMessageHelper helper = createMimeMessageHelper(message);
    createHeader(helper, fromEmail, toEmails(toEmail), subject, bodyText, false);
    addAttachment(helper, attachmentFiles);
    getMailSender().send(message);
  }

  // list of toEmails, list of attachmentFiles
  public void sendEmail(String fromEmail, List<String> toEmail, String subject, String bodyText,
      List<File> attachmentFiles) throws MessagingException {
    MimeMessage message = getMimeMessage();
    MimeMessageHelper helper = createMimeMessageHelper(message);
    createHeader(helper, fromEmail, toEmails(toEmail), subject, bodyText, false);
    addAttachment(helper, attachmentFiles);
    getMailSender().send(message);
  }

  // list of toEmails, a single File file
  public void sendEmail(String fromEmail, List<String> toEmail, String subject, String bodyText,
      File attachmentFile) throws MessagingException {
    MimeMessage message = getMimeMessage();
    MimeMessageHelper helper = createMimeMessageHelper(message);
    createHeader(helper, fromEmail, toEmails(toEmail), subject, bodyText, false);
    addAttachment(helper, attachmentFile);
    getMailSender().send(message);
  }

  // a single String toEmail, no attachments
  public void sendEmail(String fromEmail, String toEmail, String subject, String bodyText)
      throws MessagingException {
    sendEmail(fromEmail, toEmails(toEmail), subject, bodyText);
  }

  public void sendEmail(String fromEmail, List<String> toEmail, String subject, String bodyText)
      throws MessagingException {
    MimeMessage message = getMimeMessage();
    MimeMessageHelper helper = createMimeMessageHelper(message);
    createHeader(helper, fromEmail, toEmails(toEmail), subject, bodyText, false);
    getMailSender().send(message);
  }

  public void sendEmail(String fromEmail, String[] toEmail, String subject, String bodyText)
      throws MessagingException {
    MimeMessage message = getMimeMessage();
    MimeMessageHelper helper = createMimeMessageHelper(message);
    createHeader(helper, fromEmail, toEmail, subject, bodyText, false);
    getMailSender().send(message);
  }

  public void sendHtmlEmailWithAttachment(String fromEmail, List<String> toEmail, String subject,
      String bodyText, File... attachmentFiles) throws MessagingException {
    MimeMessage message = getMimeMessage();
    MimeMessageHelper helper = createMimeMessageHelper(message);
    createHeader(helper, fromEmail, toEmails(toEmail), subject, bodyText, true);
    addAttachment(helper, attachmentFiles);
    getMailSender().send(message);
  }

  public void sendHtmlEmail(String fromEmail, List<String> toEmail, String subject, String bodyText) throws MessagingException {
    MimeMessage message = getMimeMessage();
    MimeMessageHelper helper = createMimeMessageHelper(message);
    createHeader(helper, fromEmail, toEmails(toEmail), subject, bodyText, true);
    getMailSender().send(message);
  }

  private MimeMessage getMimeMessage() {
    return getMailSender().createMimeMessage();
  }
}