package net.evolveip.ossmosis.api.scheduler.validator.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Set;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateSimpleScheduleDefinition;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidScheduleDefinition;

public class ScheduleDefinitionValidator implements
    ConstraintValidator<ValidScheduleDefinition, HttpRequestCreateSimpleScheduleDefinition> {

  @Override
  public void initialize(ValidScheduleDefinition constraintAnnotation) {
  }

  @Override
  public boolean isValid(HttpRequestCreateSimpleScheduleDefinition value,
      ConstraintValidatorContext context) {
    if (value == null) {
      return false;
    }

    Set<String> selectedWeekRunDays = value.getSelectedWeekRunDays();
    Set<String> selectedMonthRunDays = value.getSelectedMonthRunDays();

    if (selectedWeekRunDays != null && !selectedWeekRunDays.isEmpty()
        && selectedMonthRunDays != null && !selectedMonthRunDays.isEmpty()) {
      return false;
    }
    return true;
  }
}