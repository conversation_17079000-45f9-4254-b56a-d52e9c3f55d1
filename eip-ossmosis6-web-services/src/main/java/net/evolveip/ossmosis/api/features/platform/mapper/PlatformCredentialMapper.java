package net.evolveip.ossmosis.api.features.platform.mapper;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import net.evolveip.broadsoft.oci.entities.connection.BroadsoftOCIConnectionData;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;
import net.evolveip.ossmosis.api.features.platform.constant.PlatformCredentialConstants;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformCredentialRequestDTO;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformCredentialResponseDTO;
import net.evolveip.ossmosis.api.features.platform.entity.BroadsoftASCredential;
import net.evolveip.ossmosis.api.features.platform.entity.BroadsoftNSCredential;
import net.evolveip.ossmosis.api.features.platform.entity.DubberCredential;
import net.evolveip.ossmosis.api.features.platform.entity.PlatformCredential;
import net.evolveip.ossmosis.api.features.platform.entity.RedskyCredential;
import net.evolveip.ossmosis.api.features.platform.entity.WebexCredential;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface PlatformCredentialMapper {

  @Mapping(source = "platformCredentialId", target = "platformCredentialId")
  @Mapping(source = "hostname", target = "hostname")
  @Mapping(source = "username", target = "username")
  @Mapping(source = "password", target = "password")
  @Mapping(source = "port", target = "port")
  @Mapping(source = "timeout", target = "timeout")
  @Mapping(source = "requestsPerSecond", target = "requestsPerSecond")
  @Mapping(source = "platformId", target = "platform.platformId")
  @Mapping(ignore = true, target = "dateCreated")
  @Mapping(ignore = true, target = "dateUpdated")
  @Mapping(target = "version", expression = "java(getExtraField(dto.getExtra(), \"version\"))")
  @Mapping(target = "connectionType", expression = "java(getExtraField(dto.getExtra(), \"connectionType\"))")
  @Mapping(target = "connectionGroup", expression = "java(getExtraField(dto.getExtra(), \"connectionGroup\"))")
  @Mapping(target = "connectionName", expression = "java(getExtraField(dto.getExtra(), \"connectionName\"))")
  BroadsoftASCredential toBroadsoftASEntity(PlatformCredentialRequestDTO dto);

  @Mapping(source = "platformCredentialId", target = "platformCredentialId")
  @Mapping(source = "hostname", target = "hostname")
  @Mapping(source = "username", target = "username")
  @Mapping(source = "password", target = "password")
  @Mapping(source = "port", target = "port")
  @Mapping(source = "timeout", target = "timeout")
  @Mapping(source = "requestsPerSecond", target = "requestsPerSecond")
  @Mapping(source = "platformId", target = "platform.platformId")
  @Mapping(ignore = true, target = "dateCreated")
  @Mapping(ignore = true, target = "dateUpdated")
  @Mapping(target = "version", expression = "java(getExtraField(dto.getExtra(), \"version\"))")
  @Mapping(target = "connectionType", expression = "java(getExtraField(dto.getExtra(), \"connectionType\"))")
  @Mapping(target = "connectionGroup", expression = "java(getExtraField(dto.getExtra(), \"connectionGroup\"))")
  @Mapping(target = "connectionName", expression = "java(getExtraField(dto.getExtra(), \"connectionName\"))")
  BroadsoftNSCredential toBroadsoftNSEntity(PlatformCredentialRequestDTO dto);

  @Mapping(source = "platformCredentialId", target = "platformCredentialId")
  @Mapping(source = "hostname", target = "hostname")
  @Mapping(source = "username", target = "username")
  @Mapping(source = "password", target = "password")
  @Mapping(source = "port", target = "port")
  @Mapping(source = "timeout", target = "timeout")
  @Mapping(source = "requestsPerSecond", target = "requestsPerSecond")
  @Mapping(source = "enabled", target = "enabled")
  @Mapping(source = "platformId", target = "platform.platformId")
  @Mapping(ignore = true, target = "dateCreated")
  @Mapping(ignore = true, target = "dateUpdated")
  @Mapping(target = "clientId", expression = "java(getExtraField(dto.getExtra(), \"clientId\"))")
  @Mapping(target = "clientSecret", expression = "java(getExtraField(dto.getExtra(), \"clientSecret\"))")
  @Mapping(target = "accountId", expression = "java(getExtraField(dto.getExtra(), \"accountId\"))")
  @Mapping(target = "accountSecret", expression = "java(getExtraField(dto.getExtra(), \"accountSecret\"))")
  @Mapping(target = "callRecordingPlatform", expression = "java(getExtraField(dto.getExtra(), \"callRecordingPlatform\"))")
  @Mapping(target = "userDefaultLanguage", expression = "java(getExtraField(dto.getExtra(), \"userDefaultLanguage\"))")
  @Mapping(target = "userDefaultRole", expression = "java(getExtraField(dto.getExtra(), \"userDefaultRole\"))")
  @Mapping(target = "groupId", expression = "java(getExtraField(dto.getExtra(), \"groupId\"))")
  DubberCredential toDubberEntity(PlatformCredentialRequestDTO dto);

  @Mapping(source = "platformCredentialId", target = "platformCredentialId")
  @Mapping(source = "hostname", target = "hostname")
  @Mapping(source = "username", target = "username")
  @Mapping(source = "password", target = "password")
  @Mapping(source = "port", target = "port")
  @Mapping(source = "timeout", target = "timeout")
  @Mapping(source = "requestsPerSecond", target = "requestsPerSecond")
  @Mapping(source = "enabled", target = "enabled")
  @Mapping(source = "platformId", target = "platform.platformId")
  @Mapping(ignore = true, target = "dateCreated")
  @Mapping(ignore = true, target = "dateUpdated")
  RedskyCredential toRedskyEntity(PlatformCredentialRequestDTO dto);

  @Mapping(source = "platformCredentialId", target = "platformCredentialId")
  @Mapping(source = "hostname", target = "hostname")
  @Mapping(source = "username", target = "username")
  @Mapping(source = "password", target = "password")
  @Mapping(source = "port", target = "port")
  @Mapping(source = "timeout", target = "timeout")
  @Mapping(source = "requestsPerSecond", target = "requestsPerSecond")
  @Mapping(source = "enabled", target = "enabled")
  @Mapping(source = "platformId", target = "platform.platformId")
  @Mapping(ignore = true, target = "dateCreated")
  @Mapping(ignore = true, target = "dateUpdated")
  @Mapping(target = "clientId", expression = "java(getExtraField(dto.getExtra(), \"clientId\"))")
  @Mapping(target = "redirectUri", expression = "java(getExtraField(dto.getExtra(), \"redirectUri\"))")
  @Mapping(target = "grantType", expression = "java(getExtraField(dto.getExtra(), \"grantType\"))")
  WebexCredential toWebexEntity(PlatformCredentialRequestDTO dto);

  @Mapping(source = "platformCredentialId", target = "platformCredentialId")
  @Mapping(source = "hostname", target = "hostname")
  @Mapping(source = "username", target = "username")
  @Mapping(source = "password", target = "password")
  @Mapping(source = "port", target = "port")
  @Mapping(source = "timeout", target = "timeout")
  @Mapping(source = "requestsPerSecond", target = "requestsPerSecond")
  @Mapping(source = "enabled", target = "enabled")
  @Mapping(source = "dateCreated", target = "dateCreated")
  @Mapping(source = "dateUpdated", target = "dateUpdated")
  @Mapping(source = "platform.platformId", target = "platformId")
  @Mapping(target = "type", expression = "java(getCredentialType(platformCredential))")
  @Mapping(target = "extra", expression = "java(getExtraFields(platformCredential))")
  PlatformCredentialResponseDTO toDTO(PlatformCredential platformCredential);

  List<PlatformCredentialResponseDTO> entitiesToDTOs(List<PlatformCredential> platformCredentials);

  @Mapping(source = "hostname", target = "hostName")
  @Mapping(source = "username", target = "userId")
  @Mapping(source = "password", target = "password")
  @Mapping(source = "port", target = "port")
  @Mapping(source = "timeout", target = "connectionTimeout")
  @Mapping(source = "version", target = "version")
  @Mapping(source = "connectionType", target = "connectionType")
  @Mapping(source = "connectionGroup", target = "connectionGroup")
  @Mapping(source = "connectionName", target = "connectionName")
  @Mapping(source = "platform.platformName", target = "platformIdentifier")
  @Mapping(ignore = true, target = "ipAddress")
  @Mapping(ignore = true, target = "dmsProvisioningServer")
  @Mapping(ignore = true, target = "receptionistClientUrl")
  @Mapping(ignore = true, target = "callCenterClientUrl")
  @Mapping(ignore = true, target = "enabled")
  @Mapping(ignore = true, target = "readTimeout")
  @Mapping(ignore = true, target = "debug")
  BroadsoftOCIConnectionData toBroadsoftOCIConnectionData(BroadsoftASCredential platformCredential);

  @Mapping(source = "hostname", target = "hostName")
  @Mapping(source = "username", target = "userId")
  @Mapping(source = "password", target = "password")
  @Mapping(source = "port", target = "port")
  @Mapping(source = "timeout", target = "connectionTimeout")
  @Mapping(source = "version", target = "version")
  @Mapping(source = "connectionType", target = "connectionType")
  @Mapping(source = "connectionGroup", target = "connectionGroup")
  @Mapping(source = "connectionName", target = "connectionName")
  @Mapping(source = "platform.platformName", target = "platformIdentifier")
  @Mapping(ignore = true, target = "ipAddress")
  @Mapping(ignore = true, target = "dmsProvisioningServer")
  @Mapping(ignore = true, target = "receptionistClientUrl")
  @Mapping(ignore = true, target = "callCenterClientUrl")
  @Mapping(ignore = true, target = "enabled")
  @Mapping(ignore = true, target = "readTimeout")
  @Mapping(ignore = true, target = "debug")
  BroadsoftOCIConnectionData toBroadsoftOCIConnectionData(BroadsoftNSCredential platformCredential);

  @Mapping(source = "hostname", target = "hostname")
  @Mapping(source = "username", target = "username")
  @Mapping(source = "password", target = "password")
  @Mapping(source = "requestsPerSecond", target = "requestsPerSecond")
  @Mapping(source = "enabled", target = "enabled")
  RedskyClientCredentials toRedskyClientCredentials(RedskyCredential platformCredential);

  default String getCredentialType(PlatformCredential credential) {
    if (credential instanceof BroadsoftASCredential) {
      return PlatformCredentialConstants.BROADSOFT_AS_CRED_TYPE;
    } else if (credential instanceof BroadsoftNSCredential) {
      return PlatformCredentialConstants.BROADSOFT_NS_CRED_TYPE;
    } else if (credential instanceof DubberCredential) {
      return PlatformCredentialConstants.DUBBER_CRED_TYPE;
    } else if (credential instanceof RedskyCredential) {
      return PlatformCredentialConstants.REDSKY_CRED_TYPE;
    } else if (credential instanceof WebexCredential) {
      return PlatformCredentialConstants.WEBEX_CRED_TYPE;
    }
    return null;
  }

  default String getExtraField(Map<String, Object> extra, String fieldName) {
    if (extra != null && extra.containsKey(fieldName)) {
      return extra.get(fieldName).toString();
    }
    return null;
  }

  default Map<String, Object> getExtraFields(PlatformCredential credential) {
    Map<String, Object> extra = new HashMap<>();
    
    if (credential instanceof BroadsoftASCredential) {
      BroadsoftASCredential bsCred = (BroadsoftASCredential) credential;
      extra.put("version", bsCred.getVersion());
      extra.put("connectionType", bsCred.getConnectionType());
      extra.put("connectionName", bsCred.getConnectionName());
      extra.put("connectionGroup", bsCred.getConnectionGroup());
    } else if (credential instanceof BroadsoftNSCredential) {
      BroadsoftNSCredential bsCred = (BroadsoftNSCredential) credential;
      extra.put("version", bsCred.getVersion());
      extra.put("connectionType", bsCred.getConnectionType());
      extra.put("connectionName", bsCred.getConnectionName());
      extra.put("connectionGroup", bsCred.getConnectionGroup());
    } else if (credential instanceof DubberCredential) {
      DubberCredential dubberCred = (DubberCredential) credential;
      extra.put("clientId", dubberCred.getClientId());
      extra.put("clientSecret", dubberCred.getClientSecret());
      extra.put("accountId", dubberCred.getAccountId());
      extra.put("accountSecret", dubberCred.getAccountSecret());
      extra.put("callRecordingPlatform", dubberCred.getCallRecordingPlatform());
      extra.put("userDefaultLanguage", dubberCred.getUserDefaultLanguage());
      extra.put("userDefaultRole", dubberCred.getUserDefaultRole());
      extra.put("groupId", dubberCred.getGroupId());
    } else if (credential instanceof WebexCredential) {
      WebexCredential webexCred = (WebexCredential) credential;
      extra.put("clientId", webexCred.getClientId());
      extra.put("redirectUri", webexCred.getRedirectUri());
      extra.put("grantType", webexCred.getGrantType());
    }
    
    return extra;
  }
}
