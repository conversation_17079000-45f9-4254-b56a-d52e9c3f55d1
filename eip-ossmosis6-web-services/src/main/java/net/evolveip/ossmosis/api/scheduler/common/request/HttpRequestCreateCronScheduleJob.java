package net.evolveip.ossmosis.api.scheduler.common.request;

import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Valid
@Builder
public class HttpRequestCreateCronScheduleJob {

  @Valid
  private HttpRequestCreateEmailDefinition emailDefinition;
  @Valid
  private HttpRequestCreateCronScheduleDefinition jobScheduleDefinition;
  @Valid
  private HttpRequestCreateJobDefinition jobDefinition;
}