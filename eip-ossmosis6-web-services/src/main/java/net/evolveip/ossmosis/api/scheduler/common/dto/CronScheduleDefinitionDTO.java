package net.evolveip.ossmosis.api.scheduler.common.dto;

import java.time.ZoneId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.quartz.CronExpression;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CronScheduleDefinitionDTO {

  private final String scheduleType = "CronSchedule";
  private CronExpression cronJobExpression;
  private String cronJobExpressionString;
  private ZoneId userTimeZone;
}