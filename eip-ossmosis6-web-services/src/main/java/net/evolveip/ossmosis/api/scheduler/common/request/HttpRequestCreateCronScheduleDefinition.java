package net.evolveip.ossmosis.api.scheduler.common.request;

import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidCronExpression;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidZoneId;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Valid
public class HttpRequestCreateCronScheduleDefinition {

  @ValidCronExpression
  private String cronJobExpression;
  @ValidZoneId
  private String userTimeZone;
}