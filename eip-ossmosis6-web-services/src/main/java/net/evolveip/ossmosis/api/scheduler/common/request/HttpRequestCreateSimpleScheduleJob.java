package net.evolveip.ossmosis.api.scheduler.common.request;


import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Valid
public class HttpRequestCreateSimpleScheduleJob {

  @Valid
  private HttpRequestCreateEmailDefinition emailDefinition;
  @Valid
  private HttpRequestCreateSimpleScheduleDefinition jobScheduleDefinition;
  @Valid
  private HttpRequestCreateJobDefinition jobDefinition;
}