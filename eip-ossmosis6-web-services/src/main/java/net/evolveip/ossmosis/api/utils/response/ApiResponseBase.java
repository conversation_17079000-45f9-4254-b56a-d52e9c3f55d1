package net.evolveip.ossmosis.api.utils.response;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;
import lombok.Data;

@Data
public class ApiResponseBase {

  // unique response identifier:
  private UUID responseId;
  // timestamp of the response:
  private ZonedDateTime timestamp;
  // HTTP status code associated with the API response.
  private int httpStatusCode;
  // HTTP status reason of the API response, indicating success or failure.
  private List<ApiResponseError> errors;
  // boolean flag that determines if the request has been successful or not:
  private boolean isSuccessful;


}
