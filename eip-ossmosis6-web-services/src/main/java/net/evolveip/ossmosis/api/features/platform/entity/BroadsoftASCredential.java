package net.evolveip.ossmosis.api.features.platform.entity;

import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import net.evolveip.ossmosis.api.features.platform.constant.PlatformCredentialConstants;

@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@DiscriminatorValue(PlatformCredentialConstants.BROADSOFT_AS_CRED_TYPE)
public class BroadsoftASCredential extends PlatformCredential{

  @Column(name = "version", length = 50)
  private String version;

  @Column(name = "connection_type", length = 50)
  private String connectionType;

  @Column(name = "connection_name", length = 100)
  private String connectionName;

  @Column(name = "connection_group", length = 100)
  private String connectionGroup;
}
