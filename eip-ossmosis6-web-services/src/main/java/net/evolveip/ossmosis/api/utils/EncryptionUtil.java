package net.evolveip.ossmosis.api.utils;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.utils.exceptions.EncryptionException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class EncryptionUtil {

  private final String secretKey;

  private static final int ITERATIONS = 10000;
  private static final int KEY_LENGTH = 128;
  private static final int SALT_LENGTH = 16;
  private static final int IV_LENGTH = 12; // GCM recommends 12 bytes
  private static final int GCM_TAG_LENGTH = 128; // in bits

  public EncryptionUtil(@Value("${spring.datasource.column.encryption.key}") String secretKey) {
    this.secretKey = secretKey;
  }

  private SecretKey deriveKey(String password, byte[] salt) {
    if (password == null || password.isEmpty()) throw new IllegalStateException("Encryption key is null or empty");
    try {
      PBEKeySpec spec = new PBEKeySpec(password.toCharArray(), salt, ITERATIONS, KEY_LENGTH);
      SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
      byte[] keyBytes = factory.generateSecret(spec).getEncoded();
      return new SecretKeySpec(keyBytes, "AES");
    } catch (Exception e) {
      String message = "Failed deriving encryption key";
      logger.error(message, e);
      throw new EncryptionException(message, e);
    }
  }

  public String encrypt(String data) {
    try {
      byte[] salt = new byte[SALT_LENGTH];
      byte[] iv = new byte[IV_LENGTH];
      SecureRandom random = new SecureRandom();
      random.nextBytes(salt);
      random.nextBytes(iv);

      SecretKey key = deriveKey(secretKey, salt);
      Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
      GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
      cipher.init(Cipher.ENCRYPT_MODE, key, gcmSpec);

      byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));

      ByteBuffer buffer = ByteBuffer.allocate(salt.length + iv.length + encryptedBytes.length);
      buffer.put(salt);
      buffer.put(iv);
      buffer.put(encryptedBytes);

      return Base64.getUrlEncoder().encodeToString(buffer.array());
    } catch (Exception e) {
      String message = "Encryption failed";
      logger.error(message, e);
      throw new EncryptionException(message, e);
    }
  }

  public String decrypt(String encryptedData) {
    try {
      byte[] decoded = Base64.getUrlDecoder().decode(encryptedData);
      ByteBuffer buffer = ByteBuffer.wrap(decoded);

      byte[] salt = new byte[SALT_LENGTH];
      buffer.get(salt);

      byte[] iv = new byte[IV_LENGTH];
      buffer.get(iv);

      byte[] encryptedBytes = new byte[buffer.remaining()];
      buffer.get(encryptedBytes);

      SecretKey key = deriveKey(secretKey, salt);
      Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
      GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
      cipher.init(Cipher.DECRYPT_MODE, key, gcmSpec);

      byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
      return new String(decryptedBytes, StandardCharsets.UTF_8);
    } catch (Exception e) {
      String message = "Decryption failed";
      logger.error(message, e);
      throw new EncryptionException(message, e);
    }
  }
}
