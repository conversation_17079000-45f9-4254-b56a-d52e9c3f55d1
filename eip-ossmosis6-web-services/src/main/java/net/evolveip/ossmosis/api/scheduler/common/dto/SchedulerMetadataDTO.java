package net.evolveip.ossmosis.api.scheduler.common.dto;


import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Getter
public class SchedulerMetadataDTO {

  private String schedulerName;
  private String version;
  private String schedulerInstanceId;
  private Boolean started;
  private Boolean shutdown;
  private Boolean inStandbyMode;
  private Date startTime;
  private String schedulerClass;
  private String jobStoreClass;
  private String threadPoolClass;
  private Integer threadPoolSize;
  private Boolean schedulerRemote;
  private Integer numberOfJobsExecuted;
  private Boolean jobStoreSupportsPersistence;
  private Boolean jobStoreClustered;
}
