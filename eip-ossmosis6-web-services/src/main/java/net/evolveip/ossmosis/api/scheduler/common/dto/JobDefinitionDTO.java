package net.evolveip.ossmosis.api.scheduler.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class JobDefinitionDTO {

  private String jobType;
  private Boolean enabled;
  private String createdBy;
  private String createdTimestamp;
  private String updatedBy;
  private String updatedTimestamp;
  private CallDetailReportDTO jobData;
}