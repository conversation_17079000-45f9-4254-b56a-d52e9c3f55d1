package net.evolveip.ossmosis.api.features.resource.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.permission.entity.Permission;
import net.evolveip.ossmosis.api.features.resource.constant.ResourceConstants;
import net.evolveip.ossmosis.api.features.resource.entity.Resource;
import net.evolveip.ossmosis.api.features.resource.entity.ResourcePermission;
import net.evolveip.ossmosis.api.features.resource.entity.ResourcePermissionId;
import net.evolveip.ossmosis.api.features.resource.repository.ResourcePermissionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ResourcePermissionService {

  private final ResourcePermissionRepository resourcePermissionRepository;

  @Autowired
  public ResourcePermissionService(ResourcePermissionRepository resourcePermissionRepository) {
    this.resourcePermissionRepository = resourcePermissionRepository;
  }

  public void createDefaultResourcePermissions(List<Resource> resources,
      List<Permission> permissions) {
    Map<String, Permission> permissionMap = permissions.stream().collect(
        Collectors.toMap(Permission::getPermissionName, permission -> permission));
    Map<String, Resource> resourceMap = resources.stream().collect(
        Collectors.toMap(Resource::getResourceName, resource -> resource));

    if (this.resourcePermissionRepository.count() != ResourceConstants.RESOURCE_PERMISSION_MAP.values()
        .stream()
        .mapToInt(List::size).sum()) {

      List<ResourcePermission> resourcePermissionList = new ArrayList<>();
      for (String resourceName : ResourceConstants.RESOURCE_PERMISSION_MAP.keySet()) {
        for (String permissionName : ResourceConstants.RESOURCE_PERMISSION_MAP.get(resourceName)) {
          resourcePermissionList.add(ResourcePermission.builder()
              .resourcePermissionId(
                  new ResourcePermissionId(resourceMap.get(resourceName).getResourceId(),
                      permissionMap.get(permissionName).getPermissionId()))
              .resource(resourceMap.get(resourceName))
              .permission(permissionMap.get(permissionName))
              .build());
        }
      }
      this.resourcePermissionRepository.saveAll(resourcePermissionList);
      logger.info("Created default resource permissions assignable");
    }
  }
}
