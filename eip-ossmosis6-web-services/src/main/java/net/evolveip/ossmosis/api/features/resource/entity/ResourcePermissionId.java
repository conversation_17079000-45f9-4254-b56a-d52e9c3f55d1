package net.evolveip.ossmosis.api.features.resource.entity;

import jakarta.persistence.Embeddable;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Embeddable
public class ResourcePermissionId implements Serializable {

  private Integer resourceId;

  private Integer permissionId;

}
