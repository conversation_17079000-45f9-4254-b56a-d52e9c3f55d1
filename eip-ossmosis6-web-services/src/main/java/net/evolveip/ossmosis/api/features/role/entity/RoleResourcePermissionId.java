package net.evolveip.ossmosis.api.features.role.entity;

import jakarta.persistence.Embeddable;
import java.io.Serializable;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Embeddable
public class RoleResourcePermissionId implements Serializable {

  private Integer roleId;
  private Integer resourceId;
  private Integer permissionId;

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof RoleResourcePermissionId that)) {
      return false;
    }

    return Objects.equals(this.roleId, that.roleId)
        && Objects.equals(this.resourceId, that.resourceId)
        && Objects.equals(this.permissionId, that.permissionId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(roleId, resourceId, permissionId);
  }
}