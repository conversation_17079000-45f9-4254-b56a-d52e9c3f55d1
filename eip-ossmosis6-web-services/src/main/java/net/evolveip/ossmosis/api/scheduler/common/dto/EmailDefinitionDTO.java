package net.evolveip.ossmosis.api.scheduler.common.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EmailDefinitionDTO {

  private String enterpriseId;
  private String enterpriseName;
  @Builder.Default
  private String fromEmail = "<EMAIL>";
  private List<String> toEmail;
}