package net.evolveip.ossmosis.api.config.auth;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.oauth2.jose.jws.SignatureAlgorithm;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.stereotype.Component;

@Component
public class DynamicJwtDecoderResolver {

  @Value("${spring.security.oauth2.resourceserver.jwt.jwk-set-uri}")
  private String defaultJwkSetUri;

  public JwtDecoder resolve(Jwt jwt) {
    String tenant = jwt.getClaimAsString("tenant");
    String appName = jwt.getClaimAsString("app");
    String jwkSetUri = determineJwkSetUri(tenant, appName);

    return NimbusJwtDecoder.withJwkSetUri(jwkSetUri)
        .jwsAlgorithm(SignatureAlgorithm.RS256)
        .build();
  }

  private String determineJwkSetUri(String tenant, String appName) {
    return this.defaultJwkSetUri.replace("{tenant}", tenant).replace("{app_name}", appName);
  }
}