package net.evolveip.ossmosis.api.features.phonenumber.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import java.time.ZonedDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class represents the status of a phone number.
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "phone_number_status")
@EqualsAndHashCode
public class PhoneNumberStatus {

  @Id
  @Column(name = "status_id", columnDefinition = "SMALLINT", nullable = false)
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer statusId;

  @Column(name = "date_created", nullable = false)
  @EqualsAndHashCode.Exclude
  private ZonedDateTime dateCreated;

  @Column(name = "date_updated", nullable = false)
  @EqualsAndHashCode.Exclude
  private ZonedDateTime dateUpdated = ZonedDateTime.now();

  @NotNull
  @Column(name = "statusName", nullable = false, columnDefinition = "VARCHAR(15) CHECK(statusName in ('ACTIVE','INACTIVE','PENDING','ON CARRIER','PENDING PORT')) default 'INACTIVE'")
  private String statusName;

  @PrePersist
  protected void onCreate() {
    ZonedDateTime now = ZonedDateTime.now();
    if (dateCreated == null) {
      dateCreated = now;
    }
    if (dateUpdated == null) {
      dateUpdated = now;
    }
  }

  @PreUpdate
  protected void onUpdate() {
    dateUpdated = ZonedDateTime.now();
  }
}