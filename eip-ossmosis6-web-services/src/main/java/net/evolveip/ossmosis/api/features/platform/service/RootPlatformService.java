package net.evolveip.ossmosis.api.features.platform.service;


import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.platform.constant.PlatformConstants;
import net.evolveip.ossmosis.api.features.platform.entity.Platform;
import net.evolveip.ossmosis.api.features.platform.repository.PlatformRepository;
import net.evolveip.ossmosis.api.utils.exceptions.NotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class RootPlatformService {

  private static final String rootPlatformName = PlatformConstants.ROOT_PLATFORM_NAME;
  private final PlatformRepository platformRepository;

  @Autowired
  public RootPlatformService(PlatformRepository platformRepository) {
    this.platformRepository = platformRepository;
  }

  @Transactional
  public void createRootPlatform() {
    if (platformRepository.findByPlatformName(rootPlatformName).isEmpty()) {
      Platform rootPlatform = Platform.builder()
          .platformName(rootPlatformName)
          .build();
      platformRepository.save(rootPlatform);
      logger.info("Root platform created: {}", rootPlatformName);
    }
  }

  @Transactional(readOnly = true)
  public Platform getRootPlatform() {
    return platformRepository.findByPlatformName(rootPlatformName)
        .orElseThrow(() -> new NotFoundException("Root platform not found>"));
  }

}