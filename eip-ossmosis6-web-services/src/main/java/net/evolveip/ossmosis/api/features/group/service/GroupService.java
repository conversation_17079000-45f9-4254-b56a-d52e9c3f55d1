package net.evolveip.ossmosis.api.features.group.service;

import io.temporal.api.enums.v1.WorkflowIdReusePolicy;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowExecutionAlreadyStarted;
import io.temporal.client.WorkflowOptions;
import io.temporal.common.SearchAttributes;
import jakarta.transaction.Transactional;
import java.io.File;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.config.csv.CSVTempLocationPropertiesConfig;
import net.evolveip.ossmosis.api.features.enterprise.util.EnterpriseSearchAttributeUtil;
import net.evolveip.ossmosis.api.features.group.common.dto.GroupRequestDTO;
import net.evolveip.ossmosis.api.features.group.common.dto.GroupResponseDTO;
import net.evolveip.ossmosis.api.features.group.entity.Group;
import net.evolveip.ossmosis.api.features.group.mapper.GroupMapper;
import net.evolveip.ossmosis.api.features.group.repository.GroupRepository;
import net.evolveip.ossmosis.api.utils.exceptions.NotFoundException;
import net.evolveip.ossmosis.api.utils.file.CSVService;
import net.evolveip.ossmosis.api.utils.file.PDFService;
import net.evolveip.ossmosis.api.utils.file.XLSXService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.validators.GroupIdValidator;
import net.evolveip.ossmosis.provisioning.constants.QueueConstants;
import net.evolveip.ossmosis.provisioning.features.group.workflows.GroupCreateWorkflow;
import net.evolveip.ossmosis.provisioning.features.group.workflows.GroupDeleteWorkflow;
import net.evolveip.ossmosis.provisioning.features.group.workflows.GroupModifyWorkflow;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class GroupService {

  private final GroupRepository groupRepository;
  private final GroupMapper mapper;
  private final CSVTempLocationPropertiesConfig tempDir;
  private final WorkflowClient workflowClient;
  private final EnterpriseSearchAttributeUtil enterpriseSearchAttributeUtil;

  public GroupService(GroupRepository groupRepository,
      GroupMapper mapper,
      CSVTempLocationPropertiesConfig tempDir,
      WorkflowClient workflowClient,
      EnterpriseSearchAttributeUtil enterpriseSearchAttributeUtil) {
    this.groupRepository = groupRepository;
    this.mapper = mapper;
    this.tempDir = tempDir;
    this.workflowClient = workflowClient;
    this.enterpriseSearchAttributeUtil = enterpriseSearchAttributeUtil;
  }

  public Page<GroupResponseDTO> processGet(String enterpriseId, String filterValue, final Pageable pageable) {
    try {
      String filterValueLowerCase = filterValue.toLowerCase();
      Page<Group> page = groupRepository.findAllByEnterpriseIdAndFilterValue(enterpriseId, filterValueLowerCase, pageable);

      return new PageImpl<>(page.getContent().stream().map(mapper::toDTO).collect(Collectors.toList()), pageable, page.getTotalElements());
    } catch (Exception e) {
      logger.error(e.getMessage());
      return new PageImpl<>(new ArrayList<>(), pageable, 0);
    }
  }

  public ApiResponse<List<GroupResponseDTO>> processGetList(String enterpriseId) {
    try {
      List<Group> groupList = groupRepository.findAllByEnterpriseId(enterpriseId);
      if (groupList.isEmpty()) {
        return ApiResponse.create(new ArrayList<>());
      }
      return ApiResponse.create(groupList.stream().map(mapper::toDTO).collect(Collectors.toList()));
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }

  public ApiResponse<GroupResponseDTO> processGetById(String enterpriseId, String groupId) {
    try {
      Optional<Group> groupOptional = groupRepository.findGroupByEnterpriseIdAndGroupId(enterpriseId, groupId);

      if (groupOptional.isEmpty()) {
        String message = "groupId [" + groupId + "] does not exist";
        logger.info(message);
        return ApiResponse.create(null, false, message, HttpStatus.NO_CONTENT);
      }
      return ApiResponse.create(mapper.toDTO(groupOptional.get()));
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }

  public void processCreate(GroupRequestDTO groupRequestDTO) {

    GroupIdValidator validator = new GroupIdValidator();
    if (!validator.isValid(groupRequestDTO.getGroupId(), null)) {
      String message = "groupId [" + groupRequestDTO.getGroupId()
          + "] is not valid. It must be less than 30 characters long and not contain any of the following special characters: % + / ; : \\ # ' \"";
      logger.info(message);
      throw new NotFoundException(message);
    }

    SearchAttributes searchAttributes = enterpriseSearchAttributeUtil.buildEnterpriseSearchAttributes(
        groupRequestDTO.getEnterpriseId(), null);

    WorkflowOptions options = WorkflowOptions.newBuilder()
        .setTaskQueue(QueueConstants.PROVISIONING_TASK_QUEUE)
        .setWorkflowId(QueueConstants.PROVISIONING_GROUP_CREATE_ID + groupRequestDTO.getGroupId())
        .setWorkflowIdReusePolicy(WorkflowIdReusePolicy.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE)
        .setWorkflowRunTimeout(Duration.ofMinutes(10))
        .setTypedSearchAttributes(searchAttributes)
        .build();

    GroupCreateWorkflow workflow = workflowClient.newWorkflowStub(GroupCreateWorkflow.class, options);

    try {
      WorkflowClient.start(workflow::execute, groupRequestDTO);
    } catch (WorkflowExecutionAlreadyStarted e) {
      throw new IllegalStateException("Group Create Request already exists");
    }
  }

  public void processUpdate(GroupRequestDTO groupRequestDTO) {

    GroupIdValidator validator = new GroupIdValidator();
    if (!validator.isValid(groupRequestDTO.getGroupId(), null)) {
      String message = "groupId [" + groupRequestDTO.getGroupId()
          + "] is not valid. It must be less than 30 characters long and not contain any of the following special characters: % + / ; : \\ # ' \"";
      logger.info(message);
      throw new NotFoundException(message);
    }

    SearchAttributes searchAttributes = enterpriseSearchAttributeUtil.buildEnterpriseSearchAttributes(
        groupRequestDTO.getEnterpriseId(), null);

    WorkflowOptions options = WorkflowOptions.newBuilder()
        .setTaskQueue(QueueConstants.PROVISIONING_TASK_QUEUE)
        .setWorkflowId(QueueConstants.PROVISIONING_GROUP_MODIFY_ID + groupRequestDTO.getGroupId())
        .setWorkflowIdReusePolicy(WorkflowIdReusePolicy.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE)
        .setWorkflowRunTimeout(Duration.ofMinutes(10))
        .setTypedSearchAttributes(searchAttributes)
        .build();

    GroupModifyWorkflow workflow = workflowClient.newWorkflowStub(GroupModifyWorkflow.class, options);

    try {
      WorkflowClient.start(workflow::execute, groupRequestDTO);
    } catch (WorkflowExecutionAlreadyStarted e) {
      throw new IllegalStateException("Group Modify Request already exists");
    }
  }

  public void processDelete(String enterpriseId, String groupId) {

    // check if anything assigned to group eventually

    SearchAttributes searchAttributes = enterpriseSearchAttributeUtil.buildEnterpriseSearchAttributes(enterpriseId, null);

    WorkflowOptions options = WorkflowOptions.newBuilder()
        .setTaskQueue(QueueConstants.PROVISIONING_TASK_QUEUE)
        .setWorkflowId(QueueConstants.PROVISIONING_GROUP_DELETE_ID + groupId)
        .setWorkflowIdReusePolicy(WorkflowIdReusePolicy.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE)
        .setWorkflowRunTimeout(Duration.ofMinutes(10))
        .setTypedSearchAttributes(searchAttributes)
        .build();

    GroupDeleteWorkflow workflow = workflowClient.newWorkflowStub(GroupDeleteWorkflow.class, options);

    try {
      WorkflowClient.start(workflow::execute, enterpriseId, groupId);
    } catch (WorkflowExecutionAlreadyStarted e) {
      throw new IllegalStateException("Group Modify Request already exists");
    }
  }

  public File processDownload(String enterpriseId, String filterValue, String format, String fileName, String timezone) {
    try {
      String newFileName = fileName + "." + format;
      File file;

      Page<Group> groups = groupRepository.findAllByEnterpriseIdAndFilterValue(enterpriseId, filterValue.toLowerCase(), null);
      List<GroupResponseDTO> groupResponseDTOS = groups.getContent().stream().map(mapper::toDTO).toList();

      switch (format) {
        case "csv" -> {
          CSVService<GroupResponseDTO> csvService = new CSVService<>();
          file = csvService.generateCsv(groupResponseDTOS, tempDir, newFileName, timezone);
        }
        case "pdf" -> {
          PDFService<GroupResponseDTO> pdfService = new PDFService<>();
          file = pdfService.generatePdf(groupResponseDTOS, tempDir, newFileName,true,  timezone);
        }
        case "xlsx" -> {
          XLSXService<GroupResponseDTO> xlsxService = new XLSXService<>();
          file = xlsxService.generateXlxs(groupResponseDTOS, tempDir, newFileName, true, timezone);
        }
        default -> {
          CSVService<GroupResponseDTO> csvService = new CSVService<>();
          file = csvService.generateCsv(groupResponseDTOS, tempDir, newFileName, timezone);
        }
      }

      return file;
    } catch (Exception e) {
      logger.error(e.getMessage());
      return null;
    }
  }

  public Group createGroup(GroupRequestDTO groupRequestDTO) {
    Optional<Group> existingGroup = groupRepository.findGroupByEnterpriseIdAndGroupId(groupRequestDTO.getEnterpriseId(), groupRequestDTO.getGroupId());

    if (existingGroup.isPresent()) {
      return existingGroup.get();
    }

    Group candidateGroup = mapper.toEntity(groupRequestDTO);
    return groupRepository.saveAndFlush(candidateGroup);
  }

  public Group modifyGroup(GroupRequestDTO groupRequestDTO) {
    Group existingGroup = groupRepository.findGroupByEnterpriseIdAndGroupId(groupRequestDTO.getEnterpriseId(), groupRequestDTO.getGroupId()).orElseThrow(() -> new NotFoundException("Group not found"));

    if (isUnchanged(existingGroup, groupRequestDTO)) {
      return existingGroup;
    }

    Group candidateGroup = mapper.toEntity(groupRequestDTO);
    Group persistedGroup = groupRepository.saveAndFlush(candidateGroup);

    return persistedGroup;
  }

  @Transactional
  public void deleteGroup(String enterpriseId, String groupId) {
    if (!groupRepository.existsByEnterpriseIdAndGroupId(enterpriseId, groupId)) return;
    groupRepository.deleteByEnterpriseIdAndGroupId(enterpriseId, groupId);
  }

  private boolean isUnchanged(Group existingGroup, GroupRequestDTO groupRequestDTO) {
    return Objects.equals(existingGroup.getGroupId(), groupRequestDTO.getGroupId()) &&
        Objects.equals(existingGroup.getEnterpriseId(), groupRequestDTO.getEnterpriseId()) &&
        Objects.equals(existingGroup.getGroupName(), groupRequestDTO.getGroupName()) &&
        Objects.equals(existingGroup.getTimezone(), groupRequestDTO.getTimezone()) &&
        Objects.equals(existingGroup.getBillingAddress(), groupRequestDTO.getBillingAddress());
  }
}
