package net.evolveip.ossmosis.api.config.dao;

import jakarta.transaction.Transactional;
import java.util.List;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.enterprise.service.RootEnterpriseService;
import net.evolveip.ossmosis.api.features.permission.entity.Permission;
import net.evolveip.ossmosis.api.features.permission.service.PermissionService;
import net.evolveip.ossmosis.api.features.phonenumber.service.DefaultCarriersService;
import net.evolveip.ossmosis.api.features.phonenumber.service.DefaultPhoneNumberStatusService;
import net.evolveip.ossmosis.api.features.platform.service.RootPlatformService;
import net.evolveip.ossmosis.api.features.resource.entity.Resource;
import net.evolveip.ossmosis.api.features.resource.service.ResourcePermissionService;
import net.evolveip.ossmosis.api.features.resource.service.ResourceService;
import net.evolveip.ossmosis.api.features.role.entity.Role;
import net.evolveip.ossmosis.api.features.role.service.DefaultRoleService;
import net.evolveip.ossmosis.api.features.role.service.RoleResourcePermissionService;
import net.evolveip.ossmosis.api.features.role.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class OssmosisDataSetup implements
    ApplicationListener<ContextRefreshedEvent> {

  private final ResourceService resourceService;
  private final DefaultRoleService defaultRoleService;
  private final PermissionService permissionService;
  private final RoleResourcePermissionService roleResourcePermissionService;
  private final ResourcePermissionService resourcePermissionService;
  private final RootPlatformService rootPlatformService;
  private final RootEnterpriseService rootEnterpriseService;
  private final DefaultCarriersService defaultCarriersService;
  private final DefaultPhoneNumberStatusService defaultPhoneNumberStatusService;
  boolean alreadySetup = false;

  @Autowired
  public OssmosisDataSetup(ResourceService resourceRepository,
      DefaultRoleService defaultRoleService,
      PermissionService permissionService,
      RoleResourcePermissionService roleResourcePermissionService,
      ResourcePermissionService resourcePermissionService,
      RootPlatformService rootPlatformService,
      RootEnterpriseService rootEnterpriseService,
      DefaultCarriersService defaultCarriersService,
      DefaultPhoneNumberStatusService defaultPhoneNumberStatusService) {
    this.resourceService = resourceRepository;
    this.defaultRoleService = defaultRoleService;
    this.permissionService = permissionService;
    this.roleResourcePermissionService = roleResourcePermissionService;
    this.resourcePermissionService = resourcePermissionService;
    this.rootPlatformService = rootPlatformService;
    this.rootEnterpriseService = rootEnterpriseService;
    this.defaultCarriersService = defaultCarriersService;
    this.defaultPhoneNumberStatusService = defaultPhoneNumberStatusService;
  }

  @Override
  @Transactional
  public void onApplicationEvent(@NonNull ContextRefreshedEvent event) {

    if (alreadySetup) {
      return;
    }

    // create the root platform:
    rootPlatformService.createRootPlatform();

    // create the root enterprise:
    rootEnterpriseService.createRootEnterprise();

    // create default roles:
    defaultRoleService.createDefaultRoles();
    List<Role> roles = defaultRoleService.getDefaultRoles();


    List<Resource> resources = this.resourceService.createDefaultResources();
    List<Permission> permissions = this.permissionService.createDefaultPermissions();
    this.resourcePermissionService.createDefaultResourcePermissions(resources, permissions);
    this.roleResourcePermissionService.createDefaultRoleResourcePermissions(roles, resources,
        permissions);
    this.defaultCarriersService.createDefaultCarriers();
    this.defaultPhoneNumberStatusService.createDefaultPhoneNumberStatus();

    alreadySetup = true;
  }
}