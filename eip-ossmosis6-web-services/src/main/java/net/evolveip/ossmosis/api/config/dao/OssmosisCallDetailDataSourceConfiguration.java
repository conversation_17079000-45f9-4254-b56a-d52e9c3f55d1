package net.evolveip.ossmosis.api.config.dao;

import java.io.IOException;
import java.util.HashMap;
import javax.sql.DataSource;
import org.hibernate.cfg.AvailableSettings;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.LazyConnectionDataSourceProxy;
import org.springframework.jdbc.datasource.init.DataSourceInitializer;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
@EnableJpaRepositories(
    basePackages = "net.evolveip.ossmosis.api.features.calldetails.repository",
    entityManagerFactoryRef = "snowflakeEntityManagerFactory",
    transactionManagerRef = "snowflakeTransactionManager"
)
public class OssmosisCallDetailDataSourceConfiguration {

  @Value("${spring.snowflake.jpa.properties.hibernate.dialect}")
  private String hibernateDialect;

  @Bean
  @ConfigurationProperties("spring.snowflake.datasource")
  public DataSourceProperties snowflakeDataSourceProperties() {
    return new DataSourceProperties();
  }

  @Bean
  public DataSource snowflakeDataSource() {
    DataSourceProperties properties = snowflakeDataSourceProperties();
    DataSource targetDataSource = properties.initializeDataSourceBuilder().build();
    return new LazyConnectionDataSourceProxy(targetDataSource);
  }

  @Bean
  public DataSourceInitializer snowflakeDataSourceInitializer(
      @Qualifier("snowflakeDataSource") final DataSource dataSource)
      throws IOException {
    DataSourceInitializer dataSourceInitializer = new DataSourceInitializer();
    dataSourceInitializer.setDataSource(dataSource);
    return dataSourceInitializer;
  }

  @Bean
  public LocalContainerEntityManagerFactoryBean snowflakeEntityManagerFactory() {
    LocalContainerEntityManagerFactoryBean entityManagerFactoryBean = new LocalContainerEntityManagerFactoryBean();
    entityManagerFactoryBean.setDataSource(snowflakeDataSource());
    entityManagerFactoryBean.setPackagesToScan("net.evolveip.ossmosis.api.features.calldetails.entity");

    HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
    entityManagerFactoryBean.setJpaVendorAdapter(vendorAdapter);

    HashMap<String, Object> properties = new HashMap<>();
    properties.put(AvailableSettings.DIALECT, hibernateDialect);
    entityManagerFactoryBean.setJpaPropertyMap(properties);

    return entityManagerFactoryBean;
  }

  @Bean
  public PlatformTransactionManager snowflakeTransactionManager() {
    JpaTransactionManager transactionManager = new JpaTransactionManager();
    transactionManager.setEntityManagerFactory(snowflakeEntityManagerFactory().getObject());
    return transactionManager;
  }

}
