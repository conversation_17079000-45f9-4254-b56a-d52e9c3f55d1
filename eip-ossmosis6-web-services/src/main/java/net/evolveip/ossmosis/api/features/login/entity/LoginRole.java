package net.evolveip.ossmosis.api.features.login.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import java.time.ZonedDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import net.evolveip.ossmosis.api.features.role.entity.Role;
import org.hibernate.annotations.ColumnDefault;

@Entity
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Table(name = "login_role", uniqueConstraints = @UniqueConstraint(columnNames = {"login_id",
    "role_id"}))
public class LoginRole {

  @Id
  @Column(name = "login_role_id")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long loginRoleId;

  @Column(name = "date_created", nullable = false, updatable = false)
  @EqualsAndHashCode.Exclude
  private ZonedDateTime dateCreated;

  @Column(name = "date_updated", nullable = false)
  @ColumnDefault("CURRENT_TIMESTAMP")
  @EqualsAndHashCode.Exclude
  private ZonedDateTime dateUpdated;

  @ManyToOne(targetEntity = Login.class, fetch = FetchType.LAZY)
  @JoinColumn(name = "login_id", nullable = false)
  private Login login;

  @ManyToOne(targetEntity = Role.class, fetch = FetchType.LAZY)
  @JoinColumn(name = "role_id", nullable = false)
  private Role role;
}