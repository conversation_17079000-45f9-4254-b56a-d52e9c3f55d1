package net.evolveip.ossmosis.api.scheduler.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JobSpecificationDTO {

  private String jobGroup;
  private String jobId;
  private String jobDescription;
  private String jobRunnerClass;
  private Boolean persistJobDataAfterExecution;
  private Boolean isExecuting;
  private Boolean durable;
  private Boolean requestsRecovery;
  private Boolean concurrentExecutionDisallowed;
}