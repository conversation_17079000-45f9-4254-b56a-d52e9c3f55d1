package net.evolveip.ossmosis.api.features.role.constant;

import java.util.AbstractMap;
import java.util.AbstractMap.SimpleEntry;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import net.evolveip.ossmosis.api.features.permission.constant.PermissionConstants;
import net.evolveip.ossmosis.api.features.resource.constant.ResourceConstants;

public final class RoleConstants {

  public static final String ROLE_SUPER_USER = "super user";
  public static final String ROLE_ADMIN = "admin";
  public static final String ROLE_POWER_USER = "power user";
  public static final String ROLE_STANDARD_USER = "standard user";

  public static final ArrayList<String> roleNameList = new ArrayList<>(
      List.of(
          ROLE_SUPER_USER,
          ROLE_ADMIN,
          ROLE_POWER_USER,
          ROLE_STANDARD_USER)
  );

  public static final Map<String, Map<String, ArrayList<String>>> DEFAULT_ROLE_RESOURCE_PERMISSION_MAP = Stream.of(
      new AbstractMap.SimpleEntry<>(RoleConstants.ROLE_SUPER_USER, Stream.of(
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_REPORTS_CALL_DETAILS, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_REPORTS_AUDIT_LOGS, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_TELEPHONY_PHONE_NUMBERS, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_SECURITY_LOGINS, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_SECURITY_ROLES, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_PROVISIONING_ENTERPRISES, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_PROVISIONING_GROUPS, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_ADMIN_PLATFORMS, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_ADMIN_TOOLBOX, new ArrayList<>(List.of(PermissionConstants.PERMISSION_NAME_FULL_ACCESS))))
          .collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue))),
      new AbstractMap.SimpleEntry<>(RoleConstants.ROLE_ADMIN, Stream.of(
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_REPORTS_CALL_DETAILS, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_REPORTS_AUDIT_LOGS, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_TELEPHONY_PHONE_NUMBERS, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_SECURITY_LOGINS, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_SECURITY_ROLES, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_PROVISIONING_ENTERPRISES, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_PROVISIONING_GROUPS, PermissionConstants.crudPermissionNameList))
          .collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue))),
      new AbstractMap.SimpleEntry<>(RoleConstants.ROLE_POWER_USER, Stream.of(
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_REPORTS_CALL_DETAILS, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_REPORTS_AUDIT_LOGS, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_TELEPHONY_PHONE_NUMBERS, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_PROVISIONING_ENTERPRISES, PermissionConstants.crudPermissionNameList),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_PROVISIONING_GROUPS, PermissionConstants.crudPermissionNameList))
          .collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue))),
      new AbstractMap.SimpleEntry<>(RoleConstants.ROLE_STANDARD_USER, Stream.of(
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_REPORTS_CALL_DETAILS, new ArrayList<>(List.of(PermissionConstants.PERMISSION_NAME_READ))),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_REPORTS_AUDIT_LOGS, new ArrayList<>(List.of(PermissionConstants.PERMISSION_NAME_READ))),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_TELEPHONY_PHONE_NUMBERS, new ArrayList<>(List.of(PermissionConstants.PERMISSION_NAME_READ))),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_PROVISIONING_ENTERPRISES, new ArrayList<>(List.of(PermissionConstants.PERMISSION_NAME_READ))),
              new AbstractMap.SimpleEntry<>(ResourceConstants.RESOURCE_PROVISIONING_GROUPS, new ArrayList<>(List.of(PermissionConstants.PERMISSION_NAME_READ))))
          .collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue)))
  ).collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue));

  // Security
  public static final String ROLES_READ = "roles_read";
  public static final String ROLES_CREATE = "roles_create";
  public static final String ROLES_UPDATE = "roles_update";
  public static final String ROLES_DELETE = "roles_delete";

  public static final String TOOLBOX_FULL_ACCESS = "toolbox_full_access";
}
