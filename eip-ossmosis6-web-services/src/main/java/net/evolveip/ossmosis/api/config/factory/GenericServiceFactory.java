package net.evolveip.ossmosis.api.config.factory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

@Component
public class GenericServiceFactory {

  private final ApplicationContext applicationContext;

  @Autowired
  public GenericServiceFactory(ApplicationContext applicationContext) {
    this.applicationContext = applicationContext;
  }

  public <T> T getService(Class<T> type) {
    return applicationContext.getBean(type);
  }
}