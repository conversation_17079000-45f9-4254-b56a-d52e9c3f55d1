package net.evolveip.ossmosis.api.features.login.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.transaction.Transactional;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.enterprise.repository.EnterpriseRepository;
import net.evolveip.ossmosis.api.features.login.dto.LoginRequestDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginRoleRequestDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginRoleResponseDTO;
import net.evolveip.ossmosis.api.features.login.entity.Login;
import net.evolveip.ossmosis.api.features.login.entity.LoginRole;
import net.evolveip.ossmosis.api.features.login.mapper.LoginRoleMapper;
import net.evolveip.ossmosis.api.features.login.repository.LoginRepository;
import net.evolveip.ossmosis.api.features.login.repository.LoginRoleRepository;
import net.evolveip.ossmosis.api.features.role.dto.RoleToLoginsRequestDTO;
import net.evolveip.ossmosis.api.features.role.dto.RoleToLoginsResponseDTO;
import net.evolveip.ossmosis.api.features.role.dto.RoleToLoginsStatus;
import net.evolveip.ossmosis.api.features.role.entity.Role;
import net.evolveip.ossmosis.api.features.role.repository.RoleRepository;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.response.ApiResponseError;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class LoginRoleService {

  private final LoginRoleRepository loginRoleRepository;
  private final LoginRoleMapper mapper;
  private final RoleRepository roleRepository;
  private final LoginRepository loginRepository;
  private final EnterpriseRepository enterpriseRepository;
  private final ObjectMapper objectMapper;


  @Autowired
  public LoginRoleService(LoginRoleRepository loginRoleRepository, LoginRoleMapper mapper,
      RoleRepository roleRepository, LoginRepository loginRepository,
      EnterpriseRepository enterpriseRepository, ObjectMapper objectMapper) {
    this.loginRoleRepository = loginRoleRepository;
    this.mapper = mapper;
    this.roleRepository = roleRepository;
    this.loginRepository = loginRepository;
    this.enterpriseRepository = enterpriseRepository;
    this.objectMapper = objectMapper;
  }

  /***
   * Retrieves the loginRole entities filtered by the loginId argument
   * @param loginId the loginId to retrieve the roles for
   * @return a DTO
   */
  public ApiResponse<List<LoginRoleResponseDTO>> processGet(Long loginId) {
    try {
      return getLoginRoleList(loginId);
    } catch (Exception e) {
      String message = String.format(e.getMessage());
      logger.error(message);
      return ApiResponse.create(e);
    }
  }

  /***
   * Retrieves the loginRoles entities filtered by the loginId and roleId arguments
   * @param loginId the loginId to retrieve the roles for
   * @param roleId the roleId to filter the results by
   * @return a DTO
   */
  public ApiResponse<List<LoginRoleResponseDTO>> processGet(Long loginId, Integer roleId) {
    try {
      return getLoginRoleList(loginId, roleId);
    } catch (Exception e) {
      String message = String.format(e.getMessage());
      logger.error(message);
      return ApiResponse.create(e);
    }
  }

  /***
   * Creates or updates a loginRole entity, based in the incoming loginRoleRequestDTO
   * @param loginRoleRequestDTO the DTO containing the loginId and roleId to be created or updated
   * @return
   */
  public ApiResponse<LoginRoleResponseDTO> processCreateOrUpdate(
      LoginRoleRequestDTO loginRoleRequestDTO) {
    try {
      LoginRole loginRole = loginRoleRepository.save(mapper.toEntity(loginRoleRequestDTO));
      return ApiResponse.create(mapper.fromEntity(loginRole));
    } catch (Exception e) {
      String message = String.format(e.getMessage());
      logger.error(message);
      return ApiResponse.create(e);
    }
  }

  /***
   * Creates or updates a list of loginRole entities, based on the incoming list of loginRoleRequestDTOs.
   * @param loginRoleRequestDTOs the list containing the DTOs to be created or updated
   * @return a standard ApiResponse encapsulating the loginResponseDTO
   */
  public ApiResponse<List<LoginRoleResponseDTO>> processCreateOrUpdate(
      List<LoginRoleRequestDTO> loginRoleRequestDTOs) {
    try {
      List<LoginRole> candidateLoginRoles = loginRoleRequestDTOs.stream().map(mapper::toEntity)
          .toList();

      List<LoginRoleResponseDTO> loginRoles = new ArrayList<>();
      for (LoginRole candidateLoginRole : candidateLoginRoles) {
        Optional<LoginRole> optionalExistentLoginRole = loginRoleRepository.findByLoginIdAndRoleId(
            candidateLoginRole.getLogin().getLoginId(), candidateLoginRole.getRole().getRoleId());
        if (optionalExistentLoginRole.isPresent()) {
          loginRoles.add(mapper.fromEntity(optionalExistentLoginRole.get()));
        } else {
          loginRoles.add(mapper.fromEntity(loginRoleRepository.saveAndFlush(candidateLoginRole)));
        }
      }
      return ApiResponse.create(loginRoles);
    } catch (Exception e) {
      String message = String.format(e.getMessage());
      logger.error(message);
      return ApiResponse.create(null, false, message, HttpStatus.BAD_REQUEST);
    }
  }

  /***
   * It creates an association between a roleId and a list of loginIds, for a given enterpriseId.
   * @param enterpriseId an enterpriseId under which the role assignment happens
   * @param roleToLoginsRequestDTO the payload that has the roleId and the lists of loginIds to be
   *                               assigned or removed to and from the role
   * @return a dto with the populated assignment and removal lists
   */
  @Transactional
  public ApiResponse<RoleToLoginsResponseDTO> assignToOrRemoveLoginsFromRole(String enterpriseId,
      RoleToLoginsRequestDTO roleToLoginsRequestDTO) {
    try {
      // validate the enterpriseId path variable:
      if (!enterpriseRepository.existsById(enterpriseId)) {
        String message = String.format("Invalid enterpriseId: %s", enterpriseId);
        return ApiResponse.create(null, false, message, HttpStatus.NO_CONTENT);
      }
      // validate the roleId:
      int roleId = roleToLoginsRequestDTO.getRoleId();
      if (!roleRepository.existsById(roleId)) {
        String message = String.format("Invalid roleId: %s", roleToLoginsRequestDTO.getRoleId());
        return ApiResponse.create(null, false, message, HttpStatus.BAD_REQUEST);
      }

      List<Long> candidateAssignLoginIds = roleToLoginsRequestDTO.getAssignLoginIds();
      List<Long> candidateRemoveLoginIds = roleToLoginsRequestDTO.getRemoveLoginIds();

      Map<Long, Enum<?>> assignedLoginIds = new HashMap<>();
      Map<Long, Enum<?>> removedLoginIds = new HashMap<>();

      if (!candidateAssignLoginIds.isEmpty()) {
        for (Long candidateLoginId : candidateAssignLoginIds) {

          // check if the current loginId exists in the login repository:
          if (!loginRepository.existsById(candidateLoginId)) {
            assignedLoginIds.put(candidateLoginId, RoleToLoginsStatus.INVALID);
            continue;
          }

          // check if the current loginId exists both in the assign and removal lists
          // if yes, mark it as duplicate and remove it from the removal list:
          if (candidateRemoveLoginIds.contains(candidateLoginId)) {
            candidateRemoveLoginIds.remove(candidateLoginId);
            assignedLoginIds.put(candidateLoginId, RoleToLoginsStatus.DUPLICATE);
            removedLoginIds.put(candidateLoginId, RoleToLoginsStatus.DUPLICATE);
            continue;
          }

          // check if the current loginId is being used in role assignment:
          if (lookupLoginRoleId(candidateLoginId, roleId)) {
            assignedLoginIds.put(
                mapper.fromEntity(getLoginRole(candidateLoginId, roleId).get()).getLoginId(),
                RoleToLoginsStatus.EXISTING);
          } else {
            assignedLoginIds.put(mapper.fromEntity(loginRoleRepository.saveAndFlush(
                    provideLoginRoleWithDate(candidateLoginId, roleId))).getLoginId(),
                RoleToLoginsStatus.ADDED);
          }
        }
      }

      if (!candidateRemoveLoginIds.isEmpty()) {

        // process the list for removal:
        for (Long candidateLoginId : candidateRemoveLoginIds) {

          // check if the current loginId exists in the login repository:
          if (!loginRepository.existsById(candidateLoginId)) {
            removedLoginIds.put(candidateLoginId, RoleToLoginsStatus.INVALID);
            continue;
          }

          if (!lookupLoginRoleId(candidateLoginId, roleId)) {
            removedLoginIds.put(candidateLoginId, RoleToLoginsStatus.NOT_EXISTING);
          } else {
            loginRoleRepository.delete(getLoginRole(candidateLoginId, roleId).get());
            if (!lookupLoginRoleId(candidateLoginId, roleId)) {
              removedLoginIds.put(candidateLoginId, RoleToLoginsStatus.REMOVED);
            } else {
              removedLoginIds.put(candidateLoginId, RoleToLoginsStatus.NOT_REMOVED);
            }
          }
        }
      }
      return ApiResponse.create(
          new RoleToLoginsResponseDTO(roleId, assignedLoginIds, removedLoginIds));
    } catch (Exception e) {
      String message = String.format(e.getMessage());
      logger.error(message);
      return ApiResponse.create(null, false, message, HttpStatus.BAD_REQUEST);
    }
  }

  /***
   * Delete a login-role association. Query first the db repository by filtering by loginId and roleId,
   * execute the delete and check again if the removal has occurred as expected.
   * @param loginRoleRequestDTO the dto containing the loginId and roleId to be deleted
   * @return true or false
   */
  public ApiResponse<Boolean> processDelete(LoginRoleRequestDTO loginRoleRequestDTO) {
    Long loginId = loginRoleRequestDTO.getLoginId();
    Integer roleId = loginRoleRequestDTO.getRoleId();
    try {
      if (!getLoginRoleList(loginId, roleId).getPayload().isEmpty()) {
        // the loginRole entity exists, proceed to delete it:
        loginRoleRepository.delete(provideLoginRole(loginId, roleId));
        // the loginRole entity does not exist, it has been removed:
        return ApiResponse.create(getLoginRoleList(loginId, roleId).getPayload().isEmpty());
      } else {
        return ApiResponse.create(false);
      }
    } catch (Exception e) {
      String message = String.format(e.getMessage());
      logger.error(message);
      return ApiResponse.create(e);
    }
  }

    /***
   * Get all login-role associations filtering by roleId,
   * @param roleId the roleId to be looked up
   * @param enterpriseId the enterpriseId to be looked up
   * @return the ApiResponse with the appropriate payload
   */
  public ApiResponse<List<Long>> getAssignedLogins(Integer roleId, String enterpriseId) {
    try {
      List<Long> loginRoles = loginRoleRepository.findAssignedLogins(roleId, enterpriseId);
      if (!loginRoles.isEmpty()) {
        return ApiResponse.create(loginRoles);
      } else {
        return ApiResponse.create(null, false, "No login-roles associations found", HttpStatus.OK);
      }
    } catch (Exception e) {
      String message = String.format(e.getMessage());
      logger.error(message);
      return ApiResponse.create(e);
    }
  }

  /***
   * Looks up and retrieves a loginRole entity from the repository. It checks for an empty result,
   * and returns an ApiResponse with the appropriate payload attached. If the roleId argument is null,
   * it returns all loginRole entities for the loginId, found in the repository.
   * @param loginId the loginId to be looked up
   * @param roleId the roleId to be looked up
   * @return the ApiResponse with the appropriate payload
   */
  private ApiResponse<List<LoginRoleResponseDTO>> getLoginRoleList(Long loginId, Integer roleId) {
    Optional<List<LoginRole>> loginRoleList = loginRoleRepository.findLoginRolesByLoginLoginId(
        loginId);
    if (loginRoleList.isEmpty()) {
      String message = String.format("no login-roles associations found for loginId: %s", loginId);
      logger.debug(message);
      return ApiResponse.create(new ArrayList<>(), false, message, HttpStatus.NO_CONTENT);
    } else {
      List<LoginRole> loginRoles = loginRoleList.get();
      if (roleId == null) {
        // returned all roles found for the loginId:
        return ApiResponse.create(loginRoles.stream().map(mapper::fromEntity).toList());
      } else {
        // filter by the loginId argument:
        return ApiResponse.create(loginRoles.stream()
            .filter(roleCandidate -> Objects.equals(roleCandidate.getRole().getRoleId(), roleId))
            .map(mapper::fromEntity).toList());
      }
    }
  }


  private boolean lookupLoginRoleId(Long loginId, Integer roleId) {
    return getLoginRole(loginId, roleId).isPresent();
  }

  private Optional<LoginRole> getLoginRole(Long loginId, Integer roleId) {
    return loginRoleRepository.findByLoginIdAndRoleId(loginId, roleId);
  }

  private ApiResponse<List<LoginRoleResponseDTO>> getLoginRoleList(Long loginId) {
    return getLoginRoleList(loginId, null);
  }

  private LoginRole provideLoginRoleWithDate(Long loginId, int roleId) {
    var loginRole = provideLoginRole(loginId, roleId);
    loginRole.setDateCreated(ZonedDateTime.now());
    loginRole.setDateUpdated(ZonedDateTime.now());
    return loginRole;
  }

  private LoginRole provideLoginRole(Long loginId, int roleId) {
    return LoginRole.builder().login(Login.builder().loginId(loginId).build())
        .role(Role.builder().roleId(roleId).build()).build();
  }

  public ApiResponse<Boolean> mergeRoleSet(LoginRequestDTO loginRequestDTO, Login newLogin) {
    try {
      Boolean permissionMergeSuccess = this.loginRoleRepository.mergeRoleSet(newLogin.getLoginId(),
          this.objectMapper.writeValueAsString(loginRequestDTO.getRoles()));
      ApiResponse<Boolean> response = ApiResponse.create(permissionMergeSuccess);

      if (!permissionMergeSuccess) {
        response.setSuccessful(false);
        response.getErrors().add(
            ApiResponseError.builder().errorMessage("Error assigning role(s) to the login.")
                .build());
      }
      return response;
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }
}