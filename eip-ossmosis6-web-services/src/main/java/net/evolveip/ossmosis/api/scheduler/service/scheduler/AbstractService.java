package net.evolveip.ossmosis.api.scheduler.service.scheduler;

import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.core.InterfaceGenericService;
import net.evolveip.ossmosis.api.features.enterprise.service.EnterpriseService;
import net.evolveip.ossmosis.api.scheduler.common.TimeUtils;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public abstract class AbstractService implements BuilderHttpResponses, InterfaceGenericService,
    TimeUtils {

  protected final Scheduler scheduler;
  protected final EnterpriseService enterpriseService;

  @Autowired
  public AbstractService(Scheduler scheduler, EnterpriseService enterpriseService) {
    this.scheduler = scheduler;
    this.enterpriseService = enterpriseService;
  }

  protected <T> ApiResponse<T> badEnterprise(String enterpriseId) {
    String message = String.format("Enterprise with enterpriseId [%s] not found", enterpriseId);
    logger.info(message);
    return genericEmptyResponse(message);
  }

  protected <T> ApiResponse<T> noJobsFoundForEnterprise(String enterpriseId) {
    String message = String.format("No jobs found for enterpriseId [%s]", enterpriseId);
    logger.info(message);
    return genericEmptyResponse(message);
  }

  protected <T> ApiResponse<T> noJobIdForEnterprise(String enterpriseId, String jobId) {
    String message = String.format("JobId [%s] not found in enterprise [%s]", jobId, enterpriseId);
    logger.info(message);
    return genericEmptyResponse(message);
  }

  protected <T> ApiResponse<T> loggedExceptionResponse(Exception ex) {
    logger.error(ex.getMessage());
    return exceptionResponse(ex);
  }
}