package net.evolveip.ossmosis.api.features.audit.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.time.ZonedDateTime;
import java.util.Map;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Entity
@Table(name = "record_audit_log", schema = "audit")
@Data
public class RecordAuditLog {

  @Id
  @Column(name = "record_audit_log_id", columnDefinition = "bigint")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long recordAuditLogId;

  @Column(name = "record_id")
  private String recordId;

  @Column(name = "old_record_id")
  private String oldRecordId;

  @Column(name = "op", length = 8)
  private String op;

  @Column(name = "timestamp", nullable = false)
  @ColumnDefault("CURRENT_TIMESTAMP")
  private ZonedDateTime timestamp;

  @Column(name = "table_oid", nullable = false, columnDefinition = "oid")
  private Long tableOid;

  @Column(name = "table_schema", nullable = false, columnDefinition = "name")
  private String tableSchema;

  @Column(name = "table_name", nullable = false, columnDefinition = "name")
  private String tableName;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(name = "record", columnDefinition = "jsonb")
  private Map<String, Object> record;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(name = "old_record", columnDefinition = "jsonb")
  private Map<String, Object> oldRecord;

  @JoinColumn(name = "request_audit_log_id")
  @ManyToOne(fetch = FetchType.LAZY)
  private RequestAuditLog requestAuditLog;

}
