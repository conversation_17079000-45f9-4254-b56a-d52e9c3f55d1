package net.evolveip.ossmosis.api.features.platform.mapper;


import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformResponseDTO;
import net.evolveip.ossmosis.api.features.platform.entity.Platform;
import net.evolveip.ossmosis.api.features.platform.repository.PlatformRepository;
import org.mapstruct.ObjectFactory;
import org.mapstruct.TargetType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/***
 * Example:
 * <a href="https://stackoverflow.com/questions/42367081/map-a-dto-to-an-entity-retrieved-from-database-if-dto-has-id-using-mapstruct/42375045#42375045">...</a>
 */
@Slf4j
@Component
public class PlatformMapperResolver {

  @Autowired
  private PlatformRepository platformRepository;

  @ObjectFactory
  public Platform resolve(PlatformResponseDTO dto, @TargetType Class<Platform> type) {
    logger.debug(dto.toString());
    return dto != null && dto.getPlatformId() != 0 ? platformRepository.findByPlatformId(
        dto.getPlatformId()).get() : null;
  }

  @ObjectFactory
  public Platform resolve(Integer platformId, @TargetType Class<Platform> type) {
    if (platformId != null && platformId <= 0) {
      return null;
    }
    Optional<Platform> platformOptional = platformRepository.findByPlatformId(platformId);
    logger.debug(platformOptional.toString());
    return platformOptional.orElse(null);
  }
}