package net.evolveip.ossmosis.api.features.calldetails.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Table(name = "CALLTYPEDIMENSION")
@NoArgsConstructor
@AllArgsConstructor
public class CallTypeEntity {

  @Id
  @Column(name = "CT_ID")
  private Long id;

  @Column(name = "CT_DESCRIPTION")
  private String callType;
}
