package net.evolveip.ossmosis.api.scheduler.listener;

import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobListener;

@Slf4j
public class JobsListener implements JobListener {

  @Override
  public String getName() {
    return "JobsListener";
  }

  @Override
  public void jobToBeExecuted(JobExecutionContext context) {
    logger.info("Before Start Job");
    logDataMap(context);
    logJob<PERSON>ey(context);
  }

  @Override
  public void jobExecutionVetoed(JobExecutionContext context) {
    logger.info("Operation aborted");
    logDataMap(context);
    logJob<PERSON>ey(context);
  }

  @Override
  public void jobWasExecuted(JobExecutionContext context, JobExecutionException jobException) {
    logger.info("Job Was Executed");
    logDataMap(context);
    logJob<PERSON>ey(context);
  }

  private void logJobKey(JobExecutionContext context) {
    logger.info("jobKey : {}", context.getJobDetail().getKey());
  }

  private void logDataMap(JobExecutionContext context) {
    logger.debug("JobDataMap: {}", context.getMergedJobDataMap());
  }
}