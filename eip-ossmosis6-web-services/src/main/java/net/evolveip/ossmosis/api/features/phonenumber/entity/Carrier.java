package net.evolveip.ossmosis.api.features.phonenumber.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.ZonedDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "carrier",
    uniqueConstraints = {
        @UniqueConstraint(columnNames = "carrier_name"),
        @UniqueConstraint(columnNames = "carrier_label"),
        @UniqueConstraint(columnNames = {"carrier_name", "carrier_label"})
    }
)
@EqualsAndHashCode
public class Carrier {

  @Id
  @Column(name = "carrier_id", columnDefinition = "INT", nullable = false)
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer carrierId;

  @Column(name = "date_created", nullable = false, updatable = false)
  @EqualsAndHashCode.Exclude
  private ZonedDateTime dateCreated;

  @Column(name = "date_updated", nullable = false)
  @EqualsAndHashCode.Exclude
  private ZonedDateTime dateUpdated;

  @NotNull
  @Size(max = 20, message = "Carrier name must not exceed 20 characters")
  @Column(name = "carrier_name", length = 20, nullable = false, columnDefinition = "VARCHAR(20) CHECK (carrier_name NOT LIKE '% %')")
  private String carrierName;

  @NotNull
  @Size(max = 100, message = "Carrier label must not exceed 100 characters")
  @Column(name = "carrier_label", length = 100, nullable = false, columnDefinition = "VARCHAR(100)")
  private String carrierLabel;

  @PrePersist
  protected void onCreate() {
    ZonedDateTime now = ZonedDateTime.now();
    if (dateCreated == null) {
      dateCreated = now;
    }
    if (dateUpdated == null) {
      dateUpdated = now;
    }
  }

  @PreUpdate
  protected void onUpdate() {
    dateUpdated = ZonedDateTime.now();
  }
}