package net.evolveip.ossmosis.api.features.platform.controller;


import jakarta.validation.Valid;
import java.util.List;
import net.evolveip.ossmosis.api.features.enterprise.constant.EnterpriseConstants;
import net.evolveip.ossmosis.api.features.platform.constant.PlatformConstants;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformRequestDTO;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformResponseDTO;
import net.evolveip.ossmosis.api.features.platform.service.PlatformService;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidInteger;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/platforms")
@Validated
public class PlatformController {

  private final PlatformService platformService;

  @Autowired
  public PlatformController(PlatformService platformService) {
    this.platformService = platformService;
  }

  @Secured(PlatformConstants.PLATFORMS_READ)
  @GetMapping
  public ResponseEntity<Page<PlatformResponseDTO>> doGet(
      @RequestParam(value = "filterValue", required = false, defaultValue = "") String filterValue,
      @SortDefault(sort = "platformId", direction = Direction.ASC) @PageableDefault(size = 15) final
      Pageable pageable
  ) {
    Page<PlatformResponseDTO> page = platformService.processGet(filterValue, pageable);
    return ResponseEntity.ok(page);
  }

  @Secured({PlatformConstants.PLATFORMS_READ, EnterpriseConstants.ENTERPRISES_CREATE, EnterpriseConstants.ENTERPRISES_UPDATE})
  @GetMapping("/list")
  public ResponseEntity<ApiResponse<List<PlatformResponseDTO>>> doGetList() {
    ApiResponse<List<PlatformResponseDTO>> apiResponse =
        ApiResponse.create(platformService.processGetList());
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(PlatformConstants.PLATFORMS_READ)
  @GetMapping("/{platformId}")
  public ResponseEntity<ApiResponse<PlatformResponseDTO>> doGetById(
      @ValidInteger @PathVariable("platformId") final String platformId) {
    ApiResponse<PlatformResponseDTO> apiResponse = ApiResponse.create(
        platformService.processGetById(
            Integer.parseInt(platformId)));
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured({PlatformConstants.PLATFORMS_CREATE, PlatformConstants.PLATFORMS_READ})
  @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<ApiResponse<PlatformResponseDTO>> doCreate(
      @Valid @RequestBody final PlatformRequestDTO platformRequestDTO) {
    ApiResponse<PlatformResponseDTO> apiResponse = ApiResponse.create(platformService.processCreate(
        platformRequestDTO));
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured({PlatformConstants.PLATFORMS_UPDATE, PlatformConstants.PLATFORMS_READ})
  @PutMapping(value = "/{platformId}", consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<ApiResponse<PlatformResponseDTO>> doUpdate(
      @ValidInteger @PathVariable("platformId") final String platformId,
      @Valid @RequestBody final PlatformRequestDTO platformRequestDTO) {
    platformRequestDTO.setPlatformId(Integer.parseInt(platformId));
    ApiResponse<PlatformResponseDTO> apiResponse = ApiResponse.create(platformService.processUpdate(
        platformRequestDTO));
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  @Secured(PlatformConstants.PLATFORMS_DELETE)
  @DeleteMapping("/{platformId}")
  public ResponseEntity<Void> doDeleteById(
      @ValidInteger @PathVariable("platformId") final String platformId) {
    platformService.processDelete(Integer.parseInt(platformId));
    return ResponseEntity.ok().build();
  }
}