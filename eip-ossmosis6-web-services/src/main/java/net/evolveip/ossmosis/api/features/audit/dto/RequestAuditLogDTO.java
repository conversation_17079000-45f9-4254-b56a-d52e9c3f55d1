package net.evolveip.ossmosis.api.features.audit.dto;


import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseResponseDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginResponseDTO;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RequestAuditLogDTO {

  private Long auditLogId;

  private UUID requestId;

  private LoginResponseDTO login;

  private EnterpriseResponseDTO enterprise;

  private String path;

  private String method;

  private Map<String, Object> payload;

  private Map<String, Object> response;

  private ZonedDateTime timestamp;

  private Long durationInMs;

  private List<RecordAuditLogDTO> recordAuditLogs;

}
