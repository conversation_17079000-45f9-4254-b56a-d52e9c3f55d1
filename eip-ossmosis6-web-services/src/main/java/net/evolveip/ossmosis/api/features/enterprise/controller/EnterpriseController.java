package net.evolveip.ossmosis.api.features.enterprise.controller;


import jakarta.validation.Valid;
import java.util.List;
import net.evolveip.ossmosis.api.features.enterprise.constant.EnterpriseConstants;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseRequestDTO;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseResponseDTO;
import net.evolveip.ossmosis.api.features.enterprise.service.EnterpriseService;
import net.evolveip.ossmosis.api.features.enterprise.validator.annotations.ValidEnterprise;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.validators.annotations.EnterpriseId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RestController
@RequestMapping(value = "/enterprises")
public class EnterpriseController {

  private final EnterpriseService enterpriseService;

  @Autowired
  public EnterpriseController(EnterpriseService enterpriseService) {
    this.enterpriseService = enterpriseService;
  }

  /***
   * GET method that retrieves all the enterprises listed in the repository
   * @return a list of enterpriseDTOs
   */
  @Secured(EnterpriseConstants.ENTERPRISES_READ)
  @GetMapping
  public ResponseEntity<Page<EnterpriseResponseDTO>> doGet(
      @RequestParam(value = "filterValue", required = false, defaultValue = "") final String filterValue,
      @SortDefault(sort = "enterpriseId", direction = Direction.ASC) @PageableDefault(size = 15) final
      Pageable pageable
  ) {
    Page<EnterpriseResponseDTO> page = enterpriseService.processGet(filterValue, pageable);
    return ResponseEntity.ok(page);
  }

  @Secured(EnterpriseConstants.ENTERPRISES_READ)
  @GetMapping("/list")
  public ResponseEntity<ApiResponse<List<EnterpriseResponseDTO>>> doGetList() {
    ApiResponse<List<EnterpriseResponseDTO>> apiResponse =
        ApiResponse.create(enterpriseService.processGetList());
    return new ResponseEntity<>(apiResponse,
        HttpStatusCode.valueOf(apiResponse.getHttpStatusCode()));
  }

  /***
   * GET method that retrieves an enterprise based on the enterpriseId
   * @param enterpriseId an enterprise id to be looked up
   * @return an enterpriseDTO containing the details
   */
  @Secured(EnterpriseConstants.ENTERPRISES_READ)
  @GetMapping("/{enterpriseId}")
  public ResponseEntity<ApiResponse<EnterpriseResponseDTO>> doGetById(
      @EnterpriseId @PathVariable final String enterpriseId) {
    ApiResponse<EnterpriseResponseDTO> apiResponse =
        ApiResponse.create(enterpriseService.processGetById(enterpriseId));
    return new ResponseEntity<>(apiResponse, HttpStatus.valueOf(apiResponse.getHttpStatusCode()));
  }

  /***
   * POST method that creates an enterprise based on the incoming enterpriseDTO values
   *
   * @param enterpriseRequestDTO A dto containing the enterprise details
   * @return an enterprise response dto with the persisted data
   */
  @Secured({EnterpriseConstants.ENTERPRISES_CREATE, EnterpriseConstants.ENTERPRISES_READ})
  @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<?> doCreate(@Valid @RequestBody final EnterpriseRequestDTO enterpriseRequestDTO) {
    enterpriseService.processCreate(enterpriseRequestDTO);
    return ResponseEntity.accepted().build();
  }

  /***
   * PUT method that creates an enterprise
   *
   * @param enterpriseRequestDTO A dto containing the enterprise details
   * @return an enterprise response dto with the persisted data
   */
  @Secured({EnterpriseConstants.ENTERPRISES_UPDATE, EnterpriseConstants.ENTERPRISES_READ})
  @PutMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<?> doUpdate(@Valid @RequestBody final EnterpriseRequestDTO enterpriseRequestDTO) {
    enterpriseService.processUpdate(enterpriseRequestDTO);
    return ResponseEntity.accepted().build();
  }

  /***
   * DELETE method that deletes an enterprise
   *
   * @param enterpriseId the id of the enterprise that needs to be deleted
   * @return an ResponseEntity with a success (true) or failure (false) status
   */
  @Secured(EnterpriseConstants.ENTERPRISES_DELETE)
  @DeleteMapping("/{enterpriseId}")
  public ResponseEntity<?> doDelete(@ValidEnterprise @PathVariable String enterpriseId) {
    enterpriseService.processDelete(enterpriseId);
    return ResponseEntity.accepted().build();
  }
}