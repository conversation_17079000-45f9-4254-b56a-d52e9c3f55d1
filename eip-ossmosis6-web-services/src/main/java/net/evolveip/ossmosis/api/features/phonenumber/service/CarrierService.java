package net.evolveip.ossmosis.api.features.phonenumber.service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.CarrierDTO;
import net.evolveip.ossmosis.api.features.phonenumber.entity.Carrier;
import net.evolveip.ossmosis.api.features.phonenumber.mapper.CarrierMapper;
import net.evolveip.ossmosis.api.features.phonenumber.repository.CarrierRepository;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CarrierService extends BaseCarrierService {

  @Autowired
  public CarrierService(CarrierRepository carrierRepository, CarrierMapper mapper) {
    super(carrierRepository, mapper);
  }

  public ApiResponse<List<CarrierDTO>> processGet() {
    try {
      List<Carrier> carriers = carrierRepository.findAll();
      if (carriers.isEmpty()) {
        return notFoundResponse();
      }
      List<CarrierDTO> carrierDTOs = carriers.stream().map(mapper::toCarrierDto)
          .collect(Collectors.toList());
      return response(carrierDTOs);
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  public ApiResponse<CarrierDTO> processGetById(Integer carrierId) {
    try {
      Optional<Carrier> carrier = carrierRepository.findById(carrierId);
      if (carrier.isEmpty()) {
        return notFoundResponse(carrierId);
      } else {
        return response(mapper.toCarrierDto(carrier.get()));
      }
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  public ApiResponse<CarrierDTO> processGetByName(String carrierName) {
    try {
      Optional<Carrier> carrier = carrierRepository.findCarrierByCarrierName(carrierName);
      if (carrier.isEmpty()) {
        return notFoundResponse(carrierName);
      } else {
        return response(mapper.toCarrierDto(carrier.get()));
      }
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  public ApiResponse<CarrierDTO> processCreate(CarrierDTO candidateCarrierDTO) {
    try {
      String carrierName = candidateCarrierDTO.getCarrierName();
      Optional<Carrier> carrierDTO = carrierRepository.findCarrierByCarrierName(carrierName);
      if (carrierDTO.isPresent()) {
        return alreadyExistsResponse(carrierName);
      }
      Carrier carrier = mapper.toCarrier(candidateCarrierDTO);
      Carrier savedCarrier = carrierRepository.save(carrier);
      return response(mapper.toCarrierDto(savedCarrier));
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  public ApiResponse<CarrierDTO> processUpdateByCarrierId(Integer carrierId,
      CarrierDTO candidateCarrierDTO) {
    try {
      if (!carrierExists(carrierId)) {
        return notFoundResponse(carrierId);
      }
      return response(mapper.toCarrierDto(updateCarrier(carrierId, candidateCarrierDTO)));
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  public ApiResponse<CarrierDTO> processUpdateByCarrierName(String carrierName,
      CarrierDTO candidateCarrierDTO) {
    try {
      Optional<Carrier> existingCarrier = carrierRepository.findCarrierByCarrierName(carrierName);
      Integer carrierId;
      if (existingCarrier.isEmpty()) {
        return notFoundResponse(carrierName);
      } else {
        carrierId = existingCarrier.get().getCarrierId();
      }
      return response(mapper.toCarrierDto(updateCarrier(carrierId, candidateCarrierDTO)));
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  private Carrier updateCarrier(Integer carrierId, CarrierDTO candidateCarrierDTO) {
    Carrier candidateCarrier = mapper.toCarrier(candidateCarrierDTO);
    candidateCarrier.setCarrierId(carrierId);
    return carrierRepository.save(candidateCarrier);
  }

  public ApiResponse<Boolean> processDelete(Integer carrierId) {
    try {
      if (!carrierExists(carrierId)) {
        return notFoundResponse(carrierId);
      }
      carrierRepository.deleteById(carrierId);
      return response(true);
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }
}