package net.evolveip.ossmosis.api.features.permission.repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import net.evolveip.ossmosis.api.features.permission.entity.Permission;
import org.springframework.data.jpa.repository.JpaRepository;

public interface PermissionRepository extends JpaRepository<Permission, Integer> {

  Optional<Permission> findByPermissionId(Integer permissionId);

  List<Permission> findByPermissionNameIn(Collection<String> permissionName);

}
