package net.evolveip.ossmosis.api.features.platform.mapper;

import java.util.List;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformRequestDTO;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformResponseDTO;
import net.evolveip.ossmosis.api.features.platform.entity.Platform;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = PlatformMapperResolver.class, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface PlatformMapper {

  @Mapping(source = "platformId", target = "platformId")
  @Mapping(source = "platformName", target = "platformName")
  Platform toPlatform(PlatformRequestDTO platformDTO);

  @InheritInverseConfiguration
  PlatformResponseDTO toPlatformDTO(Platform platform);

  List<PlatformResponseDTO> entitiesToDTOs(List<Platform> platforms);
}