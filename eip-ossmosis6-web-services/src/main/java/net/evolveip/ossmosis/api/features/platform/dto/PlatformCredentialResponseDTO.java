package net.evolveip.ossmosis.api.features.platform.dto;

import java.time.ZonedDateTime;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PlatformCredentialResponseDTO {

  private Integer platformCredentialId;

  private String hostname;

  private String username;

  private String password;

  private Integer port;

  private Integer timeout;

  private Integer requestsPerSecond;

  private Boolean enabled;

  private String type;

  private Integer platformId;

  private Map<String, Object> extra;

  private ZonedDateTime dateCreated;

  private ZonedDateTime dateUpdated;
}
