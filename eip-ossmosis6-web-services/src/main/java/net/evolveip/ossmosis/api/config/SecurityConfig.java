package net.evolveip.ossmosis.api.config;

import java.util.Arrays;
import net.evolveip.ossmosis.api.config.auth.DynamicJwtDecoder;
import net.evolveip.ossmosis.api.config.auth.OssmosisJwtAuthenticationConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(
    securedEnabled = true
)
public class SecurityConfig {

  @Value("${spring.cors.allowed-headers}")
  private String[] corsAllowedHeaders;

  @Value("${spring.cors.allowed-methods}")
  private String[] corsAllowedMethods;

  @Value("${spring.cors.allowed-origins}")
  private String[] corsAllowedOrigins;

  @Value("${ossmosis.springdoc.allowed-paths}")
  private String[] swaggerPathsToAllow;

  @Value("${spring.cors.max-age}")
  private Long corsMaxAge;

  private final DynamicJwtDecoder dynamicJwtDecoder;;
  private final OssmosisJwtAuthenticationConverter ossmosisJwtAuthenticationConverter;

  @Autowired
  public SecurityConfig(DynamicJwtDecoder dynamicJwtDecoder,
      OssmosisJwtAuthenticationConverter ossmosisJwtAuthenticationConverter) {
    this.dynamicJwtDecoder = dynamicJwtDecoder;
    this.ossmosisJwtAuthenticationConverter = ossmosisJwtAuthenticationConverter;
  }

  @Bean
  public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
    http
        .cors(httpSecurityCorsConfigurer -> httpSecurityCorsConfigurer.configurationSource(
            corsConfigurationSource()))
        .csrf(AbstractHttpConfigurer::disable)
        .authorizeHttpRequests(
            (authorize) ->
                authorize
                    //TODO: who gets access to the swagger docs
                    .requestMatchers(swaggerPathsToAllow).permitAll()
                    .anyRequest().authenticated()
        )
        .oauth2ResourceServer((oauth2) -> oauth2.jwt(jwtConfigurer -> {
          jwtConfigurer.decoder(dynamicJwtDecoder);
          jwtConfigurer.jwtAuthenticationConverter(ossmosisJwtAuthenticationConverter);
        }));
    return http.build();
  }

  @Bean
  public CorsConfigurationSource corsConfigurationSource() {
    CorsConfiguration corsConfiguration = new CorsConfiguration();
    corsConfiguration.setAllowedOrigins(Arrays.asList(corsAllowedOrigins));
    corsConfiguration.setAllowedMethods(Arrays.asList(corsAllowedMethods));
    corsConfiguration.setAllowedHeaders(Arrays.asList(corsAllowedHeaders));
    corsConfiguration.setMaxAge(corsMaxAge);

    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    source.registerCorsConfiguration("/**", corsConfiguration);
    return source;
  }
}
