package net.evolveip.ossmosis.api.features.phonenumber.common.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class PhoneNumberValidationResponseDTO {

  private List<ValidPhoneNumberDTO> validPhoneNumbers = new ArrayList<>();
  private List<InvalidPhoneNumberDTO> invalidPhoneNumbers = new ArrayList<>();

  public void addValidPhoneNumber(ValidPhoneNumberDTO phoneNumberDTO) {
    validPhoneNumbers.add(phoneNumberDTO);
  }

  public void addInvalidPhoneNumber(InvalidPhoneNumberDTO phoneNumberDTO) {
    invalidPhoneNumbers.add(phoneNumberDTO);
  }
}