package net.evolveip.ossmosis.api.scheduler.mapper;

import net.evolveip.ossmosis.api.scheduler.common.dto.SchedulerMetadataDTO;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.factory.Mappers;
import org.quartz.SchedulerMetaData;

@Mapper(
    componentModel = ComponentModel.SPRING,
    injectionStrategy = InjectionStrategy.CONSTRUCTOR
)
public interface MapperSchedulerMetadata {

  MapperSchedulerMetadata INSTANCE = Mappers.getMapper(MapperSchedulerMetadata.class);


  @Mapping(target = "schedulerName", expression = "java(source.getSchedulerName())")
  @Mapping(target = "version", expression = "java(source.getVersion())")
  @Mapping(target = "schedulerInstanceId", expression = "java(source.getSchedulerInstanceId())")
  @Mapping(target = "started", expression = "java(source.isStarted())")
  @Mapping(target = "shutdown", expression = "java(source.isShutdown())")
  @Mapping(target = "inStandbyMode", expression = "java(source.isInStandbyMode())")
  @Mapping(target = "startTime", expression = "java(source.getRunningSince())")
  @Mapping(target = "schedulerClass", expression = "java(source.getSchedulerClass().getName())")
  @Mapping(target = "jobStoreClass", expression = "java(source.getJobStoreClass().getName())")
  @Mapping(target = "threadPoolClass", expression = "java(source.getThreadPoolClass().getName())")
  @Mapping(target = "threadPoolSize", expression = "java(source.getThreadPoolSize())")
  @Mapping(target = "schedulerRemote", expression = "java(source.isSchedulerRemote())")
  @Mapping(target = "numberOfJobsExecuted", expression = "java(source.getNumberOfJobsExecuted())")
  @Mapping(target = "jobStoreSupportsPersistence", expression = "java(source.isJobStoreSupportsPersistence())")
  @Mapping(target = "jobStoreClustered", expression = "java(source.isJobStoreClustered())")
  SchedulerMetadataDTO toDto(SchedulerMetaData source);
}
