package net.evolveip.ossmosis.api.features.login.repository;

import java.util.List;
import java.util.Optional;
import net.evolveip.ossmosis.api.features.login.entity.Login;
import net.evolveip.ossmosis.api.features.login.entity.view.LoginView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface LoginRepository extends JpaRepository<Login, Long> {

  Optional<Login> findLoginByLoginIdAndLoginPrimaryEnterpriseEnterpriseId(Long loginId,
      String enterpriseId);

  @Query(value = "SELECT distinct l from Login l left join fetch l.loginPrimaryEnterprise left join fetch l.roles "
      + "left join fetch l.userPermissionList perList left join fetch perList.resource left join fetch perList.permission "
      + "where l.loginEmail = :loginEmail")
  Optional<LoginView> findLoginByLoginEmailForLoginOnly(@Param("loginEmail") String loginEmail);

  @Query(value = "SELECT distinct l from Login l left join fetch l.loginPrimaryEnterprise left join fetch l.roles "
      + "left join fetch l.userPermissionList perList left join fetch perList.resource left join fetch perList.permission "
      + "where l.loginEmail = :loginEmail and l.loginPrimaryEnterprise.enterpriseId = :enterpriseId")
  Optional<LoginView> findLoginByLoginEmail(@Param("loginEmail") String loginEmail,
      String enterpriseId);

  @Query(value = "SELECT l from Login l join fetch l.loginPrimaryEnterprise left join fetch l.roles "
      + "left join fetch l.userPermissionList perList left join fetch perList.resource left join fetch perList.permission"
      + " where l.loginPrimaryEnterprise.enterpriseId in :enterpriseIds")
  List<LoginView> findAllWithDataPopulated(List<String> enterpriseIds);

  @Query(value = "SELECT l from Login l join fetch l.loginPrimaryEnterprise left join fetch l.roles "
      + "left join fetch l.userPermissionList perList left join fetch perList.resource left join fetch perList.permission"
      + " where l.loginPrimaryEnterprise.enterpriseId = :enterpriseId")
  List<LoginView> findAllForEnterpriseWithDataPopulated(String enterpriseId);

  @Query(value = "SELECT COUNT(l) from Login l where l.loginPrimaryEnterprise.enterpriseId = :enterpriseId and (lower(l.loginEmail) like %:filterValue% or lower(l.loginNameFirst) like %:filterValue% or lower(l.loginNameLast) like %:filterValue% or lower(l.loginPrimaryEnterprise.enterpriseId) like %:filterValue% or lower(l.loginGroup) like %:filterValue% or lower(l.loginPhoneNumber) like %:filterValue%)")
  long findFilteredCountForEnterpriseWithDataPopulated(String enterpriseId, String filterValue);

  @Query(value = "SELECT l from Login l join fetch l.loginPrimaryEnterprise left join fetch l.roles "
      + "left join fetch l.userPermissionList perList left join fetch perList.resource left join fetch perList.permission"
      + " where l.loginPrimaryEnterprise.enterpriseId = :enterpriseId and (lower(l.loginEmail) like %:filterValue% or lower(l.loginNameFirst) like %:filterValue% or lower(l.loginNameLast) like %:filterValue% or lower(l.loginPrimaryEnterprise.enterpriseId) like %:filterValue% or lower(l.loginGroup) like %:filterValue% or lower(l.loginPhoneNumber) like %:filterValue%)")
  Page<LoginView> findAllByFilteredPage(String enterpriseId, String filterValue, Pageable pageable);

  @Query(nativeQuery = true, value = "SELECT * from partnerprovider.fn_logins_create(:jsonText, :loginId)")
  List<Login> createLogins(String jsonText, Long loginId);

  @Query(nativeQuery = true, value = "SELECT * from partnerprovider.fn_logins_update(:jsonText, :loginId)")
  List<Login> updateLogins(String jsonText, Long loginId);

  @Modifying
  @Transactional
  @Query("UPDATE Login l SET l.loginPrimaryEnterprise.enterpriseId = :enterpriseId WHERE l.loginId = :loginId")
  int updateLoginEnterprise(@Param("loginId") Long loginId, @Param("enterpriseId") String enterpriseId);
}
