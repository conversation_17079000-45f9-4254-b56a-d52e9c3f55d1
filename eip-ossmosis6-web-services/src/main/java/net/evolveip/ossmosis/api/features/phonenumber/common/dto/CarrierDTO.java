package net.evolveip.ossmosis.api.features.phonenumber.common.dto;

import java.time.ZonedDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CarrierDTO {

  @EqualsAndHashCode.Exclude
  private Integer carrierId;
  @EqualsAndHashCode.Exclude
  private ZonedDateTime dateCreated;
  @EqualsAndHashCode.Exclude
  private ZonedDateTime dateUpdated;
  private String carrierName;
  @EqualsAndHashCode.Exclude
  private String carrierLabel;
}
