package net.evolveip.ossmosis.api.features.phonenumber.common;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface DtoUtils {

  default <T, R> Set<R> extractSliceToSet(Collection<T> collection, Function<T, R> extractor) {
    return new HashSet<>(extractSliceToList(collection, extractor));
  }

  default <T, R> List<R> extractSliceToList(Collection<T> collection, Function<T, R> extractor) {
    return collection.stream()
        .filter(Objects::nonNull)
        .map(extractor)
        .filter(Objects::nonNull)
        .toList();
  }

  // goes over a list argument and attempts to add its elements into a set.
  // if set-add operation returns a false, the element is collected in a duplicates set and the
  // duplicates set returned:
  default <T> Set<T> hasDuplicates(Collection<T> collection) {
    Set<T> uniques = new HashSet<>();
    return collection.stream()
        .filter(e -> !uniques.add(e))
        .collect(Collectors.toUnmodifiableSet());
  }
}