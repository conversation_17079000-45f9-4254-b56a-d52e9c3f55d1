package net.evolveip.ossmosis.api.scheduler.service.trigger;

import java.text.ParseException;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.Set;
import java.util.TimeZone;
import java.util.stream.Collectors;
import net.evolveip.ossmosis.api.scheduler.common.dto.CronScheduleDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.dto.SimpleScheduleDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.enums.WeekDays;
import org.hibernate.procedure.ParameterMisuseException;
import org.quartz.CalendarIntervalScheduleBuilder;
import org.quartz.CalendarIntervalTrigger;
import org.quartz.CronExpression;
import org.quartz.CronTrigger;
import org.quartz.JobDetail;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SimpleTrigger;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.quartz.TriggerKey;
import org.quartz.impl.calendar.MonthlyCalendar;
import org.quartz.impl.calendar.WeeklyCalendar;
import org.springframework.scheduling.quartz.CronTriggerFactoryBean;
import org.springframework.stereotype.Service;

@Service
public class TriggerBuilderService {


  public Trigger buildTrigger(
      Scheduler scheduler,
      JobDetail jobDetail,
      SimpleScheduleDefinitionDTO entryDTO) throws ParameterMisuseException, SchedulerException {

    ZoneId userZoneId = entryDTO.getUserTimeZone();
    TimeZone userTimeZoneZone = TimeZone.getTimeZone(userZoneId);
    LocalTime reportRunTime = entryDTO.getReportRunTime();
    Integer frequency = entryDTO.getFrequency();
    ChronoUnit frequencyUnit = entryDTO.getFrequencyUnits();
    Set<WeekDays> selectedWeekRunDays = entryDTO.getSelectedWeekRunDays();
    Set<Integer> selectedMonthRunDays = entryDTO.getSelectedMonthRunDays();

    TriggerKey triggerKey = TriggerKey.triggerKey(jobDetail.getKey().getName(),
        jobDetail.getKey().getGroup());
    ZonedDateTime runTime = ZonedDateTime.of(LocalDate.now(), reportRunTime, userZoneId);

    // Creating the SimpleScheduleBuilder based on frequency units
    CalendarIntervalScheduleBuilder scheduleBuilder = createScheduleBuilder(frequency, frequencyUnit);

    // Creating the trigger
    TriggerBuilder<CalendarIntervalTrigger> triggerBuilder = TriggerBuilder
        .newTrigger()
        .withIdentity(triggerKey)
        .startAt(getNextRunTime(runTime, frequency, frequencyUnit, selectedWeekRunDays, selectedMonthRunDays))
        .withSchedule(scheduleBuilder);

    // Add calendar to exclude certain days if selectedWeekRunDays is not empty
    if (selectedWeekRunDays != null && !selectedWeekRunDays.isEmpty()) {
      Set<Integer> daysToRun = selectedWeekRunDays.stream()
          .map(WeekDays::getCalendarDay)
          .collect(Collectors.toSet());
      WeeklyCalendar weeklyCalendar = createWeeklyCalendar(daysToRun, userTimeZoneZone);
      scheduler.addCalendar(jobDetail.getKey().getName(), weeklyCalendar, false, false);
      triggerBuilder.modifiedByCalendar(jobDetail.getKey().getName());
    }

    if (selectedMonthRunDays != null && !selectedMonthRunDays.isEmpty()) {
      MonthlyCalendar monthlyCalendar = createMonthlyCalendar(selectedMonthRunDays,
          userTimeZoneZone);
      scheduler.addCalendar(jobDetail.getKey().getName(), monthlyCalendar, false, false);
      triggerBuilder.modifiedByCalendar(jobDetail.getKey().getName());
    }
    return triggerBuilder.build();
  }

  public CronTrigger buildTrigger(JobDetail jobDetail,
      CronScheduleDefinitionDTO candidateCronScheduleDefinitionDTO) throws ParseException {
    CronExpression cronExpression = new CronExpression(
        candidateCronScheduleDefinitionDTO.getCronJobExpression());

    CronTriggerFactoryBean cronTriggerFactory = new CronTriggerFactoryBean();
    cronTriggerFactory.setJobDetail(jobDetail);
    cronTriggerFactory.setName(jobDetail.getKey().getName());
    cronTriggerFactory.setGroup(jobDetail.getKey().getGroup());
    cronTriggerFactory.setCronExpression(cronExpression.getCronExpression());
    cronTriggerFactory.setTimeZone(
        TimeZone.getTimeZone(
            String.valueOf(candidateCronScheduleDefinitionDTO.getUserTimeZone()))
    );
    cronTriggerFactory.setMisfireInstruction(
        SimpleTrigger.MISFIRE_INSTRUCTION_RESCHEDULE_NOW_WITH_EXISTING_REPEAT_COUNT);
    cronTriggerFactory.afterPropertiesSet();

    return cronTriggerFactory.getObject();
  }

  private Date getNextRunTime(ZonedDateTime runTime, Integer frequency, ChronoUnit frequencyUnit, Set<WeekDays> selectedWeekRunDays, Set<Integer> selectedMonthRunDays) {
    ZonedDateTime now = ZonedDateTime.now();

    Integer dayOfMonth = null;
    if (selectedMonthRunDays != null && !selectedMonthRunDays.isEmpty()) {
      dayOfMonth = selectedMonthRunDays.iterator().next();
    }
    if (dayOfMonth != null && frequencyUnit == ChronoUnit.MONTHS) {
      runTime = runTime.withDayOfMonth(dayOfMonth);
    }

    while (runTime.isBefore(now)) {
      switch (frequencyUnit) {
        case MINUTES:
          runTime = runTime.plusMinutes(frequency);
          break;
        case HOURS:
          runTime = runTime.plusHours(frequency);
          break;
        case DAYS:
          runTime = runTime.plusDays(frequency);
          break;
        case WEEKS:
          WeekDays dayOfWeek = selectedWeekRunDays.iterator().next();
          runTime = runTime.with(TemporalAdjusters.next(DayOfWeek.valueOf(dayOfWeek.toString())));
          break;
        case MONTHS:
          runTime = runTime.plusMonths(1);
          break;
      }
    }
    return Date.from(runTime.toInstant());
  }


  private CalendarIntervalScheduleBuilder createScheduleBuilder(Integer frequency,
      ChronoUnit frequencyUnit) {
    return switch (frequencyUnit) {
      case MINUTES -> CalendarIntervalScheduleBuilder
          .calendarIntervalSchedule()
          .withIntervalInMinutes(frequency);
      case HOURS -> CalendarIntervalScheduleBuilder
          .calendarIntervalSchedule()
          .withIntervalInHours(frequency);
      case DAYS -> CalendarIntervalScheduleBuilder
          .calendarIntervalSchedule()
          .withIntervalInDays(frequency);
      case WEEKS -> CalendarIntervalScheduleBuilder
          .calendarIntervalSchedule()
          .withIntervalInWeeks(frequency);
      case MONTHS -> CalendarIntervalScheduleBuilder
          .calendarIntervalSchedule()
          .withIntervalInMonths(frequency);
      default -> throw new IllegalArgumentException("Unsupported frequency unit: " + frequencyUnit);
    };
  }

  private WeeklyCalendar createWeeklyCalendar(Set<Integer> daysToRun, TimeZone timeZone) {
    WeeklyCalendar weeklyCalendar = new WeeklyCalendar();
    weeklyCalendar.setTimeZone(timeZone);
    for (int i = WeekDays.SUNDAY.getCalendarDay(); i <= WeekDays.SATURDAY.getCalendarDay(); i++) {
      weeklyCalendar.setDayExcluded(i, true);
    }
    for (Integer day : daysToRun) {
      weeklyCalendar.setDayExcluded(day, false);
    }
    return weeklyCalendar;
  }

  private MonthlyCalendar createMonthlyCalendar(Set<Integer> daysToRun, TimeZone timeZone) {
    MonthlyCalendar monthlyCalendar = new MonthlyCalendar();
    monthlyCalendar.setTimeZone(timeZone);
    for (int i = 1; i <= 31; i++) {
      monthlyCalendar.setDayExcluded(i, true);
    }

    for (Integer day : daysToRun) {
      monthlyCalendar.setDayExcluded(day, false);
    }
    return monthlyCalendar;
  }
}