package net.evolveip.ossmosis.api.features.phonenumber.service;

import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.PhoneNumberStatusConstants.ACTIVE;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.PhoneNumberStatusConstants.INACTIVE;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.PhoneNumberStatusConstants.ON_CARRIER;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.PhoneNumberStatusConstants.PENDING;
import static net.evolveip.ossmosis.api.features.phonenumber.common.constants.PhoneNumberStatusConstants.PENDING_PORT;

import java.util.List;
import net.evolveip.ossmosis.api.features.phonenumber.entity.PhoneNumberStatus;
import net.evolveip.ossmosis.api.features.phonenumber.mapper.PhoneNumberStatusMapper;
import net.evolveip.ossmosis.api.features.phonenumber.repository.PhoneNumberStatusRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DefaultPhoneNumberStatusService extends BasePhoneNumberStatusService {

  private final List<String> defaultPhoneNumberStatuses = List.of(
      ACTIVE,
      INACTIVE,
      PENDING,
      ON_CARRIER,
      PENDING_PORT
  );

  @Autowired
  public DefaultPhoneNumberStatusService(
      PhoneNumberStatusRepository phoneNumberStatusRepository,
      PhoneNumberStatusMapper mapper) {
    super(phoneNumberStatusRepository, mapper);
  }

  public void createDefaultPhoneNumberStatus() {
    for (String statusName : defaultPhoneNumberStatuses) {
      if (phoneNumberStatusRepository.findPhoneNumberStatusByStatusName(statusName).isEmpty()) {
        PhoneNumberStatus phoneNumberStatus = PhoneNumberStatus
            .builder()
            .statusName(statusName)
            .build();
        phoneNumberStatusRepository.save(phoneNumberStatus);
      }
    }
  }
}
