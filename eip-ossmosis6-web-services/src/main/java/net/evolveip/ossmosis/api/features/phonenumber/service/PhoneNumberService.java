package net.evolveip.ossmosis.api.features.phonenumber.service;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import jakarta.persistence.EntityNotFoundException;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import net.evolveip.ossmosis.api.config.csv.CSVTempLocationPropertiesConfig;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.enterprise.repository.EnterpriseRepository;
import net.evolveip.ossmosis.api.features.group.service.GroupService;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.HttpPostRequestPhoneNumberDTO;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.HttpPutRequestPhoneNumberDTO;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.HttpRequestPhoneNumberDTO;
import net.evolveip.ossmosis.api.features.phonenumber.common.dto.HttpResponsePhoneNumberDTO;
import net.evolveip.ossmosis.api.features.phonenumber.entity.Carrier;
import net.evolveip.ossmosis.api.features.phonenumber.entity.PhoneNumber;
import net.evolveip.ossmosis.api.features.phonenumber.entity.PhoneNumberStatus;
import net.evolveip.ossmosis.api.features.phonenumber.mapper.PhoneNumberMapper;
import net.evolveip.ossmosis.api.features.phonenumber.repository.PhoneNumberRepository;
import net.evolveip.ossmosis.api.utils.exceptions.OssmosisDataIntegrityViolation;
import net.evolveip.ossmosis.api.utils.file.CSVService;
import net.evolveip.ossmosis.api.utils.file.PDFService;
import net.evolveip.ossmosis.api.utils.file.XLSXService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.service.AuthorizationService;
import net.evolveip.ossmosis.api.utils.exceptions.DuplicateFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.NestedExceptionUtils;
import org.springframework.core.NestedRuntimeException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PhoneNumberService extends BasePhoneNumberService {

  private final PhoneNumberUtil phoneNumberUtil;
  private final CSVTempLocationPropertiesConfig tempDir;
  private final ObjectMapper objectMapper;

  @Autowired
  public PhoneNumberService(PhoneNumberRepository phoneNumberRepository,
      AuthorizationService authorizationService, CarrierService carrierService, EnterpriseRepository enterpriseRepository,
      PhoneNumberStatusService phoneNumberStatusService, PhoneNumberMapper mapper,
      PhoneNumberUtil phoneNumberUtil, CSVTempLocationPropertiesConfig tempDir,
      ObjectMapper objectMapper, GroupService groupService) {
    super(phoneNumberRepository, authorizationService, carrierService,
        enterpriseRepository, phoneNumberStatusService, mapper);
    this.phoneNumberUtil = phoneNumberUtil;
    this.tempDir = tempDir;
    this.objectMapper = objectMapper;
    this.objectMapper.registerModule(new JavaTimeModule());
  }

  /**
   * Return a list of all phone numbers
   *
   * @param enterpriseId the enterprise id for which to retrieve phone numbers
   * @return a list of phone numbers
   */
  public ApiResponse<?> processGet(String enterpriseId, String filterValue, final Pageable pageable) {
    try {
      ApiResponse<?> apiResponse;
      apiResponse = validateAuthorization(enterpriseId);
      if (apiResponse != null) {
        return apiResponse;
      }

      Page<PhoneNumber> page = phoneNumberRepository.findAllByEnterpriseIdAndFilterValue(enterpriseId, filterValue.toLowerCase(), pageable);
      List<HttpResponsePhoneNumberDTO> phoneNumberDTOS = page.getContent().stream()
          .map(mapper::toHttpResponsePhoneNumberDto).toList();
      return response(new PageImpl<>(phoneNumberDTOS, pageable, page.getTotalElements()));
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  public ApiResponse<HttpResponsePhoneNumberDTO> processGetById(String enterpriseId,
      Long phoneNumberId) {
    try {
      ApiResponse<HttpResponsePhoneNumberDTO> apiResponse;
      // check if current user has the privilege to access the enterpriseId specified URI:
      apiResponse = validateAuthorization(enterpriseId);
      if (apiResponse != null) {
        return apiResponse;
      }
      apiResponse = validatePhoneNumberId(enterpriseId, phoneNumberId);
      if (apiResponse != null) {
        return apiResponse;
      }
      PhoneNumber phoneNumber = phoneNumberRepository.findByEnterpriseEnterpriseIdAndPhoneNumberId(
          enterpriseId, phoneNumberId).orElseThrow();
      return response(mapper.toHttpResponsePhoneNumberDto(phoneNumber));
    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  @Transactional(rollbackFor = NestedRuntimeException.class)
  public ApiResponse<List<HttpResponsePhoneNumberDTO>> processCreate(String enterpriseId,
      List<HttpPostRequestPhoneNumberDTO> request) {
    try {
      ApiResponse<List<HttpResponsePhoneNumberDTO>> apiResponse;

      // check if current user has the privilege to access the enterpriseId specified URI:
      apiResponse = validateAuthorization(enterpriseId);
      if (apiResponse != null) {
        return apiResponse;
      }

      apiResponse = validatePostRequest(request);
      if (apiResponse != null) {
        return apiResponse;
      }

      // validate and filter existing phone numbers
      List<PhoneNumber> finalCandidatePhoneNumbers = filterExistingNumbers(
          populateEntities(request), false);
      //... exit if no phone number is left to be added:
      if (finalCandidatePhoneNumbers.isEmpty()) {
        return emptyCandidateListResponse();
      }

      List<PhoneNumber> savedPhoneNumbers = phoneNumberRepository.saveAll(
          finalCandidatePhoneNumbers);

      return response(
          savedPhoneNumbers.stream().map(mapper::toHttpResponsePhoneNumberDto).toList());

    } catch (Exception e) {

      if (e instanceof DataIntegrityViolationException) {
        Throwable rootCause = NestedExceptionUtils.getMostSpecificCause(e);
        String message = rootCause != null ? rootCause.getMessage() : e.getMessage();

        throw new OssmosisDataIntegrityViolation(message);
      }

      return loggedExceptionResponse(e);
    }
  }

  @Transactional
  public ApiResponse<List<HttpResponsePhoneNumberDTO>> processUpdate(String enterpriseId,
      List<HttpPutRequestPhoneNumberDTO> request) {
    try {
      ApiResponse<List<HttpResponsePhoneNumberDTO>> apiResponse;

      // check if current user has the privilege to access the enterpriseId specified URI:
      apiResponse = validateAuthorization(enterpriseId);
      if (apiResponse != null) {
        return apiResponse;
      }

      // check if the incoming phoneNumberIds:
      // - have duplicates in the incoming list of dtos
      // - are valid (exist in the database)
      // - belong to the enterpriseId
      // - match their own countryCode and phoneNumber
      apiResponse = validatePhoneNumberIds(enterpriseId, request);
      if (apiResponse != null) {
        return apiResponse;
      }

      apiResponse = validatePutRequest(request);
      if (apiResponse != null) {
        return apiResponse;
      }

      // validate and filter existing phone numbers
      List<PhoneNumber> candidatePhoneNumbers = populateEntities(request);
      filterExistingNumbers(candidatePhoneNumbers, true);

      List<PhoneNumber> savedPhoneNumbers = candidatePhoneNumbers.stream()
          .map(this::updateSinglePhoneNumber).toList();

      return response(
          savedPhoneNumbers.stream().map(mapper::toHttpResponsePhoneNumberDto).toList());
    } catch (Exception e) {

      if (e instanceof DataIntegrityViolationException) {
        Throwable rootCause = NestedExceptionUtils.getMostSpecificCause(e);
        String message = rootCause != null ? rootCause.getMessage() : e.getMessage();

        throw new OssmosisDataIntegrityViolation(message);
      }

      return loggedExceptionResponse(e);
    }
  }

  @Transactional
  public ApiResponse<Boolean> processDelete(String enterpriseId, List<Long> phoneNumberIds) {
    try {
      ApiResponse<Boolean> apiResponse;
      // check if current user has the privilege to access the enterpriseId specified URI:
      apiResponse = validateAuthorization(enterpriseId);
      if (apiResponse != null) {
        return apiResponse;
      }

      // check if phoneNumberIds are valid:
      apiResponse = validatePhoneNumberIds(phoneNumberIds);
      if (apiResponse != null) {
        String error = apiResponse.getErrors().get(0).getErrorMessage();
        return invalidPhoneNumberIdsResponse(error);
      }

      phoneNumberRepository.deleteAllById(phoneNumberIds);
      return response(true);
    } catch (Exception e) {

      if (e instanceof DataIntegrityViolationException) {
        Throwable rootCause = NestedExceptionUtils.getMostSpecificCause(e);
        String message = rootCause != null ? rootCause.getMessage() : e.getMessage();

        throw new OssmosisDataIntegrityViolation(message);
      }

      return loggedExceptionResponse(e);
    }
  }

  @Transactional(rollbackFor = NestedRuntimeException.class)
  public PhoneNumber updateSinglePhoneNumber(PhoneNumber phoneNumber) {
    Long phoneNumberId = phoneNumber.getPhoneNumberId();
    PhoneNumber existingPhoneNumber = phoneNumberRepository.findById(phoneNumber.getPhoneNumberId())
        .orElseThrow(() -> new EntityNotFoundException(
            String.format("Phone number id not found: %s", phoneNumberId)));

    if (!Objects.equals(phoneNumber.getAssignmentType(), "unassigned")) {
      Optional<PhoneNumber> phoneNumberAssignmentOptional = phoneNumberRepository.findPhoneNumberByEnterpriseEnterpriseIdAndAssignmentTypeAndAssignmentId(phoneNumber.getEnterprise().getEnterpriseId(),
          phoneNumber.getAssignmentType(), phoneNumber.getAssignmentId());

      phoneNumberAssignmentOptional.ifPresent(this::unassignPhoneNumber);
    }

    // Update existing phone number's fields:
    existingPhoneNumber.setAccountNumber(phoneNumber.getAccountNumber());
    existingPhoneNumber.setBtnCountryCode(phoneNumber.getBtnCountryCode());
    existingPhoneNumber.setBtnPhoneNumber(phoneNumber.getBtnPhoneNumber());
    existingPhoneNumber.setCarrier(phoneNumber.getCarrier());
    existingPhoneNumber.setStatus(phoneNumber.getStatus());
    existingPhoneNumber.setPin(phoneNumber.getPin());
    existingPhoneNumber.setServiceAddress(phoneNumber.getServiceAddress());
    existingPhoneNumber.setAssignmentType(phoneNumber.getAssignmentType());
    existingPhoneNumber.setAssignmentId(phoneNumber.getAssignmentId());
    existingPhoneNumber.setAssignmentName(phoneNumber.getAssignmentName());
    existingPhoneNumber.setCallingLineIdName(phoneNumber.getCallingLineIdName());
    existingPhoneNumber.setE911Address(phoneNumber.getE911Address());

    return phoneNumberRepository.save(existingPhoneNumber);
  }

  public ApiResponse<Boolean> processValidateById(String enterpriseId, Long phoneNumberId) {
    ApiResponse<Boolean> apiResponse = validateAuthorization(enterpriseId);
    if (apiResponse != null) {
      return apiResponse;
    }

    apiResponse = validatePhoneNumberId(enterpriseId, phoneNumberId);
    if (apiResponse != null) {
      return apiResponse;
    }

    PhoneNumber phoneNumberEntity = phoneNumberRepository.findByEnterpriseEnterpriseIdAndPhoneNumberId(
        enterpriseId, phoneNumberId).orElseThrow();

    Phonenumber.PhoneNumber phoneNumber = new Phonenumber.PhoneNumber();
    phoneNumber.setCountryCode(Integer.parseInt(phoneNumberEntity.getCountryCodeNoPlus()));
    phoneNumber.setNationalNumber(Long.parseLong(phoneNumberEntity.getPhoneNumber()));
    boolean isValid = phoneNumberUtil.isValidNumber(phoneNumber);
    return response(isValid);
  }

  public File processDownload(String enterpriseId, String filterValue, String format, String fileName, String timezone) {
    try {
      String newFileName = fileName + "." + format;
      File file;
      List<String> enterpriseIdsList;

      Page<PhoneNumber> phoneNumbers = phoneNumberRepository.findAllByEnterpriseIdAndFilterValue(enterpriseId, filterValue.toLowerCase(), null);
      List<HttpResponsePhoneNumberDTO> phoneNumberDTOS = phoneNumbers.getContent().stream().map(mapper::toHttpResponsePhoneNumberDto).toList();

      switch (format) {
        case "csv" -> {
            CSVService<HttpResponsePhoneNumberDTO> csvService = new CSVService<>();
            file = csvService.generateCsv(phoneNumberDTOS, tempDir, newFileName, timezone);
        }
        case "pdf" -> {
          PDFService<HttpResponsePhoneNumberDTO> pdfService = new PDFService<>();
          file = pdfService.generatePdf(phoneNumberDTOS, tempDir, newFileName,true,  timezone);
        }
        case "xlsx" -> {
          XLSXService<HttpResponsePhoneNumberDTO> xlsxService = new XLSXService<>();
          file = xlsxService.generateXlxs(phoneNumberDTOS, tempDir, newFileName, true, timezone);
        }
        default -> {
          CSVService<HttpResponsePhoneNumberDTO> csvService = new CSVService<>();
          file = csvService.generateCsv(phoneNumberDTOS, tempDir, newFileName, timezone);
        }
      }

      return file;
    } catch (Exception e) {
      String message = String.format(e.getMessage());
      return null;
    }
  }

  public ApiResponse<HttpResponsePhoneNumberDTO> processGetPhoneNumberAssignment(String enterpriseId, String assignmentType, String assignmentId) {
    try {
      Optional<PhoneNumber> phoneNumberOptional = phoneNumberRepository.findPhoneNumberByEnterpriseEnterpriseIdAndAssignmentTypeAndAssignmentId(enterpriseId, assignmentType, assignmentId);

      if (phoneNumberOptional.isEmpty()) {
        return ApiResponse.create(null, true, new ArrayList<>(), HttpStatus.OK);
      }

      return ApiResponse.create(mapper.toHttpResponsePhoneNumberDto(phoneNumberOptional.get()));

    } catch (Exception e) {
      return loggedExceptionResponse(e);
    }
  }

  // private methods section:
  private List<PhoneNumber> populateEntities(List<? extends HttpRequestPhoneNumberDTO> request) {
    return request.stream()
        .map(dto -> {
          PhoneNumber phoneNumber = populateEntity(dto);
          if (dto instanceof HttpPutRequestPhoneNumberDTO) {
            phoneNumber.setPhoneNumberId(
                Long.valueOf(dto.getPhoneNumberId()));
          }
          return phoneNumber;
        })
        .collect(Collectors.toList());
  }

  private PhoneNumber populateEntity(HttpRequestPhoneNumberDTO dto) {
    Enterprise enterprise = enterpriseRepository.findEnterpriseByEnterpriseId(dto.getEnterpriseId())
        .orElseThrow(
            () -> new EntityNotFoundException("Enterprise not found: " + dto.getEnterpriseId()));
    Carrier carrier = carrierService.carrierRepository.findById(Integer.valueOf(dto.getCarrierId()))
        .orElseThrow(() -> new EntityNotFoundException("Carrier not found: " + dto.getCarrierId()));
    PhoneNumberStatus phoneNumberStatus = phoneNumberStatusService.phoneNumberStatusRepository.findById(
            Integer.valueOf(dto.getStatusId()))
        .orElseThrow(() -> new EntityNotFoundException("Status not found: " + dto.getStatusId()));

    return PhoneNumber
        .builder()
        .countryCode(dto.getCountryCode())
        .phoneNumber(dto.getPhoneNumber())
        .enterprise(enterprise)
        .accountNumber(dto.getAccountNumber())
        .btnCountryCode(dto.getBtnCountryCode())
        .btnPhoneNumber(dto.getBtnPhoneNumber())
        .carrier(carrier)
        .status(phoneNumberStatus)
        .pin(dto.getPin())
        .serviceAddress(dto.getServiceAddress())
        .assignmentType(dto.getAssignmentType())
        .assignmentId(dto.getAssignmentId())
        .assignmentName(dto.getAssignmentName())
        .callingLineIdName(dto.getCallingLineIdName())
        .e911Address(dto.getE911Address())
        .build();
  }

  /***
   * Filter out the phone numbers that already exist in the database.
   * @param phoneNumbers a list of phone numbers to be checked against the database
   * @return a list of phone numbers that do not exist in the database
   */
  private List<PhoneNumber> filterExistingNumbers(List<PhoneNumber> phoneNumbers, boolean isUpdate) {
    List<PhoneNumber> duplicatePhoneNumbers = new ArrayList<>();

    // check if the phone number already exists in the database:
    for (PhoneNumber number : phoneNumbers) {
      String countryCodeString = number.getCountryCode();
      String phoneNumberString = number.getPhoneNumber();

      // The check is done by countryCode and phoneNumber for all enterprises,
      // not just the current one. The reason for this is that the phone number could be allocated
      // to another enterprise, and we need to prevent the same phone number from being allocated to
      // multiple enterprises.
      Optional<PhoneNumber> result = phoneNumberRepository.findByCountryCodeAndPhoneNumber(
          countryCodeString, phoneNumberString);
      
      if (result.isPresent()) {
        // For updates, we allow the same phone number if it's the same entity being updated
        if (isUpdate && result.get().getPhoneNumberId().equals(number.getPhoneNumberId())) {
          continue;
        }
        duplicatePhoneNumbers.add(number);
      }
    }

    // if duplicates found, throw exception with details
    if (!duplicatePhoneNumbers.isEmpty()) {
      List<String> duplicatesString = extractSliceToList(duplicatePhoneNumbers,
          PhoneNumber::getPString);
      String message = String.format("Phone number(s) already exist: %s",
          String.join(", ", duplicatesString));
      throw new DuplicateFoundException(message);
    }

    return phoneNumbers;
  }

  private void unassignPhoneNumber(PhoneNumber phoneNumber) {
      phoneNumber.setAssignmentType("unassigned");
      phoneNumber.setAssignmentId("");
      phoneNumber.setAssignmentName("");
      phoneNumber.setCallingLineIdName("");
      phoneNumber.setE911Address(null);

      phoneNumberRepository.saveAndFlush(phoneNumber);
  }
}