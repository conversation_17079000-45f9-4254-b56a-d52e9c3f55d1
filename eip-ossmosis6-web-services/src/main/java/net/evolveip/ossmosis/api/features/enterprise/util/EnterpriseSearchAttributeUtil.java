package net.evolveip.ossmosis.api.features.enterprise.util;


import io.temporal.common.SearchAttributeKey;
import io.temporal.common.SearchAttributes;
import java.util.Optional;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.enterprise.repository.EnterpriseRepository;
import org.springframework.stereotype.Component;

@Component
public class EnterpriseSearchAttributeUtil {

  private final EnterpriseRepository enterpriseRepository;

  public EnterpriseSearchAttributeUtil(EnterpriseRepository enterpriseRepository) {
    this.enterpriseRepository = enterpriseRepository;
  }

  public SearchAttributes buildEnterpriseSearchAttributes(String enterpriseId, String parentEnterpriseId) {
    String enterpriseIdPath = this.buildEnterpriseParentString(enterpriseId, parentEnterpriseId);

    return SearchAttributes.newBuilder()
        .set(SearchAttributeKey.forKeyword("enterpriseIdPath"), enterpriseIdPath)
        .build();
  }

  public String buildEnterpriseParentString(String currentEnterpriseId, String parentEnterpriseId) {
    StringBuilder builder = new StringBuilder();

    if (parentEnterpriseId != null) {
      builder.insert(0, currentEnterpriseId);
      currentEnterpriseId = parentEnterpriseId;
    }

    while (currentEnterpriseId != null) {
      Optional<Enterprise> enterpriseOpt = enterpriseRepository.findEnterpriseByEnterpriseId(currentEnterpriseId);
      if (enterpriseOpt.isEmpty()) break;

      Enterprise enterprise = enterpriseOpt.get();
      builder.insert(0, enterprise.getEnterpriseId() + ".");
      if (enterprise.getParentEnterprise() != null) {
        currentEnterpriseId = enterprise.getParentEnterprise().getEnterpriseId();
      } else {
        currentEnterpriseId = null;
      }
    }

    if (!builder.isEmpty()) {
      builder.deleteCharAt(builder.length() - 1);
    }

    return builder.toString();
  }
}
