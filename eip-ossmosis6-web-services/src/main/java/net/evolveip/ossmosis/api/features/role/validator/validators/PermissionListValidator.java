package net.evolveip.ossmosis.api.features.role.validator.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.List;
import net.evolveip.ossmosis.api.features.role.dto.RoleResourcePermissionRequestDTO;
import net.evolveip.ossmosis.api.features.role.validator.annotations.ValidPermissionList;

public class PermissionListValidator implements
    ConstraintValidator<ValidPermissionList, List<RoleResourcePermissionRequestDTO>> {

  @Override
  public boolean isValid(List<RoleResourcePermissionRequestDTO> permissionList,
      ConstraintValidatorContext context) {
    if (permissionList == null || permissionList.isEmpty()) {
      return false;
    }

    // Check if all elements are valid instances of RoleResourcePermissionRequestDTO
    for (Object obj : permissionList) {
      if (!(obj instanceof RoleResourcePermissionRequestDTO)) {
        return false;
      }
    }
    return true;
  }
}