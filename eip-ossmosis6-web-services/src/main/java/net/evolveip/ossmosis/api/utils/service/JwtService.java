package net.evolveip.ossmosis.api.utils.service;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.impl.DefaultClaims;

import java.time.Instant;
import java.util.Date;
import java.util.function.Function;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class JwtService {

    private final JwtDecoder jwtDecoder;


    @Autowired
    public JwtService(JwtDecoder jwtDecoder) {
        this.jwtDecoder = jwtDecoder;
    }


    public String extractUserEmail(String token) {
        return extractClaim(token, claims -> claims.get("email", String.class));
    }

    public String extractFirstName(String token) {
        return extractClaim(token, claims -> claims.get("first_name", String.class));
    }

    public String extractLastName(String token) {
        return extractClaim(token, claims -> claims.get("last_name", String.class));
    }

    public String extractEnterpriseId(String token) {
        return extractClaim(token, claims -> claims.get("enterprise_id", String.class));
    }

    public String extractUserName(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    public boolean isTokenValid(String token, UserDetails userDetails) {
        final String userEmail = extractUserEmail(token);
        return (userEmail.equals(userDetails.getUsername())) && !isTokenExpired(token);
    }

    private <T> T extractClaim(String token, Function<Claims, T> claimsResolvers) {
        final Claims claims = extractAllClaims(token);
        return claimsResolvers.apply(claims);
    }


    private boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    private Date extractExpiration(String token) {
        return extractClaim(token, claims -> {
            Instant date = claims.get("exp", Instant.class);
            return new Date(date.toEpochMilli());
        });
    }

    private Claims extractAllClaims(String token) {
        try {
            Jwt jwt = jwtDecoder.decode(token);
            return Jwts.claims(jwt.getClaims());
        } catch (Exception e) {
            logger.error("Could not verify JWT token integrity!", e);
        }
        return new DefaultClaims();
    }
}