package net.evolveip.ossmosis.api.features.role.mapper;

import java.util.List;
import net.evolveip.ossmosis.api.features.role.dto.RoleResourcePermissionResponseDTO;
import net.evolveip.ossmosis.api.features.role.entity.RoleResourcePermission;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;

@Mapper(componentModel = ComponentModel.SPRING)
public interface RoleResourcePermissionMapper {

  @Mapping(source = "permission.permissionId", target = "permissionId")
  @Mapping(source = "resource.resourceId", target = "resourceId")
  RoleResourcePermissionResponseDTO toRoleResourcePermissionResponseDTO(
      RoleResourcePermission roleResourcePermission);


  @Mapping(ignore = true, target = "roleResourcePermissionId")
  @Mapping(ignore = true, target = "roleId")
  @Mapping(source = "resource.resourceId", target = "resourceId")
  @Mapping(source = "permission.permissionId", target = "permissionId")
  List<RoleResourcePermissionResponseDTO> toDTOs(
      List<RoleResourcePermission> roleResourcePermissions);

}
