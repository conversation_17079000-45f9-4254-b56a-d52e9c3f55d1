package net.evolveip.ossmosis.api.scheduler.mapper;

import net.evolveip.ossmosis.api.scheduler.common.dto.EmailDefinitionDTO;
import net.evolveip.ossmosis.api.scheduler.common.request.HttpRequestCreateEmailDefinition;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = ComponentModel.SPRING, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface MapperEmailDefinition {

  MapperEmailDefinition INSTANCE = Mappers.getMapper(MapperEmailDefinition.class);

  @Mapping(source = "enterpriseId", target = "enterpriseId")
  @Mapping(ignore = true, target = "enterpriseName")
  @Mapping(ignore = true, target = "fromEmail")
  @Mapping(source = "toEmail", target = "toEmail")
  EmailDefinitionDTO toDto(HttpRequestCreateEmailDefinition entryHttpRequest);
}