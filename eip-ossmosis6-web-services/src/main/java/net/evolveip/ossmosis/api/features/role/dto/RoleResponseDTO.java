package net.evolveip.ossmosis.api.features.role.dto;

import java.time.ZonedDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Builder
public class RoleResponseDTO {

  private Integer roleId;

  private ZonedDateTime dateCreated;

  private ZonedDateTime dateUpdated;

  private String roleName;

  private String enterpriseId;

  private boolean readOnly;

  private List<RoleResourcePermissionResponseDTO> permissionList;

}
