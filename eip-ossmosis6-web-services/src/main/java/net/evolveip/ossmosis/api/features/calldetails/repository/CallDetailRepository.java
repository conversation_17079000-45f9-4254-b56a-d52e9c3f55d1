package net.evolveip.ossmosis.api.features.calldetails.repository;

import java.util.List;
import java.util.Optional;
import net.evolveip.ossmosis.api.features.calldetails.entity.CallDetailEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CallDetailRepository extends JpaRepository<CallDetailEntity, Long>, CustomCallDetailRepository{

  Optional<CallDetailEntity> findCallDetailById(Long id);

  @Query(nativeQuery = true, value = "ALTER SESSION SET JDBC_QUERY_RESULT_FORMAT='JSON'")
  void setResultFormat();

  @Query(nativeQuery = true, value = "SELECT TOP 20 BF_ID,BF_GROUP,BF_USERNUMBER,BF_STARTTIME,BF_CALLDURATION,BF_LASTUPDATEDTIMESTAMP from BROADSOFTCDRFACT")
  List<CallDetailEntity> findFirst5();
}
