package net.evolveip.ossmosis.api.scheduler.validator.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.time.ZoneId;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidZoneId;

public class ZoneIdValidator implements ConstraintValidator<ValidZoneId, String> {

  @Override
  public void initialize(ValidZoneId constraintAnnotation) {
    // No initialization needed
  }

  @Override
  public boolean isValid(final String value, final ConstraintValidatorContext context) {
    if (value == null) {
      return true; // Use @NotNull for null validation
    }
    try {
      ZoneId.of(value);
      return true;
    } catch (Exception e) {
      return false;
    }
  }
}