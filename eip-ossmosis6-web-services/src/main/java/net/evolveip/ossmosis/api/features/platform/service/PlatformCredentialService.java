package net.evolveip.ossmosis.api.features.platform.service;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.platform.constant.PlatformCredentialConstants;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformCredentialRequestDTO;
import net.evolveip.ossmosis.api.features.platform.dto.PlatformCredentialResponseDTO;
import net.evolveip.ossmosis.api.features.platform.entity.PlatformCredential;
import net.evolveip.ossmosis.api.features.platform.mapper.PlatformCredentialMapper;
import net.evolveip.ossmosis.api.features.platform.repository.CustomPlatformCredentialRepositoryImpl;
import net.evolveip.ossmosis.api.features.platform.repository.PlatformRepository;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class PlatformCredentialService {

  private final CustomPlatformCredentialRepositoryImpl platformCredentialRepository;
  private final PlatformCredentialMapper platformCredentialMapper;
  private final PlatformRepository platformRepository;

  @Autowired
  public PlatformCredentialService(
      CustomPlatformCredentialRepositoryImpl platformCredentialRepository,
      PlatformCredentialMapper platformCredentialMapper, PlatformRepository platformRepository) {
    this.platformCredentialRepository = platformCredentialRepository;
    this.platformCredentialMapper = platformCredentialMapper;
    this.platformRepository = platformRepository;
  }

  @Transactional(readOnly = true)
  public ApiResponse<List<PlatformCredentialResponseDTO>> getPlatformCredentials(
      Integer platformId) {
    try {
      return ApiResponse.create(this.platformCredentialMapper.entitiesToDTOs(
          platformCredentialRepository.getPlatformCredentialsByPlatformId(platformId)));
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }

  @Transactional
  public ApiResponse<PlatformCredentialResponseDTO> createPlatformCredentials(
      PlatformCredentialRequestDTO platformCredentialRequestDTO) {
    try {
      ApiResponse<PlatformCredentialResponseDTO> errorResponse = checkPlatformExists(
          platformCredentialRequestDTO.getPlatformId());
      if (errorResponse != null) {
        return errorResponse;
      }
      errorResponse = checkPlatformCredentialTypeExists(
          platformCredentialRequestDTO.getPlatformCredentialId(),
          platformCredentialRequestDTO.getType(), platformCredentialRequestDTO.getPlatformId());
      if (errorResponse != null) {
        return errorResponse;
      }
      if (platformCredentialRequestDTO.getPlatformCredentialId() > 0) {
        return ApiResponse.create(null, false,
            "PlatformCredentialId: " + platformCredentialRequestDTO.getPlatformCredentialId()
                + " must be less then or equal to 0.", HttpStatus.BAD_REQUEST);
      }

      PlatformCredential platformCredential = createCredentialByType(platformCredentialRequestDTO);
      if (platformCredential == null) {
        return ApiResponse.create(null, false,
            "Invalid credential type: " + platformCredentialRequestDTO.getType(),
            HttpStatus.BAD_REQUEST);
      }

      platformCredential.setDateCreated(ZonedDateTime.now());
      platformCredential.setDateUpdated(ZonedDateTime.now());
      return ApiResponse.create(platformCredentialMapper.toDTO(
          platformCredentialRepository.savePlatformCredential(
              platformCredential)));
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }

  @Modifying
  @Transactional
  public ApiResponse<PlatformCredentialResponseDTO> processUpdate(
      PlatformCredentialRequestDTO platformCredentialRequestDTO) {
    try {
      ApiResponse<PlatformCredentialResponseDTO> errorResponse = checkPlatformExists(
          platformCredentialRequestDTO.getPlatformId());
      if (errorResponse != null) {
        return errorResponse;
      }
      errorResponse = checkPlatformCredentialTypeExists(
          platformCredentialRequestDTO.getPlatformCredentialId(),
          platformCredentialRequestDTO.getType(), platformCredentialRequestDTO.getPlatformId());
      if (errorResponse != null) {
        return errorResponse;
      }
      if (!platformCredentialRepository.existsById(
          platformCredentialRequestDTO.getPlatformCredentialId())) {
        return ApiResponse.create(null, false,
            "PlatformCredentialId: " + platformCredentialRequestDTO.getPlatformCredentialId()
                + " does not exist.", HttpStatus.BAD_REQUEST);
      }

      PlatformCredential platformCredential = createCredentialByType(platformCredentialRequestDTO);
      if (platformCredential == null) {
        return ApiResponse.create(null, false,
            "Invalid credential type: " + platformCredentialRequestDTO.getType(),
            HttpStatus.BAD_REQUEST);
      }

      platformCredential.setDateUpdated(ZonedDateTime.now());
      return ApiResponse.create(platformCredentialMapper.toDTO(
          platformCredentialRepository.savePlatformCredential(
              platformCredential)));
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
  }

  @Transactional(readOnly = true)
  public PlatformCredential getPlatformCredentialsByPlatformIdAndType(Integer platformId, String type) {
    try {
      Optional<? extends PlatformCredential> platformCredentialOptional = platformCredentialRepository.findCredentialByPlatformIdAndType(platformId, type);
      return platformCredentialOptional.orElse(null);
    } catch (Exception e) {
      logger.error(e.getMessage());
      return null;
    }
  }

  private PlatformCredential createCredentialByType(PlatformCredentialRequestDTO dto) {
    switch(dto.getType()) {
      case PlatformCredentialConstants.BROADSOFT_AS_CRED_TYPE:
        return platformCredentialMapper.toBroadsoftASEntity(dto);
      case PlatformCredentialConstants.BROADSOFT_NS_CRED_TYPE:
        return platformCredentialMapper.toBroadsoftNSEntity(dto);
      case PlatformCredentialConstants.DUBBER_CRED_TYPE:
        return platformCredentialMapper.toDubberEntity(dto);
      case PlatformCredentialConstants.REDSKY_CRED_TYPE:
        return platformCredentialMapper.toRedskyEntity(dto);
      case PlatformCredentialConstants.WEBEX_CRED_TYPE:
        return platformCredentialMapper.toWebexEntity(dto);
      default:
        return null;
    }
  }

  private ApiResponse<PlatformCredentialResponseDTO> checkPlatformExists(int platformId) {
    if (!platformRepository.existsById(platformId)) {
      return ApiResponse.create(null, false, "PlatformId: " + platformId + " does not exist.",
          HttpStatus.BAD_REQUEST);
    }
    return null;
  }

  private ApiResponse<PlatformCredentialResponseDTO> checkPlatformCredentialTypeExists(
      int platformCredentialId, String type, int platformId) {
    Optional<? extends PlatformCredential> platformCredential = platformCredentialRepository.findCredentialByPlatformIdAndType(
        platformId, type);
    if (platformCredential.isPresent()
        && platformCredential.get().getPlatformCredentialId() != platformCredentialId) {
      return ApiResponse.create(null, false,
          "PlatformId: " + platformId + " already has a credential of type: " + type,
          HttpStatus.BAD_REQUEST);
    }
    return null;
  }
}
