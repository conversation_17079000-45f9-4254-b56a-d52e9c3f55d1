package net.evolveip.ossmosis.api.features.role.service;

import jakarta.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.features.enterprise.constant.EnterpriseConstants;
import net.evolveip.ossmosis.api.features.enterprise.repository.EnterpriseRepository;
import net.evolveip.ossmosis.api.features.login.repository.LoginRoleRepository;
import net.evolveip.ossmosis.api.features.role.constant.RoleConstants;
import net.evolveip.ossmosis.api.features.role.dto.RoleRequestDTO;
import net.evolveip.ossmosis.api.features.role.dto.RoleResponseDTO;
import net.evolveip.ossmosis.api.features.role.entity.Role;
import net.evolveip.ossmosis.api.features.role.entity.view.RolePermissionView;
import net.evolveip.ossmosis.api.features.role.mapper.RoleMapper;
import net.evolveip.ossmosis.api.features.role.mapper.RoleViewMapper;
import net.evolveip.ossmosis.api.features.role.repository.RoleRepository;
import net.evolveip.ossmosis.api.features.role.repository.RoleResourcePermissionRepository;
import net.evolveip.ossmosis.api.utils.exceptions.DuplicateFoundException;
import net.evolveip.ossmosis.api.utils.exceptions.NotFoundException;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.api.utils.service.AuthorizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RoleService {

  private final RoleRepository roleRepository;
  private final EnterpriseRepository enterpriseRepository;
  private final LoginRoleRepository loginRoleRepository;
  private final RoleResourcePermissionRepository roleResourcePermissionRepository;
  private final RoleViewMapper roleViewMapper;
  private final RoleResourcePermissionService roleResourcePermissionService;
  private final AuthorizationService authorizationService;
  private final RoleMapper roleMapper;

  @Autowired
  public RoleService(RoleRepository roleRepository, EnterpriseRepository enterpriseRepository,
      LoginRoleRepository loginRoleRepository,
      RoleResourcePermissionRepository roleResourcePermissionRepository,
      RoleViewMapper roleViewMapper, RoleResourcePermissionService roleResourcePermissionService,
      AuthorizationService authorizationService, RoleMapper roleMapper) {
    this.roleRepository = roleRepository;
    this.enterpriseRepository = enterpriseRepository;
    this.loginRoleRepository = loginRoleRepository;
    this.roleResourcePermissionRepository = roleResourcePermissionRepository;
    this.roleViewMapper = roleViewMapper;
    this.roleResourcePermissionService = roleResourcePermissionService;
    this.authorizationService = authorizationService;
    this.roleMapper = roleMapper;
  }

  public List<RoleResponseDTO> processGetAll(String enterpriseId) {
    List<RoleResponseDTO> ret = this.roleViewMapper.entitiesToDTOs(
        this.roleRepository.findAllWithPermissionView(enterpriseId));
    // set read only to false for root enterprise users
    if (authorizationService.getCurrentUserEnterprise().getEnterpriseId()
        .equals(EnterpriseConstants.ROOT_ENTERPRISE_ID) && enterpriseId.equals(
        EnterpriseConstants.ROOT_ENTERPRISE_ID)) {
      ret.forEach(role -> role.setReadOnly(false));
    } else {
      ret.removeIf(role -> role.getRoleName().equals(RoleConstants.ROLE_SUPER_USER));
    }
    return ret;
  }

  public Page<RoleResponseDTO> processGet(String enterpriseId, String filterValue, final Pageable pageable) {
    try {
      String filterValueLowerCase = filterValue.toLowerCase();
      long totalElements = roleRepository.findFilteredCountForEnterpriseWithDataPopulated(enterpriseId, filterValueLowerCase);
      Page<RolePermissionView> page = roleRepository.findAllByFilteredPage(enterpriseId, filterValueLowerCase, pageable);
      List<RoleResponseDTO> ret = this.roleViewMapper.entitiesToDTOs(page.getContent());
      
      // Set read only to false for root enterprise users
      if (authorizationService.getCurrentUserEnterprise().getEnterpriseId()
          .equals(EnterpriseConstants.ROOT_ENTERPRISE_ID) && enterpriseId.equals(
          EnterpriseConstants.ROOT_ENTERPRISE_ID)) {
        ret.forEach(role -> role.setReadOnly(false));
      }
      
      return new PageImpl<>(ret, pageable, totalElements);
    } catch (Exception e) {
      logger.error(e.getMessage());
      return new PageImpl<>(new ArrayList<>(), pageable, 0);
    }
  }

  @Transactional
  public RoleResponseDTO processCreate(String enterpriseId, RoleRequestDTO roleRequestDTO) {

    authorizationService.checkIfCurrentUserHasAccessToEnterpriseId(enterpriseId);
    checkPathParamWithRoleEnterpriseId(roleRequestDTO, enterpriseId);
    checkIfRoleNameBelongsToAnotherRoleIdWithinSameEnterprise(roleRequestDTO);

    // create the role entity from the incoming DTO:
    Role candidateRole = roleMapper.toRole(roleRequestDTO, new Role(), enterpriseId,
        enterpriseRepository);

    // save the new entity to the database:
    Role role = roleRepository.saveAndFlush(candidateRole);

    // set the resource permissions for the new role:
    roleResourcePermissionService.createRoleResourcePermissionsByRole(role, roleRequestDTO);
    role.setPermissionList(roleResourcePermissionService.getResourcePermissionsByRole(role));
    return roleMapper.toDTO(role);
  }

  @Transactional
  public RoleResponseDTO processUpdate(String enterpriseId, RoleRequestDTO roleRequestDTO) {
    authorizationService.checkIfCurrentUserHasAccessToEnterpriseId(enterpriseId);
    checkPathParamWithRoleEnterpriseId(roleRequestDTO, enterpriseId);
    checkIfRoleNameBelongsToAnotherRoleIdWithinSameEnterprise(roleRequestDTO);

    Integer candidateRoleId = roleRequestDTO.getRoleId();
    String candidateRoleName = roleRequestDTO.getRoleName();

    // check if the incoming roleId is valid:
    Role role = roleRepository.findById(candidateRoleId)
        .orElseThrow(() -> new NotFoundException("Role id not found: " + candidateRoleId));

    // change the name of the role if it's different from the existing role name:
    if (!candidateRoleName.equalsIgnoreCase(role.getRoleName())) {
      role.setRoleName(candidateRoleName);
      roleRepository.saveAndFlush(role);
    }
    roleResourcePermissionService.updateRoleResourcePermissionsByRole(role, roleRequestDTO);
    role.setPermissionList(roleResourcePermissionService.getResourcePermissionsByRole(role));
    return roleMapper.toDTO(role);
  }

  @Transactional
  public ApiResponse<Boolean> deleteRole(int roleId, String enterpriseId, boolean forceDelete) {
    try {
      Optional<Role> role = roleRepository.findById(roleId);
      // make sure the enterpriseId path param matches the role enterpriseId and that
      // the role isn't a default role before deleting
      if (role.isPresent() && role.get().getEnterprise().getEnterpriseId().equals(enterpriseId)
          && !role.get().getDefaultRole()) {
        int roleAssignmentCount = loginRoleRepository.findAssignedLogins(roleId, enterpriseId)
            .size();
        if (!forceDelete && roleAssignmentCount > 0) {
          return ApiResponse.create(false, false,
              "Error deleting role. There are " + roleAssignmentCount
                  + " logins assigned this role.", HttpStatus.BAD_REQUEST);
        }
        int loginRoleDeleteCount = 0;
        if (roleAssignmentCount > 0) {
          loginRoleDeleteCount = this.loginRoleRepository.deleteAllByRoleRoleId(roleId);
        }
        if (roleAssignmentCount == loginRoleDeleteCount) {
          this.roleResourcePermissionRepository.deleteAllByRoleRoleId(roleId);
          int roleDeleteCount = this.roleRepository.deleteByRoleId(roleId);
          if (roleDeleteCount == 1) {
            return ApiResponse.create(true);
          }
        }
      }
    } catch (Exception e) {
      logger.error(e.getMessage());
      return ApiResponse.create(e);
    }
    return ApiResponse.create(false, false, "Error deleting role.", HttpStatus.BAD_REQUEST);
  }

  /***
   * Check if the enterpriseId specified in the role request matches the URI enterpriseId
   * @param role role request to be checked
   * @param enterprisePathParam the URI enterpriseId
   */
  private void checkPathParamWithRoleEnterpriseId(RoleRequestDTO role, String enterprisePathParam) {
    if (role.getEnterpriseId() == null || !role.getEnterpriseId().equals(enterprisePathParam)) {
      throw new AccessDeniedException("Error path enterpriseId does not match role enterpriseId.");
    }
  }

  /***
   * Check if the roleName is associated with a different roleId within the same enterpriseId
   * @param roleRequestDTO the role request to be checked
   */
  private void checkIfRoleNameBelongsToAnotherRoleIdWithinSameEnterprise(
      RoleRequestDTO roleRequestDTO) {
    if (roleRequestDTO.getRoleId() == null) {
      throw new NotFoundException("roleId is required");
    }
    String roleName = roleRequestDTO.getRoleName();
    String enterpriseId = roleRequestDTO.getEnterpriseId();

    // check if the roleName is associated with a different roleId within the same enterpriseId:
    roleRepository.findRoleByRoleNameAndEnterprise_EnterpriseId(roleName, enterpriseId).stream()
        .filter(r -> !r.getRoleId().equals(roleRequestDTO.getRoleId())).findAny().ifPresent(s -> {
          throw new DuplicateFoundException(
              String.format("Role name: '%s' already exists for enterprise '%s'", roleName,
                  enterpriseId));
        });
  }
}