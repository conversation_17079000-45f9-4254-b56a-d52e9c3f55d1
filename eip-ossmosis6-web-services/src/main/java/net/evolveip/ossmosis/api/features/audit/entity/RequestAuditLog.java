package net.evolveip.ossmosis.api.features.audit.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedAttributeNode;
import jakarta.persistence.NamedEntityGraph;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.login.entity.Login;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Entity
@Builder
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "request_audit_log", schema = "audit", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"request_id"})})
@NamedEntityGraph(name = "RequestAuditLog.recordAuditLogs", attributeNodes = {
    @NamedAttributeNode("recordAuditLogs"), @NamedAttributeNode("enterprise"),
    @NamedAttributeNode("login")})
public class RequestAuditLog {

  @Id
  @Column(name = "request_audit_log_id", nullable = false)
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long auditLogId;

  @Column(name = "request_id", nullable = false)
  private UUID requestId;

  @JoinColumn(name = "login_id", nullable = false)
  @ManyToOne(targetEntity = Login.class, fetch = FetchType.LAZY)
  private Login login;

  @JoinColumn(name = "enterprise_id")
  @ManyToOne(targetEntity = Enterprise.class, fetch = FetchType.LAZY)
  private Enterprise enterprise;

  @Column(name = "path", nullable = false)
  private String path;

  @Column(name = "method", nullable = false)
  private String method;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(name = "payload", columnDefinition = "json")
  private Map<String, Object> payload;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(name = "response", columnDefinition = "json")
  private Map<String, Object> response;

  @Column(name = "timestamp", nullable = false)
  @ColumnDefault("CURRENT_TIMESTAMP")
  private ZonedDateTime timestamp;

  @Column(name = "duration_in_ms")
  private Long durationInMs;

  @OneToMany(mappedBy = "requestAuditLog", fetch = FetchType.LAZY)
  private List<RecordAuditLog> recordAuditLogs = new ArrayList<>();

}
