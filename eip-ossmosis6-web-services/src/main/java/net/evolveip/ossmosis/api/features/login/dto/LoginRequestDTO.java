package net.evolveip.ossmosis.api.features.login.dto;

import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.EMAIL_VALIDATION_MESSAGE;
import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_BLANK_VALIDATION_MESSAGE;
import static net.evolveip.ossmosis.api.utils.request.OssmosisRequestConstant.NOT_NULL_VALIDATION_MESSAGE;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.evolveip.ossmosis.api.features.login.constant.LoginConstants;
import net.evolveip.ossmosis.api.utils.validators.annotations.EnterpriseId;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Builder
public class LoginRequestDTO {

  @NotNull(message = "loginId" + NOT_NULL_VALIDATION_MESSAGE)
  private Long loginId;

  @NotBlank(message = "loginEmail" + NOT_BLANK_VALIDATION_MESSAGE)
  @Length(max = LoginConstants.LOGIN_EMAIL_MAX_LENGTH)
  @Email(message = EMAIL_VALIDATION_MESSAGE)
  private String loginEmail;

  @NotBlank(message = "loginNameFirst" + NOT_BLANK_VALIDATION_MESSAGE)
  @Length(max = LoginConstants.LOGIN_NAME_MAX_LENGTH)
  private String loginNameFirst;

  @NotBlank(message = "loginNameLast" + NOT_BLANK_VALIDATION_MESSAGE)
  @Length(max = LoginConstants.LOGIN_NAME_MAX_LENGTH)
  private String loginNameLast;

  @NotNull(message = "active" + NOT_NULL_VALIDATION_MESSAGE)
  private Boolean active = true;

  @NotNull(message = "locked" + NOT_NULL_VALIDATION_MESSAGE)
  private Boolean locked = false;

  @NotBlank(message = "loginGroup" + NOT_BLANK_VALIDATION_MESSAGE)
  @Length(max = LoginConstants.LOGIN_GROUP_MAX_LENGTH)
  private String loginGroup;

  @NotBlank(message = "loginPhoneNumber" + NOT_BLANK_VALIDATION_MESSAGE)
  @Length(max = LoginConstants.LOGIN_PHONE_NUMBER_MAX_LENGTH)
  private String loginPhoneNumber;

  @EnterpriseId
  @NotNull(message = "loginPrimaryEnterpriseId" + NOT_NULL_VALIDATION_MESSAGE)
  private String loginPrimaryEnterpriseId;

  @Valid
  private List<LoginResourcePermissionRequestDTO> userPermissionList;

  @Valid
  private List<LoginRoleRequestDTO> roles;

}
