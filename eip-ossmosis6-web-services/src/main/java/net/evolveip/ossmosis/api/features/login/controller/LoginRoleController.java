package net.evolveip.ossmosis.api.features.login.controller;


import jakarta.validation.Valid;
import java.util.List;
import net.evolveip.ossmosis.api.features.login.constant.LoginConstants;
import net.evolveip.ossmosis.api.features.login.dto.LoginRoleRequestDTO;
import net.evolveip.ossmosis.api.features.login.dto.LoginRoleResponseDTO;
import net.evolveip.ossmosis.api.features.login.service.LoginRoleService;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/***
 * Controller that assigns roles to logins
 */
@RestController
@RequestMapping(value = "/logins/id/{loginId}/loginrole")
@Validated
public class LoginRoleController {

  private final LoginRoleService loginRoleService;

  @Autowired
  public LoginRoleController(LoginRoleService loginRoleService) {
    this.loginRoleService = loginRoleService;
  }

  /***
   * Returns all the roles assigned a loginId
   * @param loginId
   * @return
   */
  @Secured(LoginConstants.LOGINS_READ)
  @GetMapping
  public ResponseEntity<ApiResponse<?>> doGet(@PathVariable Long loginId) {
    ApiResponse<List<LoginRoleResponseDTO>> apiResponse = loginRoleService.processGet(loginId);
    return new ResponseEntity<>(apiResponse,
        HttpStatusCode.valueOf(apiResponse.getHttpStatusCode()));
  }

  /***
   * Returns the role filtered by the roleId, assigned to a specific loginId
   * @param loginId
   * @param roleId
   * @return
   */
  @Secured(LoginConstants.LOGINS_READ)
  @GetMapping("/{roleId}")
  public ResponseEntity<ApiResponse<?>> doGetByRoleId(@PathVariable Long loginId,
      @PathVariable Integer roleId) {
    ApiResponse<List<LoginRoleResponseDTO>> apiResponse = loginRoleService.processGet(loginId,
        roleId);
    return new ResponseEntity<>(apiResponse,
        HttpStatusCode.valueOf(apiResponse.getHttpStatusCode()));
  }

  /***
   * Creates a login-role association
   * @param loginId
   * @param loginRoleRequestDTO
   * @return a loginRoleResponseDTO
   */
  @Secured(LoginConstants.LOGINS_CREATE)
  @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<ApiResponse<?>> doCreate(@PathVariable Long loginId, @Valid @RequestBody
  LoginRoleRequestDTO loginRoleRequestDTO) {
    loginRoleRequestDTO.setLoginId(loginId);
    ApiResponse<LoginRoleResponseDTO> apiResponse = loginRoleService.processCreateOrUpdate(
        loginRoleRequestDTO);
    return new ResponseEntity<>(apiResponse,
        HttpStatusCode.valueOf(apiResponse.getHttpStatusCode()));
  }

  /***
   * Updates a login-role association
   * @param loginId
   * @param loginRoleRequestDTO
   * @return
   */
  @Secured({LoginConstants.LOGINS_UPDATE, LoginConstants.LOGINS_READ})
  @PutMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<ApiResponse<?>> doUpdate(@PathVariable Long loginId,
      @Valid @RequestBody LoginRoleRequestDTO loginRoleRequestDTO) {
    loginRoleRequestDTO.setLoginId(loginId);
    ApiResponse<LoginRoleResponseDTO> apiResponse = loginRoleService.processCreateOrUpdate(
        loginRoleRequestDTO);
    return new ResponseEntity<>(apiResponse,
        HttpStatusCode.valueOf(apiResponse.getHttpStatusCode()));
  }

  /***
   * Performs a deletion of a login-role association
   * @param loginId the loginId to be looked up
   * @param roleId the roleId to be looked up
   * @return true for success, false for failure
   */
  @Secured(LoginConstants.LOGINS_DELETE)
  @DeleteMapping(value = "/{roleId}")
  public ResponseEntity<ApiResponse<?>> doDelete(@PathVariable Long loginId,
      @PathVariable Integer roleId) {
    LoginRoleRequestDTO loginRequestDTO = LoginRoleRequestDTO.builder().loginId(loginId)
        .roleId(roleId)
        .build();
    ApiResponse<Boolean> apiResponse = loginRoleService.processDelete(loginRequestDTO);
    return new ResponseEntity<>(apiResponse,
        HttpStatusCode.valueOf(apiResponse.getHttpStatusCode()));
  }
}
