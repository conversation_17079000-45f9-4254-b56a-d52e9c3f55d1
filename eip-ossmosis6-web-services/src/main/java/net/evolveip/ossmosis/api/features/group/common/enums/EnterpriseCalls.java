package net.evolveip.ossmosis.api.features.group.common.enums;

import lombok.Getter;

@Getter
public enum EnterpriseCalls {
  UseExtension("useExtension", "Use Extension"),
  UseLocationCodePlusExtension("useLocationCodePlusExtension", "Use Location Code plus Extension"),
  UseExternalCallsPolicy("useExternalCallsPolicy", "Use External Calls Policy"),
  Default(UseExtension.enumId, UseExtension.enumLabel);

  private final String enumId;
  private final String enumLabel;

  EnterpriseCalls(String enumId, String enumLabel) {
    this.enumId = enumId;
    this.enumLabel = enumLabel;
  }
}