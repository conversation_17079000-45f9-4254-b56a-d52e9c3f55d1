package net.evolveip.ossmosis.api.features.calldetails.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Table(name = "BROADSOFTCDRFACT")
@NoArgsConstructor
@AllArgsConstructor
public class CallDetailEntity {

  @Id
  @Column(name = "BF_ID")
  private Long id;

  @Column(name = "BF_USERNUMBER")
  private String userNumber;

  @Column(name = "BF_CALLINGNUMBER")
  private String callingNumber;

  @Column(name = "BF_CALLEDNUMBER")
  private String calledNumber;

  @Column(name = "BF_CALLDURATION")
  private Long callDuration;

  @Column(name = "BF_DIRECTION")
  private String callDirection;

  @Column(name = "BF_ANSWERINDICATOR")
  private String answerStatus;

  @Column(name = "BF_CALLINGLERGINFO")
  private Long callingLerg;

  @Column(name = "BF_CALLEDLERGINFO"  )
  private Long calledLerg;

  @Column(name = "BF_ROUTE")
  private String callRoute;

  @Column(name = "BF_EIPID")
  private String enterpriseId;

  @Column(name = "BF_STARTTIME")
  private String startDateTime;

  @Column(name = "BF_GROUPNUMBER")
  private String groupNumber;

  @Column(name = "BF_GROUP")
  private String groupId;

  @Column(name = "BF_CALLCATEGORYKEY")
  private Long callCategoryKey;

  @Column(name = "BF_EIPCALLTYPE")
  private Long callType;

  @Column(name = "BF_USERID")
  private String userId;
}
