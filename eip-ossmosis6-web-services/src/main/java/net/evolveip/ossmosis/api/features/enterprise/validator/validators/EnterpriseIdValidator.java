package net.evolveip.ossmosis.api.features.enterprise.validator.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import net.evolveip.ossmosis.api.features.enterprise.service.EnterpriseValidationMethods;
import net.evolveip.ossmosis.api.features.enterprise.validator.annotations.ValidEnterprise;

public class EnterpriseIdValidator implements
    ConstraintValidator<ValidEnterprise, String> {

  private final EnterpriseValidationMethods validationService;

  public EnterpriseIdValidator(final EnterpriseValidationMethods validationService) {
    this.validationService = validationService;
  }

  @Override
  public boolean isValid(final String value, final ConstraintValidatorContext cvContext) {
    if (value == null) {
      return false;
    }
    return validationService.isEnterpriseIdValid(value);
  }
}