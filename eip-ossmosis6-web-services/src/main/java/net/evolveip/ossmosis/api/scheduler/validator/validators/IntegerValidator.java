package net.evolveip.ossmosis.api.scheduler.validator.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import net.evolveip.ossmosis.api.scheduler.validator.annotations.ValidInteger;

public class IntegerValidator implements ConstraintValidator<ValidInteger, String> {

  @Override
  public void initialize(ValidInteger constraintAnnotation) {
  }

  @Override
  public boolean isValid(final String value, final ConstraintValidatorContext context) {
    if (value == null) {
      return false;
    }
    try {
      Integer.parseInt(value);
      return true;
    } catch (Exception e) {
      return false;
    }
  }
}