package net.evolveip.ossmosis.api.core;

import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.http.HttpStatus;

public interface InterfaceGenericService {

  default <T> ApiResponse<T> response(T response) {
    return ApiResponse.create(response);
  }

  default <T> ApiResponse<T> genericEmptyResponse(String message) {
    return genericBadResponse(message, HttpStatus.OK);
  }

  default ApiResponse<Boolean> genericBooleanResponse(String message) {
    return ApiResponse.create(false, false,  message, HttpStatus.BAD_REQUEST);
  }

  default <T> ApiResponse<T> genericBadResponse(String message, HttpStatus code) {
    return ApiResponse.create(null, false, message, code);
  }

  default <T> ApiResponse<T> exceptionResponse(Exception ex) {
    return ApiResponse.create(ex);
  }
}