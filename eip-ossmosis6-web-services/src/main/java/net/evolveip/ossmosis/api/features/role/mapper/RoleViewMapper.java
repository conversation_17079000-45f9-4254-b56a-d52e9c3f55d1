package net.evolveip.ossmosis.api.features.role.mapper;

import java.util.List;
import net.evolveip.ossmosis.api.features.role.dto.RoleResponseDTO;
import net.evolveip.ossmosis.api.features.role.entity.view.RolePermissionView;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = ComponentModel.SPRING, uses = {
    RoleResourcePermissionRolePermissionViewMapper.class})
public interface RoleViewMapper {

  RoleViewMapper INSTANCE = Mappers.getMapper(RoleViewMapper.class);

  @Mapping(source = "roleId", target = "roleId")
  @Mapping(source = "dateCreated", target = "dateCreated")
  @Mapping(source = "dateUpdated", target = "dateUpdated")
  @Mapping(source = "roleName", target = "roleName")
  @Mapping(source = "enterpriseId", target = "enterpriseId")
  @Mapping(source = "permissionList", target = "permissionList")
  @Mapping(source = "defaultRole", target = "readOnly")
  RoleResponseDTO toRoleResponseDTO(RolePermissionView role);


  List<RoleResponseDTO> entitiesToDTOs(List<RolePermissionView> roles);

}
