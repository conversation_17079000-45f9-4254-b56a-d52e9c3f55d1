package net.evolveip.ossmosis.api.features.audit.mapper;

import java.util.List;
import net.evolveip.ossmosis.api.features.audit.dto.RequestAuditLogDTO;
import net.evolveip.ossmosis.api.features.audit.entity.RequestAuditLog;
import net.evolveip.ossmosis.api.features.enterprise.mapper.EnterpriseMapper;
import net.evolveip.ossmosis.api.features.login.mapper.LoginMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;

@Mapper(componentModel = ComponentModel.SPRING, uses = {LoginMapper.class, EnterpriseMapper.class,
    RecordAuditLogMapper.class})
public interface RequestAuditLogMapper {

  @Mapping(source = "auditLogId", target = "auditLogId")
  @Mapping(source = "requestId", target = "requestId")
  @Mapping(source = "login", target = "login")
  @Mapping(source = "enterprise", target = "enterprise")
  @Mapping(source = "path", target = "path")
  @Mapping(source = "method", target = "method")
  @Mapping(source = "payload", target = "payload")
  @Mapping(source = "response", target = "response")
  @Mapping(source = "timestamp", target = "timestamp")
  @Mapping(source = "durationInMs", target = "durationInMs")
  @Mapping(source = "recordAuditLogs", target = "recordAuditLogs")
  RequestAuditLogDTO map(RequestAuditLog requestAuditLog);

  List<RequestAuditLogDTO> entitiesToDTOs(List<RequestAuditLog> requestAuditLogs);

}
