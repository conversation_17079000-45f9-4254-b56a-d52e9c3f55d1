package net.evolveip.ossmosis.api.config.logging;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.config.audit.AuditFilterConfig;
import net.evolveip.ossmosis.api.config.auth.OssmosisJwtAuthenticationConverter;
import net.evolveip.ossmosis.api.features.audit.entity.RequestAuditLog;
import net.evolveip.ossmosis.api.features.audit.service.RecordAuditLogService;
import net.evolveip.ossmosis.api.features.audit.service.RequestAuditLogService;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.login.dto.LoginInfoResponseDTO;
import net.evolveip.ossmosis.api.features.login.entity.Login;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.OrRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;


@Component
@Slf4j
public class OssmosisRequestLoggingFilter extends OncePerRequestFilter {

  @Value("${ossmosis.logging.requestfilter.logLengthLimit}")
  private int logLengthLimit;

  @Value("${ossmosis.logging.audit-methods}")
  private String[] auditMethods;

  private final RequestAuditLogService requestAuditLogService;

  private final RecordAuditLogService recordAuditLogService;

  private final ObjectMapper objectMapper;

  private final RequestMatcher swaggerRequestMatcher;

  @Autowired
  public OssmosisRequestLoggingFilter(RequestAuditLogService requestAuditLogService,
      ObjectMapper objectMapper,
      RecordAuditLogService recordAuditLogService,
      @Value("${ossmosis.springdoc.allowed-paths}") String[] swaggerPathsToAllow) {
    this.requestAuditLogService = requestAuditLogService;
    this.objectMapper = objectMapper;
    this.recordAuditLogService = recordAuditLogService;
    List<RequestMatcher> matchers = new ArrayList<>();
    for (String path : swaggerPathsToAllow) {
      matchers.add(new AntPathRequestMatcher(path));
    }
    swaggerRequestMatcher = new OrRequestMatcher(matchers);
  }

  @Override
  protected boolean shouldNotFilter(@NonNull HttpServletRequest request) {
    return swaggerRequestMatcher.matcher(request).isMatch();
  }

  @Override
  protected void doFilterInternal(HttpServletRequest request, @NonNull HttpServletResponse response,
      @NonNull FilterChain filterChain)
      throws ServletException, IOException {

    boolean recordRequestInAuditTable = Arrays.stream(auditMethods).toList()
        .contains(request.getMethod()) || OssmosisJwtAuthenticationConverter.firstTimeInSystem();

    LoginInfoResponseDTO login = null;
    RequestAuditLog auditLogEntry = null;

    if (recordRequestInAuditTable) {
      if (SecurityContextHolder.getContext().getAuthentication().getDetails() != null &&
          SecurityContextHolder.getContext().getAuthentication()
              .getDetails() instanceof LoginInfoResponseDTO) {
        login = (LoginInfoResponseDTO) SecurityContextHolder.getContext().getAuthentication()
            .getDetails();
      }
      auditLogEntry = RequestAuditLog.builder()
          .requestId(AuditFilterConfig.getCurrentRequestId())
          .login(Login.builder().loginId(login == null ? null : login.getLoginResponseDTO()
              .getLoginId()).build())
          .enterprise(Enterprise.builder()
              .enterpriseId(login.getLoginResponseDTO().getLoginPrimaryEnterpriseId())
              .build())
          .path(request.getServletPath())
          .durationInMs(0L)
          .timestamp(ZonedDateTime.now())
          .method(request.getMethod())
          .payload(null)
          .response(null)
          .build();
      ApiResponse<RequestAuditLog> apiResponse = this.requestAuditLogService.addUpdateRequestToLog(
          auditLogEntry);
      if (apiResponse.isSuccessful() && apiResponse.getPayload() != null) {
        auditLogEntry = apiResponse.getPayload();
      }
    }

    ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(request);
    ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);

    long startTime = System.currentTimeMillis();
    filterChain.doFilter(requestWrapper, responseWrapper);
    long timeTaken = System.currentTimeMillis() - startTime;
    String requestBody;
    String responseBody;
    Map<String, Object> jsonNodeRequestBody = null;
    Map<String, Object> jsonNodeResponseBody = null;

    if (List.of(HttpMethod.POST.name(), HttpMethod.PUT.name()).contains(request.getMethod()) && (
        MediaType.APPLICATION_JSON_VALUE.equals(response.getContentType())
            && MediaType.APPLICATION_JSON_VALUE.equals(request.getContentType()))) {
      requestBody = formatJsonWithJacksonForRequest(
          getStringValue(requestWrapper.getContentAsByteArray(),
              request.getCharacterEncoding())
      );
      responseBody = formatJsonWithJackson(
          getStringValue(responseWrapper.getContentAsByteArray(),
              response.getCharacterEncoding()));

      jsonNodeRequestBody = objectMapper.readValue(requestBody, HashMap.class);
      jsonNodeResponseBody = objectMapper.readValue(responseBody, HashMap.class);
      logger.info(
          "Request Complete: method={} uri={} status code={} time={} payload={} response={}",
          request.getMethod(), request.getRequestURI(),
          response.getStatus(),
          timeTaken,
          truncateStringValue(requestBody),
          truncateStringValue(responseBody));
    } else if (MediaType.APPLICATION_JSON_VALUE.equals(response.getContentType())) {
      responseBody = formatJsonWithJackson(
          getStringValue(responseWrapper.getContentAsByteArray(),
              response.getCharacterEncoding()));
      jsonNodeResponseBody = objectMapper.readValue(responseBody, HashMap.class);
      logger.info(
          "Request Complete: method={} uri={} status code={} time={} response={}",
          request.getMethod(), request.getRequestURI(),
          response.getStatus(),
          timeTaken,
          truncateStringValue(responseBody));
    } else if (MediaType.APPLICATION_JSON_VALUE.equals(request.getContentType())) {
      requestBody = formatJsonWithJacksonForRequest(
          getStringValue(requestWrapper.getContentAsByteArray(),
              request.getCharacterEncoding())
      );
      jsonNodeRequestBody = objectMapper.readValue(requestBody, HashMap.class);
      logger.info(
          "Request Complete: method={} uri={} status code={} time={} payload={}",
          request.getMethod(), request.getRequestURI(),
          response.getStatus(),
          timeTaken,
          truncateStringValue(requestBody));
    } else {
      logger.info(
          "Request Complete: method={} uri={} status code={} time={}",
          request.getMethod(), request.getRequestURI(),
          response.getStatus(),
          timeTaken);
    }
    if (recordRequestInAuditTable) {
      auditLogEntry.setDurationInMs(timeTaken);
      auditLogEntry.setPayload(jsonNodeRequestBody);
      auditLogEntry.setResponse(jsonNodeResponseBody);

      Map pathVariables = (Map) request.getAttribute(
          HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
      if (pathVariables != null) {
        String enterpriseId = (String) pathVariables.get("enterpriseId");
        if (enterpriseId != null) {
          auditLogEntry.setEnterprise(Enterprise.builder().enterpriseId(enterpriseId).build());
        }
      }

      this.requestAuditLogService.addUpdateRequestToLog(auditLogEntry);
      if (OssmosisJwtAuthenticationConverter.firstTimeInSystem()) {
        this.recordAuditLogService.updateRecordAuditLogForCreateLoginOnFirstRequest(auditLogEntry,
            login);
      }
    }

    responseWrapper.copyBodyToResponse();
  }

  public String formatJsonWithJackson(String json) throws JsonProcessingException {
    JsonNode jsonNode = objectMapper.readTree(json);
    return jsonNode.toString();
  }

  private String formatJsonWithJacksonForRequest(String json) throws JsonProcessingException {
    JsonNode jsonNode = objectMapper.readTree(json);
    //need to do this for json arrays that are received in request body
    return "{\"body\": " + jsonNode.toString() + "}";
  }

  private String truncateStringValue(String json) {
    return json.length() > logLengthLimit ? json.substring(0, logLengthLimit) + "..." : json;
  }

  private String getStringValue(byte[] contentAsByteArray, String characterEncoding) {
    try {
      String ret = new String(contentAsByteArray, characterEncoding);
      return ret.isEmpty() ? "{}" : ret;
    } catch (UnsupportedEncodingException e) {
      logger.error("UnsupportedEncoding in logging filer: " + e.getMessage());
    }
    return "{}";
  }
}