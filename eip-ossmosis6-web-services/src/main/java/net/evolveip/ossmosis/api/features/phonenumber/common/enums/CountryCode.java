package net.evolveip.ossmosis.api.features.phonenumber.common.enums;

import lombok.Getter;

@Getter
public enum CountryCode {
  Afghanistan("+93", "AF", "AFG", "Afghanistan"),
  Albania("+355", "AL", "ALB", "Albania"),
  Algeria("+213", "DZ", "DZA", "Algeria"),
  AmericanSamoa("******", "AS", "ASM", "American Samoa"),
  Andorra("+376", "AD", "AND", "Andorra"),
  Angola("+244", "AO", "AGO", "Angola"),
  Anguilla("******", "AI", "AIA", "Anguilla"),
  Antarctica("+672", "AQ", "ATA", "Antarctica"),
  AntiguaAndBarbuda("******", "AG", "ATG", "Antigua and Barbuda"),
  Argentina("+54", "AR", "ARG", "Argentina"),
  Armenia("+374", "AM", "ARM", "Armenia"),
  Aruba("+297", "AW", "ABW", "Aruba"),
  Australia("+61", "AU", "AUS", "Australia"),
  Austria("+43", "AT", "AUT", "Austria"),
  Azerbaijan("+994", "AZ", "AZE", "Azerbaijan"),
  Bahamas("******", "BS", "BHS", "Bahamas"),
  Bahrain("+973", "BH", "BHR", "Bahrain"),
  Bangladesh("+880", "BD", "BGD", "Bangladesh"),
  Barbados("******", "BB", "BRB", "Barbados"),
  Belarus("+375", "BY", "BLR", "Belarus"),
  Belgium("+32", "BE", "BEL", "Belgium"),
  Belize("+501", "BZ", "BLZ", "Belize"),
  Benin("+229", "BJ", "BEN", "Benin"),
  Bermuda("+1-441", "BM", "BMU", "Bermuda"),
  Bhutan("+975", "BT", "BTN", "Bhutan"),
  Bolivia("+591", "BO", "BOL", "Bolivia"),
  BosniaHerzegovina("+387", "BA", "BIH", "Bosnia and Herzegovina"),
  Botswana("+267", "BW", "BWA", "Botswana"),
  Brazil("+55", "BR", "BRA", "Brazil"),
  BritishOceanTerritory("+246", "IO", "IOT", "British Indian Ocean Territory"),
  BritishVirginIslands("+1-284", "VG", "VGB", "British Virgin Islands"),
  Brunei("+673", "BN", "BRN", "Brunei"),
  Bulgaria("+359", "BG", "BGR", "Bulgaria"),
  BurkinaFaso("+226", "BF", "BFA", "Burkina Faso"),
  Burundi("+257", "BI", "BDI", "Burundi"),
  Cambodia("+855", "KH", "KHM", "Cambodia"),
  Cameroon("+237", "CM", "CMR", "Cameroon"),
  Canada("+1", "CA", "CAN", "Canada"),
  CapeVerde("+238", "CV", "CPV", "Cape Verde"),
  CaymanIslands("+1-345", "KY", "CYM", "Cayman Islands"),
  CentralAfricanRepublic("+236", "CF", "CAF", "Central African Republic"),
  Chad("+235", "TD", "TCD", "Chad"),
  Chile("+56", "CL", "CHL", "Chile"),
  China("+86", "CN", "CHN", "China"),
  ChristmasIsland("+61", "CX", "CXR", "Christmas Island"),
  CocosIslands("+61", "CC", "CCK", "Cocos Islands"),
  Colombia("+57", "CO", "COL", "Colombia"),
  Comoros("+269", "KM", "COM", "Comoros"),
  CookIslands("+682", "CK", "COK", "Cook Islands"),
  CostaRica("+506", "CR", "CRI", "Costa Rica"),
  Croatia("+385", "HR", "HRV", "Croatia"),
  Cuba("+53", "CU", "CUB", "Cuba"),
  Curacao("+599", "CW", "CUW", "Curacao"),
  Cyprus("+357", "CY", "CYP", "Cyprus"),
  CzechRepublic("+420", "CZ", "CZE", "Czech Republic"),
  DemocraticRepublicOfTheCongo("+243", "CD", "COD", "Democratic Republic of the Congo"),
  Denmark("+45", "DK", "DNK", "Denmark"),
  Djibouti("+253", "DJ", "DJI", "Djibouti"),
  Dominica("+1-767", "DM", "DMA", "Dominica"),
  DominicanRepublic("+1-809", "DO", "DOM", "Dominican Republic"),
  EastTimor("+670", "TL", "TLS", "East Timor"),
  Ecuador("+593", "EC", "ECU", "Ecuador"),
  Egypt("+20", "EG", "EGY", "Egypt"),
  ElSalvador("+503", "SV", "SLV", "El Salvador"),
  EquatorialGuinea("+240", "GQ", "GNQ", "Equatorial Guinea"),
  Eritrea("+291", "ER", "ERI", "Eritrea"),
  Estonia("+372", "EE", "EST", "Estonia"),
  Ethiopia("+251", "ET", "ETH", "Ethiopia"),
  FalklandIslands("+500", "FK", "FLK", "Falkland Islands"),
  FaroeIslands("+298", "FO", "FRO", "Faroe Islands"),
  Fiji("+679", "FJ", "FJI", "Fiji"),
  Finland("+358", "FI", "FIN", "Finland"),
  France("+33", "FR", "FRA", "France"),
  FrenchPolynesia("+689", "PF", "PYF", "French Polynesia"),
  Gabon("+241", "GA", "GAB", "Gabon"),
  Gambia("+220", "GM", "GMB", "Gambia"),
  Georgia("+995", "GE", "GEO", "Georgia"),
  Germany("+49", "DE", "DEU", "Germany"),
  Ghana("+233", "GH", "GHA", "Ghana"),
  Gibraltar("+350", "GI", "GIB", "Gibraltar"),
  Greece("+30", "GR", "GRC", "Greece"),
  Greenland("+299", "GL", "GRL", "Greenland"),
  Grenada("+1-473", "GD", "GRD", "Grenada"),
  Guam("+1-671", "GU", "GUM", "Guam"),
  Guatemala("+502", "GT", "GTM", "Guatemala"),
  Guernsey("+44", "1481", "GGY", "Guernsey"),
  Guinea("+224", "GN", "GIN", "Guinea"),
  GuineaBissau("+245", "GW", "GNB", "Guinea-Bissau"),
  Guyana("+592", "GY", "GUY", "Guyana"),
  Haiti("+509", "HT", "HTI", "Haiti"),
  Honduras("+504", "HN", "HND", "Honduras"),
  HongKong("+852", "HK", "HKG", "Hong Kong"),
  Hungary("+36", "HU", "HUN", "Hungary"),
  Iceland("+354", "IS", "ISL", "Iceland"),
  India("+91", "IN", "IND", "India"),
  Indonesia("+62", "ID", "IDN", "Indonesia"),
  Iran("+98", "IR", "IRN", "Iran"),
  Iraq("+964", "IQ", "IRQ", "Iraq"),
  Ireland("+353", "IE", "IRL", "Ireland"),
  IsleMan("+44-1624", "IM", "IMN", "Isle of Man"),
  Israel("+972", "IL", "ISR", "Israel"),
  Italy("+39", "IT", "ITA", "Italy"),
  IvoryCoast("+225", "CI", "CIV", "Ivory Coast"),
  Jamaica("+1-876", "JM", "JAM", "Jamaica"),
  Japan("+81", "JP", "JPN", "Japan"),
  Jersey("+44", "1534", "JEY", "Jersey"),
  Jordan("+962", "JO", "JOR", "Jordan"),
  Kazakhstan("+7", "KZ", "KAZ", "Kazakhstan"),
  Kenya("+254", "KE", "KEN", "Kenya"),
  Kiribati("+686", "KI", "KIR", "Kiribati"),
  Kosovo("+383", "XK", "XKX", "Kosovo"),
  Kuwait("+965", "KW", "KWT", "Kuwait"),
  Kyrgyzstan("+996", "KG", "KGZ", "Kyrgyzstan"),
  Laos("+856", "LA", "LAO", "Laos"),
  Latvia("+371", "LV", "LVA", "Latvia"),
  Lebanon("+961", "LB", "LBN", "Lebanon"),
  Lesotho("+266", "LS", "LSO", "Lesotho"),
  Liberia("+231", "LR", "LBR", "Liberia"),
  Libya("+218", "LY", "LBY", "Libya"),
  Liechtenstein("+423", "LI", "LIE", "Liechtenstein"),
  Lithuania("+370", "LT", "LTU", "Lithuania"),
  Luxembourg("+352", "LU", "LUX", "Luxembourg"),
  Macau("+853", "MO", "MAC", "Macau"),
  Macedonia("+389", "MK", "MKD", "North Macedonia"),
  Madagascar("+261", "MG", "MDG", "Madagascar"),
  Malawi("+265", "MW", "MWI", "Malawi"),
  Malaysia("+60", "MY", "MYS", "Malaysia"),
  Maldives("+960", "MV", "MDV", "Maldives"),
  Mali("+223", "ML", "MLI", "Mali"),
  Malta("+356", "MT", "MLT", "Malta"),
  MarshallIslands("+692", "MH", "MHL", "Marshall Islands"),
  Mauritania("+222", "MR", "MRT", "Mauritania"),
  Mauritius("+230", "MU", "MUS", "Mauritius"),
  Mayotte("+262", "YT", "MYT", "Mayotte"),
  Mexico("+52", "MX", "MEX", "Mexico"),
  Micronesia("+691", "FM", "FSM", "Micronesia"),
  Moldova("+373", "MD", "MDA", "Moldova"),
  Monaco("+377", "MC", "MCO", "Monaco"),
  Mongolia("+976", "MN", "MNG", "Mongolia"),
  Montenegro("+382", "ME", "MNE", "Montenegro"),
  Montserrat("+1-664", "MS", "MSR", "Montserrat"),
  Morocco("+212", "MA", "MAR", "Morocco"),
  Mozambique("+258", "MZ", "MOZ", "Mozambique"),
  Myanmar("+95", "MM", "MMR", "Myanmar"),
  Namibia("+264", "NA", "NAM", "Namibia"),
  Nauru("+674", "NR", "NRU", "Nauru"),
  Nepal("+977", "NP", "NPL", "Nepal"),
  Netherlands("+31", "NL", "NLD", "Netherlands"),
  NetherlandsAntilles("+599", "AN", "ANT", "Netherlands Antilles"),
  NewCaledonia("+687", "NC", "NCL", "New Caledonia"),
  NewZealand("+64", "NZ", "NZL", "New Zealand"),
  Nicaragua("+505", "NI", "NIC", "Nicaragua"),
  Niger("+227", "NE", "NER", "Niger"),
  Nigeria("+234", "NG", "NGA", "Nigeria"),
  Niue("+683", "NU", "NIU", "Niue"),
  NorthKorea("+850", "KP", "PRK", "North Korea"),
  NorthernMarianaIslands("+1-670", "MP", "MNP", "Northern Mariana Islands"),
  Norway("+47", "NO", "NOR", "Norway"),
  Oman("+968", "OM", "OMN", "Oman"),
  Pakistan("+92", "PK", "PAK", "Pakistan"),
  Palau("+680", "PW", "PLW", "Palau"),
  Palestine("+970", "PS", "PSE", "Palestine"),
  Panama("+507", "PA", "PAN", "Panama"),
  PapuaNewGuinea("+675", "PG", "PNG", "Papua New Guinea"),
  Paraguay("+595", "PY", "PRY", "Paraguay"),
  Peru("+51", "PE", "PER", "Peru"),
  Philippines("+63", "PH", "PHL", "Philippines"),
  Pitcairn("+64", "PN", "PCN", "Pitcairn Islands"),
  Poland("+48", "PL", "POL", "Poland"),
  Portugal("+351", "PT", "PRT", "Portugal"),
  PuertoRico("+1-787", "PR", "PRI", "Puerto Rico"),
  Qatar("+974", "QA", "QAT", "Qatar"),
  RepublicCongo("+242", "CG", "COG", "Republic of the Congo"),
  Reunion("+262", "RE", "REU", "Reunion"),
  Romania("+40", "RO", "ROU", "Romania"),
  Russia("+7", "RU", "RUS", "Russia"),
  Rwanda("+250", "RW", "RWA", "Rwanda"),
  SaintBarthelemy("+590", "BL", "BLM", "Saint Barthelemy"),
  SaintHelena("+290", "SH", "SHN", "Saint Helena"),
  SaintKittsNevis("+1-869", "KN", "KNA", "Saint Kitts and Nevis"),
  SaintLucia("+1-758", "LC", "LCA", "Saint Lucia"),
  SaintMartin("+590", "MF", "MAF", "Saint Martin"),
  SaintPierreMiquelon("+508", "PM", "SPM", "Saint Pierre and Miquelon"),
  SaintVincentGrenadines("+1-784", "VC", "VCT", "Saint Vincent and the Grenadines"),
  Samoa("+685", "WS", "WSM", "Samoa"),
  SanMarino("+378", "SM", "SMR", "San Marino"),
  SaoTomePrincipe("+239", "ST", "STP", "Sao Tome and Principe"),
  SaudiArabia("+966", "SA", "SAU", "Saudi Arabia"),
  Senegal("+221", "SN", "SEN", "Senegal"),
  Serbia("+381", "RS", "SRB", "Serbia"),
  Seychelles("+248", "SC", "SYC", "Seychelles"),
  SierraLeone("+232", "SL", "SLE", "Sierra Leone"),
  Singapore("+65", "SG", "SGP", "Singapore"),
  SintMaarten("+1-721", "SX", "SXM", "Sint Maarten"),
  Slovakia("+421", "SK", "SVK", "Slovakia"),
  Slovenia("+386", "SI", "SVN", "Slovenia"),
  SolomonIslands("+677", "SB", "SLB", "Solomon Islands"),
  Somalia("+252", "SO", "SOM", "Somalia"),
  SouthAfrica("+27", "ZA", "ZAF", "South Africa"),
  SouthKorea("+82", "KR", "KOR", "South Korea"),
  SouthSudan("+211", "SS", "SSD", "South Sudan"),
  Spain("+34", "ES", "ESP", "Spain"),
  SriLanka("+94", "LK", "LKA", "Sri Lanka"),
  Sudan("+249", "SD", "SDN", "Sudan"),
  Suriname("+597", "SR", "SUR", "Suriname"),
  SvalbardJanMayen("+47", "SJ", "SJM", "Svalbard and Jan Mayen"),
  Swaziland("+268", "SZ", "SWZ", "Eswatini (Swaziland)"),
  Sweden("+46", "SE", "SWE", "Sweden"),
  Switzerland("+41", "CH", "CHE", "Switzerland"),
  Syria("+963", "SY", "SYR", "Syria"),
  Taiwan("+886", "TW", "TWN", "Taiwan"),
  Tajikistan("+992", "TJ", "TJK", "Tajikistan"),
  Tanzania("+255", "TZ", "TZA", "Tanzania"),
  Thailand("+66", "TH", "THA", "Thailand"),
  Togo("+228", "TG", "TGO", "Togo"),
  Tokelau("+690", "TK", "TKL", "Tokelau"),
  Tonga("+676", "TO", "TON", "Tonga"),
  TrinidadTobago("+1-868", "TT", "TTO", "Trinidad and Tobago"),
  Tunisia("+216", "TN", "TUN", "Tunisia"),
  Turkey("+90", "TR", "TUR", "Turkey"),
  Turkmenistan("+993", "TM", "TKM", "Turkmenistan"),
  TurksCaicosIslands("+1-649", "TC", "TCA", "Turks and Caicos Islands"),
  Tuvalu("+688", "TV", "TUV", "Tuvalu"),
  Uganda("+256", "UG", "UGA", "Uganda"),
  Ukraine("+380", "UA", "UKR", "Ukraine"),
  UnitedArabEmirates("+971", "AE", "ARE", "United Arab Emirates"),
  UnitedKingdom("+44", "GB", "GBR", "United Kingdom"),
  UnitedStates("+1", "US", "USA", "United States"),
  Uruguay("+598", "UY", "URY", "Uruguay"),
  USVirginIslands("+1-340", "VI", "VIR", "United States Virgin Islands"),
  Uzbekistan("+998", "UZ", "UZB", "Uzbekistan"),
  Vanuatu("+678", "VU", "VUT", "Vanuatu"),
  Vatican("+379", "VA", "VAT", "Vatican City"),
  Venezuela("+58", "VE", "VEN", "Venezuela"),
  Vietnam("+84", "VN", "VNM", "Vietnam"),
  WallisFutuna("+681", "WF", "WLF", "Wallis and Futuna"),
  WesternSahara("+212", "EH", "ESH", "Western Sahara"),
  Yemen("+967", "YE", "YEM", "Yemen"),
  Zambia("+260", "ZM", "ZMB", "Zambia"),
  Zimbabwe("+263", "ZW", "ZWE", "Zimbabwe");

  private final String dialingCode;
  private final String isoAlpha2Code;
  private final String IsoAlpha3Code;
  private final String countryName;

  CountryCode(String dialingCode, String isoAlpha2Code, String IsoAlpha3Code, String countryName) {
    this.dialingCode = dialingCode;
    this.isoAlpha2Code = isoAlpha2Code;
    this.IsoAlpha3Code = IsoAlpha3Code;
    this.countryName = countryName;
  }
}