package net.evolveip.ossmosis.provisioning;

import io.grpc.Metadata;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowClientOptions;
import io.temporal.serviceclient.WorkflowServiceStubs;
import io.temporal.serviceclient.WorkflowServiceStubsOptions;
import io.temporal.worker.Worker;
import io.temporal.worker.WorkerFactory;
import java.util.Map;
import java.util.Set;
import net.evolveip.ossmosis.provisioning.utils.annotations.WorkflowImplementation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.data.util.AnnotatedTypeScanner;

@Configuration
public class TemporalConfig {

    private final ApplicationContext applicationContext;

    public TemporalConfig(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Value("${temporal.host}")
    private String temporalHost;

    @Value("${temporal.port}")
    private int temporalPort;

    @Value("${temporal.namespace}")
    private String temporalNamespace;

    @Value("${temporal.api.key}")
    private String API_KEY;

    @Bean
    public WorkflowServiceStubs workflowServiceStubs() {

        if (!temporalHost.equals("localhost")) {
            System.setProperty("com.google.protobuf.use_unsafe_pre22_gencode", "true");

            Metadata headers = new Metadata();
            Metadata.Key<String> apiKeyHeader = Metadata.Key.of("x-api-key",
                Metadata.ASCII_STRING_MARSHALLER);
            headers.put(apiKeyHeader, API_KEY);

            return WorkflowServiceStubs.newServiceStubs(
                WorkflowServiceStubsOptions.newBuilder()
                    .setEnableHttps(true)
                    .setTarget(temporalHost + ":" + temporalPort)
                    .setHeaders(headers)
                    .build()
            );
        }

        return WorkflowServiceStubs.newServiceStubs(
            WorkflowServiceStubsOptions.newBuilder()
                .setTarget(temporalHost + ":" + temporalPort)
                .build()
        );
    }

    @Bean
    public WorkflowClient workflowClient(WorkflowServiceStubs workflowServiceStubs) {
        return WorkflowClient.newInstance(
            workflowServiceStubs,
            WorkflowClientOptions.newBuilder()
                .setNamespace(temporalNamespace)
                .build()
        );
    }

    @Bean
    public WorkerFactory workerFactory(WorkflowClient workflowClient) {
        return WorkerFactory.newInstance(workflowClient);
    }

    @EventListener(org.springframework.context.event.ContextRefreshedEvent.class)
    public void registerWorkers() {
        WorkerFactory workerFactory = workerFactory(workflowClient(workflowServiceStubs()));
        Worker worker = workerFactory.newWorker("provisioning-queue");

        registerWorkflows(worker);
        registerActivities(worker);

        workerFactory.start();
    }

    private void registerWorkflows(Worker worker) {
        // Register all workflow implementation types
        // Workflow implementations must be registered by class, not by instance
        // Find all classes annotated with @WorkflowImplementation
        String basePackage = this.getClass().getPackage().getName();
        // Get the base package by removing the last segment (config)
        basePackage = basePackage.substring(0, basePackage.lastIndexOf('.'));

        AnnotatedTypeScanner workflowScanner = new AnnotatedTypeScanner(false,
            WorkflowImplementation.class);
        Set<Class<?>> workflowTypes = workflowScanner.findTypes(basePackage);

        // Register each workflow implementation type
        for (Class<?> workflowType : workflowTypes) {
            worker.registerWorkflowImplementationTypes(workflowType);
        }
    }

    private void registerActivities(Worker worker) {
        Map<String, Object> activityBeans = applicationContext.getBeansWithAnnotation(io.temporal.activity.ActivityInterface.class);

        activityBeans.values().forEach(activity -> {
            worker.registerActivitiesImplementations(activity);
        });
    }
}