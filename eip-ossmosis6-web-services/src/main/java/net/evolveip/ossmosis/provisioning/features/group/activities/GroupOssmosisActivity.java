package net.evolveip.ossmosis.provisioning.features.group.activities;

import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;
import net.evolveip.ossmosis.api.features.group.common.dto.GroupRequestDTO;
import net.evolveip.ossmosis.api.features.group.common.dto.GroupResponseDTO;
import net.evolveip.ossmosis.api.features.group.entity.Group;

@ActivityInterface
public interface GroupOssmosisActivity {

  @ActivityMethod
  Group addGroupToOssmosis(GroupRequestDTO groupRequestDTO);

  @ActivityMethod
  Group modifyGroupInOssmosis(GroupRequestDTO groupRequestDTO);

  @ActivityMethod
  void deleteGroupFromOssmosis(String enterpriseId, String groupId);

  @ActivityMethod
  GroupResponseDTO mapToGroupResponseDTO(Group group);
}
