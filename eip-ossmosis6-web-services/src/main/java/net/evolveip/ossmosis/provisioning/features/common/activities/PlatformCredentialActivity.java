package net.evolveip.ossmosis.provisioning.features.common.activities;

import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;
import net.evolveip.broadsoft.oci.entities.connection.BroadsoftOCIConnectionData;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;

@ActivityInterface
public interface PlatformCredentialActivity {

  @ActivityMethod
  BroadsoftOCIConnectionData getBroadsoftASCredentials(String enterpriseId);

  @ActivityMethod
  BroadsoftOCIConnectionData getBroadsoftNSCredentials(String enterpriseId);

  @ActivityMethod
  RedskyClientCredentials getRedskyCredentials(String enterpriseId);
}
