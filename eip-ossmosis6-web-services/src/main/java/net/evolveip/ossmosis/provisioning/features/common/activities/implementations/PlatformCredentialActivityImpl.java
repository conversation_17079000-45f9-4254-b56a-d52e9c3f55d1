package net.evolveip.ossmosis.provisioning.features.common.activities.implementations;

import io.temporal.failure.ApplicationFailure;
import net.evolveip.broadsoft.oci.entities.connection.BroadsoftOCIConnectionData;
import net.evolveip.ossmosis.api.features.enterprise.service.EnterpriseService;
import net.evolveip.ossmosis.api.features.platform.constant.PlatformCredentialConstants;
import net.evolveip.ossmosis.api.features.platform.entity.BroadsoftASCredential;
import net.evolveip.ossmosis.api.features.platform.entity.BroadsoftNSCredential;
import net.evolveip.ossmosis.api.features.platform.entity.PlatformCredential;
import net.evolveip.ossmosis.api.features.platform.entity.RedskyCredential;
import net.evolveip.ossmosis.api.features.platform.mapper.PlatformCredentialMapper;
import net.evolveip.ossmosis.api.features.platform.service.PlatformCredentialService;
import net.evolveip.ossmosis.api.features.platform.service.RootPlatformService;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;
import net.evolveip.ossmosis.api.utils.EncryptionUtil;
import net.evolveip.ossmosis.provisioning.features.common.activities.PlatformCredentialActivity;
import org.springframework.stereotype.Component;

@Component
public class PlatformCredentialActivityImpl implements PlatformCredentialActivity {

  private final EnterpriseService enterpriseService;
  private final PlatformCredentialService platformCredentialService;
  private final RootPlatformService rootPlatformService;
  private final PlatformCredentialMapper platformCredentialMapper;
  private final EncryptionUtil encryptionUtil;

  public PlatformCredentialActivityImpl(EnterpriseService enterpriseService,
      PlatformCredentialService platformCredentialService, RootPlatformService rootPlatformService,
      PlatformCredentialMapper platformCredentialMapper, EncryptionUtil encryptionUtil) {
    this.enterpriseService = enterpriseService;
    this.platformCredentialService = platformCredentialService;
    this.rootPlatformService = rootPlatformService;
    this.platformCredentialMapper = platformCredentialMapper;
    this.encryptionUtil = encryptionUtil;
  }

  @Override
  public BroadsoftOCIConnectionData getBroadsoftASCredentials(String enterpriseId) {
    PlatformCredential platformCredential = getPlatformCredential(enterpriseId,
        PlatformCredentialConstants.BROADSOFT_AS_CRED_TYPE);

    if (platformCredential == null) {
      throw ApplicationFailure.newNonRetryableFailure(String.format("Unable to retrieve broadsoft credentials. Enterprise ID=%s", enterpriseId), "NonRetryableException");
    }
    BroadsoftOCIConnectionData connectionData = platformCredentialMapper.toBroadsoftOCIConnectionData((BroadsoftASCredential) platformCredential);
    if (connectionData != null) {
      connectionData.setPassword(encryptPassword(connectionData.getPassword()));
    }

    return connectionData;
  }

  @Override
  public BroadsoftOCIConnectionData getBroadsoftNSCredentials(String enterpriseId) {
    PlatformCredential platformCredential = getPlatformCredential(enterpriseId,
        PlatformCredentialConstants.BROADSOFT_NS_CRED_TYPE);

    if (platformCredential == null) {
      throw ApplicationFailure.newNonRetryableFailure(String.format("Unable to retrieve broadsoft credentials. Enterprise ID=%s", enterpriseId), "NonRetryableException");
    }
    BroadsoftOCIConnectionData connectionData = platformCredentialMapper.toBroadsoftOCIConnectionData((BroadsoftNSCredential) platformCredential);
    if (connectionData != null) {
      connectionData.setPassword(encryptPassword(connectionData.getPassword()));
    }

    return connectionData;
  }

  @Override
  public RedskyClientCredentials getRedskyCredentials(String enterpriseId) {
    PlatformCredential platformCredential = getPlatformCredential(enterpriseId,
        PlatformCredentialConstants.REDSKY_CRED_TYPE);

    if (platformCredential == null) {
      throw ApplicationFailure.newNonRetryableFailure(String.format("Unable to retrieve redsky credentials. Enterprise ID=%s", enterpriseId), "NonRetryableException");
    }
    RedskyClientCredentials connectionData = platformCredentialMapper.toRedskyClientCredentials((RedskyCredential) platformCredential);
    if (connectionData != null) {
      connectionData.setPassword(encryptPassword(connectionData.getPassword()));
    }

    return connectionData;
  }

  private PlatformCredential getPlatformCredential(String enterpriseId, String credentialType) {
    Integer platformId = enterpriseService.processGetById(enterpriseId).getPlatform().getPlatformId();
    PlatformCredential platformCredential = platformCredentialService.getPlatformCredentialsByPlatformIdAndType(platformId, credentialType);
    if (platformCredential == null) {
      platformId = rootPlatformService.getRootPlatform().getPlatformId();
      platformCredential = platformCredentialService.getPlatformCredentialsByPlatformIdAndType(platformId, credentialType);
    }
    return platformCredential;
  }

  private String encryptPassword(String password) {
    return encryptionUtil.encrypt(password);
  }
}
