package net.evolveip.ossmosis.provisioning.features.group.activities.implementations;

import net.evolveip.ossmosis.api.features.group.entity.Group;
import net.evolveip.ossmosis.api.features.group.mapper.GroupMapper;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;
import net.evolveip.ossmosis.api.redsky.service.group.RedskyGroupService;
import net.evolveip.ossmosis.api.utils.EncryptionUtil;
import net.evolveip.ossmosis.provisioning.features.group.activities.GroupRedskyActivity;
import net.evolveip.redsky.client.geography.model.OrganizationalUnitTO;
import org.springframework.stereotype.Component;

@Component
public class GroupRedskyActivityImpl implements GroupRedskyActivity {

  private final RedskyGroupService redskyGroupService;
  private final GroupMapper groupMapper;
  private final EncryptionUtil encryptionUtil;

  public GroupRedskyActivityImpl(RedskyGroupService redskyGroupService, GroupMapper groupMapper,
      EncryptionUtil encryptionUtil) {
    this.redskyGroupService = redskyGroupService;
    this.groupMapper = groupMapper;
    this.encryptionUtil = encryptionUtil;
  }

  @Override
  public OrganizationalUnitTO addGroupToRedsky(Group group,
      RedskyClientCredentials redskyCredentials) {
    redskyCredentials.setPassword(decryptPassword(redskyCredentials.getPassword()));
    return redskyGroupService.processAddGroup(redskyCredentials, groupMapper.toRedskyBuildingCompactInputTO(group), group.getEnterpriseId());
  }

  @Override
  public OrganizationalUnitTO modifyGroupInRedsky(Group group,
      RedskyClientCredentials redskyCredentials) {
    redskyCredentials.setPassword(decryptPassword(redskyCredentials.getPassword()));
    return redskyGroupService.processModifyGroup(redskyCredentials, groupMapper.toRedskyBuildingEditCompactInputTO(group), group.getEnterpriseId(), group.getGroupId());
  }

  @Override
  public void deleteGroupFromRedsky(String enterpriseId, String groupId,
      RedskyClientCredentials redskyCredentials) {
    redskyCredentials.setPassword(decryptPassword(redskyCredentials.getPassword()));
    redskyGroupService.processDeleteGroup(redskyCredentials, enterpriseId, groupId);
  }

  private String decryptPassword(String password) {
    return encryptionUtil.decrypt(password);
  }
}
