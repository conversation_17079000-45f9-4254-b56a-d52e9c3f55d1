package net.evolveip.ossmosis.provisioning.features.enterprise.activities.implementations;

import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseRequestDTO;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseResponseDTO;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.enterprise.mapper.EnterpriseMapper;
import net.evolveip.ossmosis.api.features.enterprise.service.EnterpriseService;
import net.evolveip.ossmosis.provisioning.features.enterprise.activities.EnterpriseOssmosisActivity;
import org.springframework.stereotype.Component;

@Component
public class EnterpriseOssmosisActivityImpl implements EnterpriseOssmosisActivity {

  private final EnterpriseService enterpriseService;
  private final EnterpriseMapper enterpriseMapper;

  public EnterpriseOssmosisActivityImpl(EnterpriseService enterpriseService,
      EnterpriseMapper enterpriseMapper) {
    this.enterpriseService = enterpriseService;
    this.enterpriseMapper = enterpriseMapper;
  }

  @Override
  public Enterprise addEnterpriseToOssmosis(EnterpriseRequestDTO enterpriseRequestDTO) {
    return enterpriseService.createEnterprise(enterpriseRequestDTO);
  }

  @Override
  public Enterprise modifyEnterpriseInOssmosis(EnterpriseRequestDTO enterpriseRequestDTO) {
    return enterpriseService.modifyEnterprise(enterpriseRequestDTO);
  }

  @Override
  public void deleteEnterpriseFromOssmosis(String enterpriseId) {
    enterpriseService.deleteEnterprise(enterpriseId);
  }

  @Override
  public EnterpriseResponseDTO mapToEnterpriseResponseDTO(Enterprise enterprise) {
    return enterpriseMapper.toDTO(enterprise);
  }

  @Override
  public boolean didEnterpriseChangePlatform(EnterpriseRequestDTO enterpriseRequestDTO) {
    EnterpriseResponseDTO enterprise = enterpriseService.processGetById(enterpriseRequestDTO.getEnterpriseId());
    return enterprise.getPlatform().getPlatformId() != enterpriseRequestDTO.getPlatformId();
  }
}
