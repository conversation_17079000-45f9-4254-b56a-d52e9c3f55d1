package net.evolveip.ossmosis.provisioning.features.enterprise.workflows;

import io.temporal.workflow.WorkflowInterface;
import io.temporal.workflow.WorkflowMethod;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseRequestDTO;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseResponseDTO;

@WorkflowInterface
public interface EnterpriseCreateWorkflow {

  @WorkflowMethod
  EnterpriseResponseDTO execute(EnterpriseRequestDTO enterpriseRequestDTO);
}
