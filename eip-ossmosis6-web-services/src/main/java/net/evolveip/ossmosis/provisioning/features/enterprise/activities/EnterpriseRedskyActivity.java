package net.evolveip.ossmosis.provisioning.features.enterprise.activities;

import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;
import net.evolveip.redsky.client.administration.model.CompanyTO;

@ActivityInterface
public interface EnterpriseRedskyActivity {

  @ActivityMethod
  CompanyTO addEnterpriseToRedsky(Enterprise enterprise, RedskyClientCredentials redskyCredentials);

  @ActivityMethod
  CompanyTO modifyEnterpriseInRedsky(Enterprise enterprise, RedskyClientCredentials redskyCredentials);

  @ActivityMethod
  void deleteEnterpriseFromRedsky(String enterpriseId, RedskyClientCredentials redskyCredentials);
}
