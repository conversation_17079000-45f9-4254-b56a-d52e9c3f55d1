package net.evolveip.ossmosis.provisioning.features.enterprise.activities.implementations;

import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.enterprise.mapper.EnterpriseMapper;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;
import net.evolveip.ossmosis.api.redsky.service.enterprise.RedskyEnterpriseService;
import net.evolveip.ossmosis.api.utils.EncryptionUtil;
import net.evolveip.ossmosis.provisioning.features.enterprise.activities.EnterpriseRedskyActivity;
import net.evolveip.redsky.client.administration.model.CompanyTO;
import org.springframework.stereotype.Component;

@Component
public class EnterpriseRedskyActivityImpl implements EnterpriseRedskyActivity {

  private final RedskyEnterpriseService redskyEnterpriseService;
  private final EnterpriseMapper enterpriseMapper;
  private final EncryptionUtil encryptionUtil;

  public EnterpriseRedskyActivityImpl(RedskyEnterpriseService redskyEnterpriseService,
      EnterpriseMapper enterpriseMapper, EncryptionUtil encryptionUtil) {
    this.redskyEnterpriseService = redskyEnterpriseService;
    this.enterpriseMapper = enterpriseMapper;
    this.encryptionUtil = encryptionUtil;
  }

  @Override
  public CompanyTO addEnterpriseToRedsky(Enterprise enterprise,
      RedskyClientCredentials redskyCredentials) {
    redskyCredentials.setPassword(decryptPassword(redskyCredentials.getPassword()));
    return redskyEnterpriseService.processAddEnterprise(redskyCredentials, enterpriseMapper.toRedskyCompanyAddTO(enterprise));
  }

  @Override
  public CompanyTO modifyEnterpriseInRedsky(Enterprise enterprise,
      RedskyClientCredentials redskyCredentials) {
    redskyCredentials.setPassword(decryptPassword(redskyCredentials.getPassword()));
    return redskyEnterpriseService.processModifyEnterprise(redskyCredentials, enterpriseMapper.toRedskyCompanyEditTO(enterprise));
  }

  @Override
  public void deleteEnterpriseFromRedsky(String enterpriseId,
      RedskyClientCredentials redskyCredentials) {
    redskyCredentials.setPassword(decryptPassword(redskyCredentials.getPassword()));
    redskyEnterpriseService.processDeleteEnterprise(redskyCredentials, enterpriseId);
  }

  private String decryptPassword(String password) {
    return encryptionUtil.decrypt(password);
  }
}
