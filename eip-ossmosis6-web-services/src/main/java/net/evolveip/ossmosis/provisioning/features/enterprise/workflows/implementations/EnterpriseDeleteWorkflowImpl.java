package net.evolveip.ossmosis.provisioning.features.enterprise.workflows.implementations;

import io.temporal.failure.ApplicationFailure;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.broadsoft.oci.entities.connection.BroadsoftOCIConnectionData;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;
import net.evolveip.ossmosis.provisioning.config.TemporalActivityConfigHolder;
import net.evolveip.ossmosis.provisioning.features.common.activities.PlatformCredentialActivity;
import net.evolveip.ossmosis.provisioning.features.enterprise.activities.EnterpriseBroadsoftActivity;
import net.evolveip.ossmosis.provisioning.features.enterprise.activities.EnterpriseOssmosisActivity;
import net.evolveip.ossmosis.provisioning.features.enterprise.activities.EnterpriseRedskyActivity;
import net.evolveip.ossmosis.provisioning.features.enterprise.workflows.EnterpriseDeleteWorkflow;
import net.evolveip.ossmosis.provisioning.utils.annotations.WorkflowImplementation;

@Slf4j
@WorkflowImplementation
public class EnterpriseDeleteWorkflowImpl implements EnterpriseDeleteWorkflow {

  private boolean initialized = false;

  private EnterpriseOssmosisActivity enterpriseOssmosisActivity;
  private EnterpriseBroadsoftActivity enterpriseBroadsoftActivity;
  private EnterpriseRedskyActivity enterpriseRedskyActivity;
  private PlatformCredentialActivity platformCredentialActivity;

  @Override
  public void execute(String enterpriseId) {

    init();

    BroadsoftOCIConnectionData broadsoftCredentials = null;
    RedskyClientCredentials redskyCredentials = null;

    try {
      broadsoftCredentials = platformCredentialActivity.getBroadsoftASCredentials(enterpriseId);
    } catch (Exception e) {
      logger.error("Failed to retrieve broadsoft platform credentials for enterprise: {}", enterpriseId, e);
    }

    try {
      redskyCredentials = platformCredentialActivity.getRedskyCredentials(enterpriseId);
    } catch (Exception e) {
      logger.error("Failed to retrieve redsky platform credentials for enterprise: {}", enterpriseId, e);
    }

    try {
      enterpriseOssmosisActivity.deleteEnterpriseFromOssmosis(enterpriseId);
    } catch (Exception e) {
      logger.error("Failed to delete enterprise from Ossmosis: {}", enterpriseId, e);
      throw ApplicationFailure.newNonRetryableFailure(String.format("Failed to delete enterprise from Ossmosis. Enterprise ID=%s Reason=%s", enterpriseId, e.getMessage()), "NonRetryableException");
    }

    if (broadsoftCredentials != null) {
      try {
        enterpriseBroadsoftActivity.deleteEnterpriseFromBroadsoft(enterpriseId, broadsoftCredentials);
      } catch (Exception e) {
        logger.error("Failed to delete enterprise from Broadsoft: {}", enterpriseId, e);
      }
    }

    if (redskyCredentials != null) {
      try {
        enterpriseRedskyActivity.deleteEnterpriseFromRedsky(enterpriseId, redskyCredentials);
      } catch (Exception e) {
        logger.error("Failed to delete enterprise from Redsky: {}", enterpriseId, e);
      }
    }
  }

  private void init() {
    if (initialized) return;

    this.enterpriseOssmosisActivity = Workflow.newActivityStub(EnterpriseOssmosisActivity.class,
        TemporalActivityConfigHolder.getDefaultActivityOptions());
    this.enterpriseBroadsoftActivity = Workflow.newActivityStub(EnterpriseBroadsoftActivity.class,
        TemporalActivityConfigHolder.getThirdPartyActivityOptions());
    this.enterpriseRedskyActivity = Workflow.newActivityStub(EnterpriseRedskyActivity.class,
        TemporalActivityConfigHolder.getThirdPartyActivityOptions());
    this.platformCredentialActivity = Workflow.newActivityStub(PlatformCredentialActivity.class,
        TemporalActivityConfigHolder.getDefaultActivityOptions());
    initialized = true;
  }
}
