package net.evolveip.ossmosis.provisioning.features.group.workflows;

import io.temporal.workflow.WorkflowInterface;
import io.temporal.workflow.WorkflowMethod;
import net.evolveip.ossmosis.api.features.group.common.dto.GroupRequestDTO;
import net.evolveip.ossmosis.api.features.group.common.dto.GroupResponseDTO;

@WorkflowInterface
public interface GroupModifyWorkflow {

  @WorkflowMethod
  GroupResponseDTO execute(GroupRequestDTO groupRequestDTO);
}
