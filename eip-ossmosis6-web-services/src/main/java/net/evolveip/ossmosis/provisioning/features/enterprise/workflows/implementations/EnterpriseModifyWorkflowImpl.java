package net.evolveip.ossmosis.provisioning.features.enterprise.workflows.implementations;

import io.temporal.failure.ApplicationFailure;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.broadsoft.oci.entities.connection.BroadsoftOCIConnectionData;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseRequestDTO;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseResponseDTO;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;
import net.evolveip.ossmosis.provisioning.config.TemporalActivityConfigHolder;
import net.evolveip.ossmosis.provisioning.features.common.activities.PlatformCredentialActivity;
import net.evolveip.ossmosis.provisioning.features.enterprise.activities.EnterpriseBroadsoftActivity;
import net.evolveip.ossmosis.provisioning.features.enterprise.activities.EnterpriseOssmosisActivity;
import net.evolveip.ossmosis.provisioning.features.enterprise.activities.EnterpriseRedskyActivity;
import net.evolveip.ossmosis.provisioning.features.enterprise.activities.EnterpriseThirdPartyEmergencyCallingActivity;
import net.evolveip.ossmosis.provisioning.features.enterprise.workflows.EnterpriseModifyWorkflow;
import net.evolveip.ossmosis.provisioning.utils.annotations.WorkflowImplementation;

@Slf4j
@WorkflowImplementation
public class EnterpriseModifyWorkflowImpl implements EnterpriseModifyWorkflow {

  private boolean initialized = false;

  private EnterpriseOssmosisActivity enterpriseOssmosisActivity;
  private EnterpriseBroadsoftActivity enterpriseBroadsoftActivity;
  private EnterpriseRedskyActivity enterpriseRedskyActivity;
  private EnterpriseThirdPartyEmergencyCallingActivity enterpriseThirdPartyEmergencyCallingActivity;
  private PlatformCredentialActivity platformCredentialActivity;

  @Override
  public EnterpriseResponseDTO execute(EnterpriseRequestDTO enterpriseRequestDTO) {

    init();

    String enterpriseId = enterpriseRequestDTO.getEnterpriseId();
    Enterprise persistedEnterprise = null;

    BroadsoftOCIConnectionData broadsoftCredentials = null;
    RedskyClientCredentials redskyCredentials = null;

    boolean didEnterpriseChangePlatform = false;

    try {
      broadsoftCredentials = platformCredentialActivity.getBroadsoftASCredentials(enterpriseId);
    } catch (Exception e) {
      logger.error("Failed to retrieve broadsoft platform credentials for enterprise: {}", enterpriseId, e);
    }

    // If broadsoft provisioning is happening and the enterprise changed platforms, delete the enterprise entry from the old platform
    if (broadsoftCredentials != null) {
      try {
        didEnterpriseChangePlatform = enterpriseOssmosisActivity.didEnterpriseChangePlatform(
            enterpriseRequestDTO);
      } catch (Exception e) {
        logger.error("Failed checking if enterprise changed platforms", e);
      }

      if (didEnterpriseChangePlatform) {
        try {
          enterpriseBroadsoftActivity.deleteEnterpriseFromBroadsoft(
              enterpriseRequestDTO.getEnterpriseId(), broadsoftCredentials);
        } catch (Exception e) {
          logger.error("Failed to delete enterprise from Broadsoft: {}", enterpriseId, e);
        }
      }
    }

    try {
      persistedEnterprise = enterpriseOssmosisActivity.modifyEnterpriseInOssmosis(enterpriseRequestDTO);
    } catch (Exception e) {
      logger.error("Failed to modify enterprise in Ossmosis: {}", enterpriseId, e);
      throw ApplicationFailure.newNonRetryableFailure(String.format("Failed to modify enterprise in Ossmosis. Enterprise ID=%s Reason=%s", enterpriseId, e.getMessage()), "NonRetryableException");
    }

    // Get new broadsoft creds
    if (didEnterpriseChangePlatform) {
      try {
        broadsoftCredentials = platformCredentialActivity.getBroadsoftASCredentials(enterpriseId);
      } catch (Exception e) {
        logger.error("Failed to retrieve broadsoft platform credentials for enterprise: {}", enterpriseId, e);
      }
    }

    if (broadsoftCredentials != null) {
      try {
        // if the platform changed, add the enterprise to the new broadsoft platform - otherwise modify the existing one
        if (didEnterpriseChangePlatform) {
          enterpriseBroadsoftActivity.addEnterpriseToBroadsoft(persistedEnterprise, broadsoftCredentials);
        } else {
          enterpriseBroadsoftActivity.modifyEnterpriseInBroadsoft(persistedEnterprise,
              broadsoftCredentials);
        }
      } catch (Exception e) {
        logger.error("Failed to modify enterprise in Broadsoft: {}", enterpriseId, e);
      }
    }

    try {
      redskyCredentials = platformCredentialActivity.getRedskyCredentials(enterpriseId);
    } catch (Exception e) {
      logger.error("Failed to retrieve redsky platform credentials for enterprise: {}", enterpriseId, e);
    }

    if (redskyCredentials != null) {
      try {
        enterpriseRedskyActivity.modifyEnterpriseInRedsky(persistedEnterprise, redskyCredentials);
      } catch (Exception e) {
        logger.error("Failed to modify enterprise in Redsky: {}", enterpriseId, e);
      }
    }

    if (broadsoftCredentials != null && redskyCredentials != null) {
      try {
        enterpriseThirdPartyEmergencyCallingActivity.addEnterpriseHELDCredentialsToBroadsoft(
            enterpriseId, broadsoftCredentials,
            enterpriseThirdPartyEmergencyCallingActivity.getHELDCredentialsFromRedsky(enterpriseId,
                redskyCredentials));
      } catch (Exception e) {
        logger.error("Failed to add HELD credentials to Broadsoft: {}", enterpriseId, e);
      }
    }

    return enterpriseOssmosisActivity.mapToEnterpriseResponseDTO(persistedEnterprise);
  }

  private void init() {
    if (initialized) return;

    this.enterpriseOssmosisActivity = Workflow.newActivityStub(EnterpriseOssmosisActivity.class,
        TemporalActivityConfigHolder.getDefaultActivityOptions());
    this.enterpriseBroadsoftActivity = Workflow.newActivityStub(EnterpriseBroadsoftActivity.class,
        TemporalActivityConfigHolder.getThirdPartyActivityOptions());
    this.enterpriseRedskyActivity = Workflow.newActivityStub(EnterpriseRedskyActivity.class,
        TemporalActivityConfigHolder.getThirdPartyActivityOptions());
    this.enterpriseThirdPartyEmergencyCallingActivity = Workflow.newActivityStub(
        EnterpriseThirdPartyEmergencyCallingActivity.class,
        TemporalActivityConfigHolder.getThirdPartyActivityOptions());
    this.platformCredentialActivity = Workflow.newActivityStub(PlatformCredentialActivity.class,
        TemporalActivityConfigHolder.getDefaultActivityOptions());
    initialized = true;
  }
}
