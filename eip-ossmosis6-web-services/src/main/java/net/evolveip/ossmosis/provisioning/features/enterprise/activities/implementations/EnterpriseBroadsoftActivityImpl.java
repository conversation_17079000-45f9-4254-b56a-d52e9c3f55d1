package net.evolveip.ossmosis.provisioning.features.enterprise.activities.implementations;

import net.evolveip.broadsoft.oci.entities.connection.BroadsoftOCIConnectionData;
import net.evolveip.ossmosis.api.broadsoft.dto.SerializableOCIResponse;
import net.evolveip.ossmosis.api.broadsoft.service.enterprise.BroadsoftEnterpriseService;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.features.enterprise.mapper.EnterpriseMapper;
import net.evolveip.ossmosis.api.utils.EncryptionUtil;
import net.evolveip.ossmosis.provisioning.features.enterprise.activities.EnterpriseBroadsoftActivity;
import org.springframework.stereotype.Component;

@Component
public class EnterpriseBroadsoftActivityImpl implements EnterpriseBroadsoftActivity {

  private final BroadsoftEnterpriseService broadsoftEnterpriseService;
  private final EnterpriseMapper enterpriseMapper;
  private final EncryptionUtil encryptionUtil;

  public EnterpriseBroadsoftActivityImpl(BroadsoftEnterpriseService broadsoftEnterpriseService,
      EnterpriseMapper enterpriseMapper, EncryptionUtil encryptionUtil) {
    this.broadsoftEnterpriseService = broadsoftEnterpriseService;
    this.enterpriseMapper = enterpriseMapper;
    this.encryptionUtil = encryptionUtil;
  }

  @Override
  public SerializableOCIResponse addEnterpriseToBroadsoft(Enterprise enterprise,
      BroadsoftOCIConnectionData broadsoftCredentials) {
    broadsoftCredentials.setPassword(decryptPassword(broadsoftCredentials.getPassword()));
    return broadsoftEnterpriseService.processAddRequestSerializable(broadsoftCredentials, enterpriseMapper.toBroadsoftServiceProviderAddRequest(enterprise));
  }

  @Override
  public SerializableOCIResponse modifyEnterpriseInBroadsoft(Enterprise enterprise,
      BroadsoftOCIConnectionData broadsoftCredentials) {
    broadsoftCredentials.setPassword(decryptPassword(broadsoftCredentials.getPassword()));
    return broadsoftEnterpriseService.processModifyRequestSerializable(broadsoftCredentials, enterpriseMapper.toBroadsoftServiceProviderModifyRequest(enterprise));
  }

  @Override
  public void deleteEnterpriseFromBroadsoft(String enterpriseId,
      BroadsoftOCIConnectionData broadsoftCredentials) {
    broadsoftCredentials.setPassword(decryptPassword(broadsoftCredentials.getPassword()));
    broadsoftEnterpriseService.processDeleteRequestSerializable(broadsoftCredentials, enterpriseMapper.toBroadsoftServiceProviderDeleteRequest(enterpriseId));
  }

  private String decryptPassword(String password) {
    return encryptionUtil.decrypt(password);
  }
}
