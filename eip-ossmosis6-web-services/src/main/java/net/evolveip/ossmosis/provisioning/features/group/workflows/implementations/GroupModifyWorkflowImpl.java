package net.evolveip.ossmosis.provisioning.features.group.workflows.implementations;

import io.temporal.failure.ApplicationFailure;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.broadsoft.oci.entities.connection.BroadsoftOCIConnectionData;
import net.evolveip.ossmosis.api.features.group.common.dto.GroupRequestDTO;
import net.evolveip.ossmosis.api.features.group.common.dto.GroupResponseDTO;
import net.evolveip.ossmosis.api.features.group.entity.Group;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;
import net.evolveip.ossmosis.provisioning.config.TemporalActivityConfigHolder;
import net.evolveip.ossmosis.provisioning.features.common.activities.PlatformCredentialActivity;
import net.evolveip.ossmosis.provisioning.features.group.activities.GroupBroadsoftActivity;
import net.evolveip.ossmosis.provisioning.features.group.activities.GroupOssmosisActivity;
import net.evolveip.ossmosis.provisioning.features.group.activities.GroupRedskyActivity;
import net.evolveip.ossmosis.provisioning.features.group.activities.GroupThirdPartyEmergencyCallingActivity;
import net.evolveip.ossmosis.provisioning.features.group.workflows.GroupModifyWorkflow;
import net.evolveip.ossmosis.provisioning.utils.annotations.WorkflowImplementation;

@Slf4j
@WorkflowImplementation
public class GroupModifyWorkflowImpl implements GroupModifyWorkflow {

  private boolean initialized = false;

  private GroupOssmosisActivity groupOssmosisActivity;
  private GroupBroadsoftActivity groupBroadsoftActivity;
  private GroupRedskyActivity groupRedskyActivity;
  private GroupThirdPartyEmergencyCallingActivity groupThirdPartyEmergencyCallingActivity;
  private PlatformCredentialActivity platformCredentialActivity;

  @Override
  public GroupResponseDTO execute(GroupRequestDTO groupRequestDTO) {

    init();

    String enterpriseId = groupRequestDTO.getEnterpriseId();
    String groupId = groupRequestDTO.getGroupId();
    Group persistedGroup = null;

    try {
      persistedGroup = groupOssmosisActivity.modifyGroupInOssmosis(groupRequestDTO);
    } catch (Exception e) {
      logger.error("Failed to modify group in Ossmosis: {}", enterpriseId, groupId, e);
      throw ApplicationFailure.newNonRetryableFailure(String.format("Failed to modify group in Ossmosis. Enterprise ID=%s Group ID=%s Reason=%s", enterpriseId, groupId, e.getMessage()), "NonRetryableException");
    }

    BroadsoftOCIConnectionData broadsoftCredentials = null;
    RedskyClientCredentials redskyCredentials = null;

    try {
      broadsoftCredentials = platformCredentialActivity.getBroadsoftASCredentials(enterpriseId);
    } catch (Exception e) {
      logger.error("Failed to retrieve broadsoft platform credentials for enterprise: {}", enterpriseId, e);
    }

    if (broadsoftCredentials != null) {
      try {
        groupBroadsoftActivity.modifyGroupInBroadsoft(persistedGroup, broadsoftCredentials);
      } catch (Exception e) {
        logger.error("Failed to modify group in Broadsoft: {}", enterpriseId, groupId, e);
      }
    }

    try {
      redskyCredentials = platformCredentialActivity.getRedskyCredentials(enterpriseId);
    } catch (Exception e) {
      logger.error("Failed to retrieve redsky platform credentials for enterprise: {}", enterpriseId, e);
    }

    if (redskyCredentials != null) {
      try {
        groupRedskyActivity.modifyGroupInRedsky(persistedGroup, redskyCredentials);
      } catch (Exception e) {
        logger.error("Failed to modify group in Redsky: {}", enterpriseId, groupId, e);
      }
    }

    if (broadsoftCredentials != null && redskyCredentials != null) {
      try {
        groupThirdPartyEmergencyCallingActivity.modifyGroupThirdPartyEmergencyCallingInBroadsoft(
            groupRequestDTO, broadsoftCredentials);
      } catch (Exception e) {
        logger.error("Failed to enable third party emergency calling in Broadsoft: {}",
            enterpriseId, groupId, e);
      }
    }

    return groupOssmosisActivity.mapToGroupResponseDTO(persistedGroup);
  }

  private void init() {
    if (initialized) return;

    this.groupOssmosisActivity = Workflow.newActivityStub(GroupOssmosisActivity.class, TemporalActivityConfigHolder.getDefaultActivityOptions());
    this.groupBroadsoftActivity = Workflow.newActivityStub(GroupBroadsoftActivity.class, TemporalActivityConfigHolder.getThirdPartyActivityOptions());
    this.groupRedskyActivity = Workflow.newActivityStub(GroupRedskyActivity.class, TemporalActivityConfigHolder.getThirdPartyActivityOptions());
    this.groupThirdPartyEmergencyCallingActivity = Workflow.newActivityStub(GroupThirdPartyEmergencyCallingActivity.class, TemporalActivityConfigHolder.getThirdPartyActivityOptions());
    this.platformCredentialActivity = Workflow.newActivityStub(PlatformCredentialActivity.class, TemporalActivityConfigHolder.getDefaultActivityOptions());
    initialized = true;
  }
}
