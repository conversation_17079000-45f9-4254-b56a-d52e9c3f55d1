package net.evolveip.ossmosis.provisioning.features.enterprise.activities.implementations;

import net.evolveip.broadsoft.oci.entities.connection.BroadsoftOCIConnectionData;
import net.evolveip.ossmosis.api.broadsoft.dto.SerializableOCIResponse;
import net.evolveip.ossmosis.api.broadsoft.service.enterprise.BroadsoftEnterpriseService;
import net.evolveip.ossmosis.api.features.enterprise.mapper.EnterpriseMapper;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;
import net.evolveip.ossmosis.api.redsky.service.enterprise.RedskyEnterpriseService;
import net.evolveip.ossmosis.api.utils.EncryptionUtil;
import net.evolveip.ossmosis.provisioning.features.enterprise.activities.EnterpriseThirdPartyEmergencyCallingActivity;
import net.evolveip.redsky.client.administration.model.CompanySecretsTO;
import org.springframework.stereotype.Component;

@Component
public class EnterpriseThirdPartyEmergencyCallingActivityImpl implements
    EnterpriseThirdPartyEmergencyCallingActivity {

  private final RedskyEnterpriseService redskyEnterpriseService;
  private final BroadsoftEnterpriseService broadsoftEnterpriseService;
  private final EnterpriseMapper enterpriseMapper;
  private final EncryptionUtil encryptionUtil;

  public EnterpriseThirdPartyEmergencyCallingActivityImpl(
      RedskyEnterpriseService redskyEnterpriseService,
      BroadsoftEnterpriseService broadsoftEnterpriseService, EnterpriseMapper enterpriseMapper,
      EncryptionUtil encryptionUtil) {
    this.redskyEnterpriseService = redskyEnterpriseService;
    this.broadsoftEnterpriseService = broadsoftEnterpriseService;
    this.enterpriseMapper = enterpriseMapper;
    this.encryptionUtil = encryptionUtil;
  }

  @Override
  public CompanySecretsTO getHELDCredentialsFromRedsky(String enterpriseId, RedskyClientCredentials redskyCredentials) {
    redskyCredentials.setPassword(decryptPassword(redskyCredentials.getPassword()));
    return redskyEnterpriseService.processGetEnterpriseHELDCredentials(redskyCredentials, enterpriseId);
  }

  @Override
  public SerializableOCIResponse addEnterpriseHELDCredentialsToBroadsoft(String enterpriseId, BroadsoftOCIConnectionData broadsoftCredentials, CompanySecretsTO companySecrets) {
    broadsoftCredentials.setPassword(decryptPassword(broadsoftCredentials.getPassword()));
    return broadsoftEnterpriseService.processThirdPartyEmergencyCallingModifyRequestSerializable(broadsoftCredentials, enterpriseMapper.toBroadsoftThirdPartyEmergencyCallingModifyRequest(companySecrets, enterpriseId));
  }

  private String decryptPassword(String password) {
    return encryptionUtil.decrypt(password);
  }
}
