package net.evolveip.ossmosis.provisioning.features.group.activities.implementations;

import net.evolveip.ossmosis.api.features.group.common.dto.GroupRequestDTO;
import net.evolveip.ossmosis.api.features.group.common.dto.GroupResponseDTO;
import net.evolveip.ossmosis.api.features.group.entity.Group;
import net.evolveip.ossmosis.api.features.group.mapper.GroupMapper;
import net.evolveip.ossmosis.api.features.group.service.GroupService;
import net.evolveip.ossmosis.provisioning.features.group.activities.GroupOssmosisActivity;
import org.springframework.stereotype.Component;

@Component
public class GroupOssmosisActivityImpl implements GroupOssmosisActivity {

  private final GroupService groupService;
  private final GroupMapper groupMapper;

  public GroupOssmosisActivityImpl(GroupService groupService, GroupMapper groupMapper) {
    this.groupService = groupService;
    this.groupMapper = groupMapper;
  }

  @Override
  public Group addGroupToOssmosis(GroupRequestDTO groupRequestDTO) {
    return groupService.createGroup(groupRequestDTO);
  }

  @Override
  public Group modifyGroupInOssmosis(GroupRequestDTO groupRequestDTO) {
    return groupService.modifyGroup(groupRequestDTO);
  }

  @Override
  public void deleteGroupFromOssmosis(String enterpriseId, String groupId) {
    groupService.deleteGroup(enterpriseId, groupId);
  }

  @Override
  public GroupResponseDTO mapToGroupResponseDTO(Group group) {
    return groupMapper.toDTO(group);
  }
}
