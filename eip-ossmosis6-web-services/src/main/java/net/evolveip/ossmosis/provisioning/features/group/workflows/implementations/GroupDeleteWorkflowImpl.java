package net.evolveip.ossmosis.provisioning.features.group.workflows.implementations;

import io.temporal.failure.ApplicationFailure;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.broadsoft.oci.entities.connection.BroadsoftOCIConnectionData;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;
import net.evolveip.ossmosis.provisioning.config.TemporalActivityConfigHolder;
import net.evolveip.ossmosis.provisioning.features.common.activities.PlatformCredentialActivity;
import net.evolveip.ossmosis.provisioning.features.group.activities.GroupBroadsoftActivity;
import net.evolveip.ossmosis.provisioning.features.group.activities.GroupOssmosisActivity;
import net.evolveip.ossmosis.provisioning.features.group.activities.GroupRedskyActivity;
import net.evolveip.ossmosis.provisioning.features.group.workflows.GroupDeleteWorkflow;
import net.evolveip.ossmosis.provisioning.utils.annotations.WorkflowImplementation;

@Slf4j
@WorkflowImplementation
public class GroupDeleteWorkflowImpl implements GroupDeleteWorkflow {

  private boolean initialized = false;

  private GroupOssmosisActivity groupOssmosisActivity;
  private GroupBroadsoftActivity groupBroadsoftActivity;
  private GroupRedskyActivity groupRedskyActivity;
  private PlatformCredentialActivity platformCredentialActivity;

  @Override
  public void execute(String enterpriseId, String groupId) {

    init();

    try {
      groupOssmosisActivity.deleteGroupFromOssmosis(enterpriseId, groupId);
    } catch (Exception e) {
      logger.error("Failed to delete group from Ossmosis: {}", enterpriseId, groupId, e);
      throw ApplicationFailure.newNonRetryableFailure(String.format("Failed to delete group from Ossmosis. Enterprise ID=%s Group ID=%s Reason=%s", enterpriseId, groupId, e.getMessage()), "NonRetryableException");
    }

    BroadsoftOCIConnectionData broadsoftCredentials = null;
    RedskyClientCredentials redskyCredentials = null;

    try {
      broadsoftCredentials = platformCredentialActivity.getBroadsoftASCredentials(enterpriseId);
    } catch (Exception e) {
      logger.error("Failed to retrieve broadsoft platform credentials for enterprise: {}", enterpriseId, e);
    }

    if (broadsoftCredentials != null) {
      try {
        groupBroadsoftActivity.deleteGroupFromBroadsoft(enterpriseId, groupId,
            broadsoftCredentials);
      } catch (Exception e) {
        logger.error("Failed to delete group from Broadsoft: {}", enterpriseId, groupId, e);
      }
    }

    try {
      redskyCredentials = platformCredentialActivity.getRedskyCredentials(enterpriseId);
    } catch (Exception e) {
      logger.error("Failed to retrieve redsky platform credentials for enterprise: {}", enterpriseId, e);
    }

    if (redskyCredentials != null) {
      try {
        groupRedskyActivity.deleteGroupFromRedsky(enterpriseId, groupId, redskyCredentials);
      } catch (Exception e) {
        logger.error("Failed to delete group from Redsky: {}", enterpriseId, groupId, e);
      }
    }
  }

  private void init() {
    if (initialized) return;

    this.groupOssmosisActivity = Workflow.newActivityStub(GroupOssmosisActivity.class, TemporalActivityConfigHolder.getDefaultActivityOptions());
    this.groupBroadsoftActivity = Workflow.newActivityStub(GroupBroadsoftActivity.class, TemporalActivityConfigHolder.getThirdPartyActivityOptions());
    this.groupRedskyActivity = Workflow.newActivityStub(GroupRedskyActivity.class, TemporalActivityConfigHolder.getThirdPartyActivityOptions());
    this.platformCredentialActivity = Workflow.newActivityStub(PlatformCredentialActivity.class, TemporalActivityConfigHolder.getDefaultActivityOptions());
    initialized = true;
  }
}
