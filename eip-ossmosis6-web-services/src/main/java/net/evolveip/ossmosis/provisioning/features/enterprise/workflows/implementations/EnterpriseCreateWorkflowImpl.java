package net.evolveip.ossmosis.provisioning.features.enterprise.workflows.implementations;

import io.temporal.failure.ApplicationFailure;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.broadsoft.oci.entities.connection.BroadsoftOCIConnectionData;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseRequestDTO;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseResponseDTO;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;
import net.evolveip.ossmosis.provisioning.config.TemporalActivityConfigHolder;
import net.evolveip.ossmosis.provisioning.features.common.activities.PlatformCredentialActivity;
import net.evolveip.ossmosis.provisioning.features.enterprise.activities.EnterpriseBroadsoftActivity;
import net.evolveip.ossmosis.provisioning.features.enterprise.activities.EnterpriseOssmosisActivity;
import net.evolveip.ossmosis.provisioning.features.enterprise.activities.EnterpriseRedskyActivity;
import net.evolveip.ossmosis.provisioning.features.enterprise.activities.EnterpriseThirdPartyEmergencyCallingActivity;
import net.evolveip.ossmosis.provisioning.features.enterprise.workflows.EnterpriseCreateWorkflow;
import net.evolveip.ossmosis.provisioning.utils.annotations.WorkflowImplementation;

@Slf4j
@WorkflowImplementation
public class EnterpriseCreateWorkflowImpl implements EnterpriseCreateWorkflow {

  private boolean initialized = false;

  private EnterpriseOssmosisActivity enterpriseOssmosisActivity;
  private EnterpriseBroadsoftActivity enterpriseBroadsoftActivity;
  private EnterpriseRedskyActivity enterpriseRedskyActivity;
  private EnterpriseThirdPartyEmergencyCallingActivity enterpriseThirdPartyEmergencyCallingActivity;
  private PlatformCredentialActivity platformCredentialActivity;

  @Override
  public EnterpriseResponseDTO execute(EnterpriseRequestDTO enterpriseRequestDTO) {

    init();

    String enterpriseId = enterpriseRequestDTO.getEnterpriseId();
    Enterprise persistedEnterprise = null;

    try {
      persistedEnterprise = enterpriseOssmosisActivity.addEnterpriseToOssmosis(enterpriseRequestDTO);
    } catch (Exception e) {
      logger.error("Failed to add enterprise to Ossmosis: {}", enterpriseId, e);
      throw ApplicationFailure.newNonRetryableFailure(String.format("Failed to add enterprise to Ossmosis. Enterprise ID=%s Reason=%s", enterpriseId, e.getMessage()), "NonRetryableException");
    }

    BroadsoftOCIConnectionData broadsoftCredentials = null;
    RedskyClientCredentials redskyCredentials = null;

    try {
      broadsoftCredentials = platformCredentialActivity.getBroadsoftASCredentials(enterpriseId);
    } catch (Exception e) {
      logger.error("Failed to retrieve broadsoft platform credentials for enterprise: {}", enterpriseId, e);
    }

    if (broadsoftCredentials != null) {
      try {
        enterpriseBroadsoftActivity.addEnterpriseToBroadsoft(persistedEnterprise,
            broadsoftCredentials);
      } catch (Exception e) {
        logger.error("Failed to add enterprise to Broadsoft: {}", enterpriseId, e);
      }
    }

    try {
      redskyCredentials = platformCredentialActivity.getRedskyCredentials(enterpriseId);
    } catch (Exception e) {
      logger.error("Failed to retrieve redsky platform credentials for enterprise: {}", enterpriseId, e);
    }

    if (redskyCredentials != null) {
      try {
        enterpriseRedskyActivity.addEnterpriseToRedsky(persistedEnterprise, redskyCredentials);
      } catch (Exception e) {
        logger.error("Failed to add enterprise to Redsky: {}", enterpriseId, e);
      }
    }

    if (broadsoftCredentials != null && redskyCredentials != null) {
      try {
        enterpriseThirdPartyEmergencyCallingActivity.addEnterpriseHELDCredentialsToBroadsoft(
            enterpriseId, broadsoftCredentials,
            enterpriseThirdPartyEmergencyCallingActivity.getHELDCredentialsFromRedsky(enterpriseId,
                redskyCredentials));
      } catch (Exception e) {
        logger.error("Failed to add HELD credentials to Broadsoft: {}", enterpriseId, e);
      }
    }

    return enterpriseOssmosisActivity.mapToEnterpriseResponseDTO(persistedEnterprise);
  }

  private void init() {
    if (initialized) return;

    this.enterpriseOssmosisActivity = Workflow.newActivityStub(EnterpriseOssmosisActivity.class,
        TemporalActivityConfigHolder.getDefaultActivityOptions());
    this.enterpriseBroadsoftActivity = Workflow.newActivityStub(EnterpriseBroadsoftActivity.class,
        TemporalActivityConfigHolder.getThirdPartyActivityOptions());
    this.enterpriseRedskyActivity = Workflow.newActivityStub(EnterpriseRedskyActivity.class,
        TemporalActivityConfigHolder.getThirdPartyActivityOptions());
    this.enterpriseThirdPartyEmergencyCallingActivity = Workflow.newActivityStub(
        EnterpriseThirdPartyEmergencyCallingActivity.class,
        TemporalActivityConfigHolder.getThirdPartyActivityOptions());
    this.platformCredentialActivity = Workflow.newActivityStub(PlatformCredentialActivity.class,
        TemporalActivityConfigHolder.getDefaultActivityOptions());
    initialized = true;
  }
}
