package net.evolveip.ossmosis.provisioning.features.group.activities;

import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;
import net.evolveip.ossmosis.api.features.group.entity.Group;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;
import net.evolveip.redsky.client.geography.model.OrganizationalUnitTO;

@ActivityInterface
public interface GroupRedskyActivity {

  @ActivityMethod
  OrganizationalUnitTO addGroupToRedsky(Group group, RedskyClientCredentials redskyCredentials);

  @ActivityMethod
  OrganizationalUnitTO modifyGroupInRedsky(Group group, RedskyClientCredentials redskyCredentials);

  @ActivityMethod
  void deleteGroupFromRedsky(String enterpriseId, String groupId, RedskyClientCredentials redskyCredentials);
}
