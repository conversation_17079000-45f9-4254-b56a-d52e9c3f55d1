package net.evolveip.ossmosis.provisioning.controller;

import net.evolveip.ossmosis.api.features.enterprise.constant.EnterpriseConstants;
import net.evolveip.ossmosis.api.utils.response.ApiResponse;
import net.evolveip.ossmosis.provisioning.dto.TemporalWorkflow;
import net.evolveip.ossmosis.provisioning.dto.TemporalWorkflowPageWrapper;
import net.evolveip.ossmosis.provisioning.service.TemporalWorkflowStatusService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/provisioning-status")
public class TemporalWorkflowStatusController {

  private final TemporalWorkflowStatusService temporalWorkflowStatusService;

  public TemporalWorkflowStatusController(
      TemporalWorkflowStatusService temporalWorkflowStatusService) {
    this.temporalWorkflowStatusService = temporalWorkflowStatusService;
  }

  @Secured(EnterpriseConstants.ENTERPRISES_READ)
  @GetMapping("/workflows")
  public ResponseEntity<ApiResponse<TemporalWorkflowPageWrapper>> doGet(
      @RequestParam(value = "pageSize", required = false, defaultValue = "15") final int pageSize,
      @RequestParam(value = "nextPageToken", required = false, defaultValue = "") final String nextPageToken) {
    TemporalWorkflowPageWrapper workflows = temporalWorkflowStatusService.processGet(pageSize, nextPageToken);

    return ResponseEntity.ok(ApiResponse.create(workflows));
  }

  @Secured(EnterpriseConstants.ENTERPRISES_READ)
  @GetMapping("/{workflowId}/history/{runId}")
  public ResponseEntity<TemporalWorkflow> getWorkflowHistory(@PathVariable String workflowId,
      @PathVariable String runId) {

    TemporalWorkflow workflow = temporalWorkflowStatusService.processGetWorkflowHistory(workflowId, runId);

    return ResponseEntity.ok(workflow);
  }
}
