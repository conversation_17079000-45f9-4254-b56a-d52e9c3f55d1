package net.evolveip.ossmosis.provisioning.service;

import com.google.protobuf.ByteString;
import com.google.protobuf.Timestamp;
import io.temporal.api.common.v1.Payloads;
import io.temporal.api.common.v1.WorkflowExecution;
import io.temporal.api.history.v1.HistoryEvent;
import io.temporal.api.workflowservice.v1.GetWorkflowExecutionHistoryRequest;
import io.temporal.api.workflowservice.v1.ListWorkflowExecutionsRequest;
import io.temporal.api.workflowservice.v1.ListWorkflowExecutionsResponse;
import io.temporal.client.WorkflowClient;
import io.temporal.common.converter.DataConverter;
import io.temporal.common.converter.DefaultDataConverter;
import io.temporal.serviceclient.WorkflowServiceStubs;
import java.time.Duration;
import java.time.Instant;
import java.util.Base64;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import net.evolveip.ossmosis.api.features.enterprise.util.EnterpriseSearchAttributeUtil;
import net.evolveip.ossmosis.api.utils.service.AuthorizationService;
import net.evolveip.ossmosis.provisioning.dto.TemporalWorkflow;
import net.evolveip.ossmosis.provisioning.dto.TemporalWorkflowActivityHistory;
import net.evolveip.ossmosis.provisioning.dto.TemporalWorkflowPageWrapper;
import org.springframework.stereotype.Service;

@Service
public class TemporalWorkflowStatusService {

  private final WorkflowClient workflowClient;
  private final WorkflowServiceStubs workflowServiceStubs;
  private final DataConverter converter = DefaultDataConverter.newDefaultInstance();
  private final AuthorizationService authorizationService;
  private final EnterpriseSearchAttributeUtil enterpriseSearchAttributeUtil;

  public TemporalWorkflowStatusService(WorkflowClient workflowClient,
      WorkflowServiceStubs workflowServiceStubs, AuthorizationService authorizationService,
      EnterpriseSearchAttributeUtil enterpriseSearchAttributeUtil) {
    this.workflowClient = workflowClient;
    this.workflowServiceStubs = workflowServiceStubs;
    this.authorizationService = authorizationService;
    this.enterpriseSearchAttributeUtil = enterpriseSearchAttributeUtil;
  }

  public TemporalWorkflowPageWrapper processGet(int pageSize, String nextPageToken) {
    String namespace = workflowClient.getOptions().getNamespace();

    ListWorkflowExecutionsRequest.Builder request = ListWorkflowExecutionsRequest.newBuilder()
        .setNamespace(namespace)
        .setPageSize(pageSize)
        .setQuery(buildEnterpriseQuery());

    if (nextPageToken != null && !nextPageToken.isEmpty()) {
        request.setNextPageToken(ByteString.copyFrom(Base64.getDecoder().decode(nextPageToken)));
    }

    ListWorkflowExecutionsResponse response = workflowServiceStubs.blockingStub().listWorkflowExecutions(request.build());

    List<TemporalWorkflow> workflows = response.getExecutionsList().stream()
        .map(exec -> {
          Instant startTime = convertToInstant(exec.getStartTime());
          Instant closeTime = exec.hasCloseTime() ? convertToInstant(exec.getCloseTime()) : null;
          Duration duration = (closeTime != null) ? Duration.between(startTime, closeTime) : Duration.ZERO;

          return new TemporalWorkflow(
              exec.getExecution().getWorkflowId(),
              exec.getExecution().getRunId(),
              exec.getType().getName(),
              exec.getStatus().name(),
              startTime,
              closeTime,
              null,
              null,
              null,
              duration.toMillis(),
              null
          );
        }).collect(Collectors.toList());

    String newToken = null;
    ByteString rawNextToken = response.getNextPageToken();
    if (!rawNextToken.isEmpty()) {
      ListWorkflowExecutionsRequest nextPageRequest = request.setNextPageToken(rawNextToken).build();

      ListWorkflowExecutionsResponse nextResponse = workflowServiceStubs.blockingStub().listWorkflowExecutions(nextPageRequest);

      if (!nextResponse.getExecutionsList().isEmpty()) {
        newToken = Base64.getEncoder().encodeToString(rawNextToken.toByteArray());
      }
    }

    TemporalWorkflowPageWrapper pageWrapper = new TemporalWorkflowPageWrapper();
    pageWrapper.setWorkflows(workflows);
    pageWrapper.setNextPageToken(newToken);

    return pageWrapper;
  }

  public TemporalWorkflow processGetWorkflowHistory(String workflowId, String runId) {

    WorkflowExecution execution = WorkflowExecution.newBuilder()
        .setWorkflowId(workflowId)
        .setRunId(runId)
        .build();

    GetWorkflowExecutionHistoryRequest request = GetWorkflowExecutionHistoryRequest.newBuilder()
        .setNamespace(workflowClient.getOptions().getNamespace())
        .setExecution(execution)
        .build();

    List<HistoryEvent> events = workflowServiceStubs.blockingStub()
        .getWorkflowExecutionHistory(request)
        .getHistory()
        .getEventsList();

    Map<Long, TemporalWorkflowActivityHistory> activityHistoryMap = new HashMap<>();

    String workflowType = null, workflowStatus = null;
    Object workflowInput = null, workflowOutput = null;
    Instant workflowStartTime = null, workflowEndTime = null;

    for (HistoryEvent event : events) {
      switch (event.getEventType()) {
        case EVENT_TYPE_WORKFLOW_EXECUTION_STARTED -> {
          var attrs = event.getWorkflowExecutionStartedEventAttributes();
          workflowType = attrs.getWorkflowType().getName();
          workflowStartTime = convertToInstant(event.getEventTime());
          workflowInput = decodePayload(attrs.hasInput() ? attrs.getInput() : null);
          workflowStatus = "running";
        }
        case EVENT_TYPE_WORKFLOW_EXECUTION_COMPLETED -> {
          var attrs = event.getWorkflowExecutionCompletedEventAttributes();
          workflowEndTime = convertToInstant(event.getEventTime());
          workflowStatus = "completed";
          workflowOutput = decodePayload(attrs.hasResult() ? attrs.getResult() : null);
        }
        case EVENT_TYPE_WORKFLOW_EXECUTION_FAILED ->  {
          var attrs = event.getWorkflowExecutionFailedEventAttributes();
          workflowEndTime = convertToInstant(event.getEventTime());
          workflowStatus = "failed";
          workflowOutput = attrs.getFailure().getMessage();
        }
        case EVENT_TYPE_WORKFLOW_EXECUTION_TERMINATED -> {
          var attrs = event.getWorkflowExecutionTerminatedEventAttributes();
          workflowEndTime = convertToInstant(event.getEventTime());
          workflowStatus = "terminated";
          workflowOutput = attrs.getReason();
        }
        case EVENT_TYPE_ACTIVITY_TASK_SCHEDULED -> {
          var attrs = event.getActivityTaskScheduledEventAttributes();
          Instant scheduledTime = convertToInstant(event.getEventTime());
          TemporalWorkflowActivityHistory history = new TemporalWorkflowActivityHistory(
              attrs.getActivityType().getName(),
              scheduledTime,
              null,
              "scheduled",
              decodePayload(attrs.hasInput() ? attrs.getInput() : null),
              null,
              null,
              0
          );
          activityHistoryMap.put(event.getEventId(), history);
        }
        case EVENT_TYPE_ACTIVITY_TASK_STARTED -> {
          var attrs = event.getActivityTaskStartedEventAttributes();
          TemporalWorkflowActivityHistory history = activityHistoryMap.get(attrs.getScheduledEventId());
          if (history != null) {
            history.setStatus("running");
          }
        }
        case EVENT_TYPE_ACTIVITY_TASK_COMPLETED -> {
          var attrs = event.getActivityTaskCompletedEventAttributes();
          TemporalWorkflowActivityHistory history = activityHistoryMap.get(attrs.getScheduledEventId());
          if (history != null) {
            Instant completedTime = convertToInstant(event.getEventTime());
            history.setCompletedTime(completedTime);
            history.setStatus("completed");
            history.setResult(decodePayload(attrs.hasResult() ? attrs.getResult() : null));

            long durationMillis = calculateDuration(history.getScheduledTime().toString(), completedTime.toString());
            history.setDuration(durationMillis);
          }
        }

        case EVENT_TYPE_ACTIVITY_TASK_FAILED -> {
          var attrs = event.getActivityTaskFailedEventAttributes();
          TemporalWorkflowActivityHistory history = activityHistoryMap.get(attrs.getScheduledEventId());
          if (history != null) {
            Instant failedTime = convertToInstant(event.getEventTime());
            history.setCompletedTime(failedTime);
            history.setStatus("failed");
            history.setError(attrs.getFailure().getMessage());

            long durationMillis = calculateDuration(history.getScheduledTime().toString(), failedTime.toString());
            history.setDuration(durationMillis);
          }
        }
      }
    }

    List<TemporalWorkflowActivityHistory> finalActivityHistory = activityHistoryMap.values().stream()
        .sorted(Comparator.comparing(TemporalWorkflowActivityHistory::getScheduledTime))
        .collect(Collectors.toList());

    if (workflowStatus.equals("terminated")) {
      finalActivityHistory.forEach(activityHistory -> {
        if (activityHistory.getStatus().equals("running") || activityHistory.getStatus().equals("scheduled")) {
          activityHistory.setStatus("terminated");
        }
      });
    }

    TemporalWorkflow workflow = new TemporalWorkflow(
        workflowId,
        runId,
        workflowType,
        workflowStatus,
        workflowStartTime,
        workflowEndTime,
        workflowInput,
        workflowStatus.equals("completed") ? workflowOutput : null,
        (workflowStatus.equals("failed") || workflowStatus.equals("terminated")) ? workflowOutput.toString() : null,
        !workflowStatus.equals("running") ? calculateDuration(workflowStartTime.toString(), workflowEndTime.toString()) : 0,
        finalActivityHistory
    );

    return workflow;
  }

  private Instant convertToInstant(Timestamp timestamp) {
    return Instant.ofEpochSecond(timestamp.getSeconds(), timestamp.getNanos());
  }

  private long calculateDuration(String scheduledTimeStr, String completedTimeStr) {
    try {
      Instant scheduledTime = Instant.parse(scheduledTimeStr);
      Instant completedTime = Instant.parse(completedTimeStr);
      return Duration.between(scheduledTime, completedTime).toMillis();
    } catch (Exception e) {
      return 0;
    }
  }

  private Object decodePayload(Payloads payloads) {
    if (payloads == null || payloads.getPayloadsCount() == 0) {
      return "No payload";
    }

    try {
      return converter.fromPayload(payloads.getPayloads(0), Object.class, Object.class);
    } catch (Exception e) {
      return "Unable to decode payload: " + e.getMessage();
    }
  }

  private String buildEnterpriseQuery() {
    String enterpriseId = authorizationService.getCurrentUserEnterprise().getEnterpriseId();
    String enterpriseIdPath = enterpriseSearchAttributeUtil.buildEnterpriseParentString(enterpriseId, null);
    if (enterpriseId != null && !enterpriseId.isEmpty()) {
      String query = String.format("enterpriseIdPath STARTS_WITH \"%s\"", enterpriseIdPath);
      return query;
    }

    return "";
  }
}
