package net.evolveip.ossmosis.provisioning.features.enterprise.activities;

import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;
import net.evolveip.broadsoft.oci.entities.connection.BroadsoftOCIConnectionData;
import net.evolveip.ossmosis.api.broadsoft.dto.SerializableOCIResponse;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;

@ActivityInterface
public interface EnterpriseBroadsoftActivity {

  @ActivityMethod
  SerializableOCIResponse addEnterpriseToBroadsoft(Enterprise enterprise, BroadsoftOCIConnectionData broadsoftCredentials);

  @ActivityMethod
  SerializableOCIResponse modifyEnterpriseInBroadsoft(Enterprise enterprise, BroadsoftOCIConnectionData broadsoftCredentials);

  @ActivityMethod
  void deleteEnterpriseFromBroadsoft(String enterpriseId, BroadsoftOCIConnectionData broadsoftCredentials);
}
