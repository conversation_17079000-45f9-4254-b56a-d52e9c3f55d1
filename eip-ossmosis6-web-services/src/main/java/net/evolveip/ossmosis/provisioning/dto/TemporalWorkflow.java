package net.evolveip.ossmosis.provisioning.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
public class TemporalWorkflow {
  private String workflowId;
  private String runId;
  private String type;
  private String status;
  private Instant startTime;
  private Instant closeTime;
  private Object input;
  private Object result;
  private String error;
  private long duration;
  private List<TemporalWorkflowActivityHistory> activityHistory;
}