package net.evolveip.ossmosis.provisioning.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import java.time.Instant;

@Data
@AllArgsConstructor
public class TemporalWorkflowActivityHistory {
  private String activityType;
  private Instant scheduledTime;
  private Instant completedTime;
  private String status;
  private Object input;
  private Object result;
  private String error;
  private long duration;
}