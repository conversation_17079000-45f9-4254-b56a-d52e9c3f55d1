package net.evolveip.ossmosis.provisioning.features.enterprise.activities;

import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseRequestDTO;
import net.evolveip.ossmosis.api.features.enterprise.dto.EnterpriseResponseDTO;
import net.evolveip.ossmosis.api.features.enterprise.entity.Enterprise;

@ActivityInterface
public interface EnterpriseOssmosisActivity {

  @ActivityMethod
  Enterprise addEnterpriseToOssmosis(EnterpriseRequestDTO enterpriseRequestDTO);

  @ActivityMethod
  Enterprise modifyEnterpriseInOssmosis(EnterpriseRequestDTO enterpriseRequestDTO);

  @ActivityMethod
  void deleteEnterpriseFromOssmosis(String enterpriseId);

  @ActivityMethod
  EnterpriseResponseDTO mapToEnterpriseResponseDTO(Enterprise enterprise);

  @ActivityMethod
  boolean didEnterpriseChangePlatform(EnterpriseRequestDTO enterpriseRequestDTO);
}
