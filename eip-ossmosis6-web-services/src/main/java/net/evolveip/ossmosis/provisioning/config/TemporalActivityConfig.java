package net.evolveip.ossmosis.provisioning.config;

import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import java.time.Duration;
import net.evolveip.ossmosis.provisioning.constants.QueueConstants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

@Configuration
public class TemporalActivityConfig {

  @Value("${temporal.default.retry-max-attempts}")
  private int defaultMaxAttempts;

  @Value("${temporal.default.start-to-close-timeout}")
  private Duration defaultStartToCloseTimeout;

  @Value("${temporal.default.schedule-to-close-timeout}")
  private Duration defaultScheduleToCloseTimeout;

  @Value("${temporal.third-party.retry-max-attempts}")
  private int thirdPartyMaxAttempts;

  @Value("${temporal.third-party.start-to-close-timeout}")
  private Duration thirdPartyStartToCloseTimeout;

  @Value("${temporal.third-party.schedule-to-close-timeout}")
  private Duration thirdPartyScheduleToCloseTimeout;

  @EventListener(ApplicationReadyEvent.class)
  public void initTemporalConfigs() {
    ActivityOptions defaultOptions = buildActivityOptions(defaultMaxAttempts, defaultStartToCloseTimeout, defaultScheduleToCloseTimeout);
    ActivityOptions thirdPartyOptions = buildActivityOptions(thirdPartyMaxAttempts, thirdPartyStartToCloseTimeout, thirdPartyScheduleToCloseTimeout);

    TemporalActivityConfigHolder.init(defaultOptions, thirdPartyOptions);
  }

  private ActivityOptions buildActivityOptions(int maxAttempts, Duration startToClose, Duration scheduleToClose) {
    RetryOptions retryOptions = RetryOptions.newBuilder()
        .setMaximumAttempts(maxAttempts)
        .setInitialInterval(Duration.ofSeconds(10))
        .setMaximumInterval(Duration.ofSeconds(60))
        .setBackoffCoefficient(2)
        .setDoNotRetry(QueueConstants.NON_RETRYABLE_EXCEPTION)
        .build();

    return ActivityOptions.newBuilder()
        .setStartToCloseTimeout(startToClose)
        .setScheduleToCloseTimeout(scheduleToClose)
        .setRetryOptions(retryOptions)
        .build();
  }
}
