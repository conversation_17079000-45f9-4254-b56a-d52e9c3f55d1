package net.evolveip.ossmosis.provisioning.config;

import io.temporal.activity.ActivityOptions;

public class TemporalActivityConfigHolder {

  private static ActivityOptions defaultActivityOptions;
  private static ActivityOptions thirdPartyActivityOptions;

  public static void init(ActivityOptions defaultOptions, ActivityOptions thirdPartyOptions) {
    TemporalActivityConfigHolder.defaultActivityOptions = defaultOptions;
    TemporalActivityConfigHolder.thirdPartyActivityOptions = thirdPartyOptions;
  }

  public static ActivityOptions getDefaultActivityOptions() {
    return defaultActivityOptions;
  }

  public static ActivityOptions getThirdPartyActivityOptions() {
    return thirdPartyActivityOptions;
  }
}
