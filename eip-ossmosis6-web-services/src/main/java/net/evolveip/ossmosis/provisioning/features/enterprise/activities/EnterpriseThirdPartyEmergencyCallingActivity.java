package net.evolveip.ossmosis.provisioning.features.enterprise.activities;

import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;
import net.evolveip.broadsoft.oci.entities.connection.BroadsoftOCIConnectionData;
import net.evolveip.ossmosis.api.broadsoft.dto.SerializableOCIResponse;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;
import net.evolveip.redsky.client.administration.model.CompanySecretsTO;

@ActivityInterface
public interface EnterpriseThirdPartyEmergencyCallingActivity {

  @ActivityMethod
  CompanySecretsTO getHELDCredentialsFromRedsky(String enterpriseId, RedskyClientCredentials redskyCredentials);

  @ActivityMethod
  SerializableOCIResponse addEnterpriseHELDCredentialsToBroadsoft(String enterpriseId, BroadsoftOCIConnectionData broadsoftCredentials, CompanySecretsTO companySecretsTO);
}
