<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>net.evolveip.ossmosis.api</groupId>
    <artifactId>eip-ossmosis-api</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>

  <artifactId>eip-ossmosis6-web-services</artifactId>

  <properties>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <hibernate.validator.version>8.0.1.Final</hibernate.validator.version>
    <springdocs.open.ai.starter>2.3.0</springdocs.open.ai.starter>
    <io.jsonwebtoken>0.11.5</io.jsonwebtoken>
    <net.snowflake.version>3.13.14</net.snowflake.version>
    <com.h2database.version>2.2.220</com.h2database.version>
    <springframework.security.test.version>6.2.1</springframework.security.test.version>
    <org.postgresql.postgresql.version>42.7.2</org.postgresql.postgresql.version>
    <spring.boot.starter.quartz.version>3.3.5</spring.boot.starter.quartz.version>
    <spring.boot.starter.mail.version>3.3.5</spring.boot.starter.mail.version>
    <jackson.version>2.17.1</jackson.version>
    <com.opencsv.version>5.9</com.opencsv.version>
    <com.itextpdf.version>5.5.13.2</com.itextpdf.version>
    <org.apache.poi.version>4.1.2</org.apache.poi.version>
    <libphonenumber.version>8.13.48</libphonenumber.version>
    <elastic-apm.version>1.52.1</elastic-apm.version>
    <jolokia.version>1.7.2</jolokia.version>
    <jackson-databind-nullable.version>0.2.6</jackson-databind-nullable.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-jpa</artifactId>
      <version>${spring.boot.version}</version>
    </dependency>

    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
      <version>${org.postgresql.postgresql.version}</version>
    </dependency>
    <!-- Fixes Critical Vuln, Remove once fixed version included in spring-boot-starter-web -->
    <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-core</artifactId>
        <version>10.1.34</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
      <version>${spring.boot.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
      <version>${spring.boot.version}</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <version>${spring.boot.version}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
      <version>${springdocs.open.ai.starter}</version>
    </dependency>

    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator</artifactId>
      <version>${hibernate.validator.version}</version>
    </dependency>

    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-api</artifactId>
      <version>${io.jsonwebtoken}</version>
    </dependency>

    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-impl</artifactId>
      <version>${io.jsonwebtoken}</version>
    </dependency>

    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-jackson</artifactId>
      <version>${io.jsonwebtoken}</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-devtools</artifactId>
      <version>${spring.boot.version}</version>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>net.snowflake</groupId>
      <artifactId>snowflake-jdbc</artifactId>
      <version>${net.snowflake.version}</version>
    </dependency>

    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <version>${com.h2database.version}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-test</artifactId>
      <version>${springframework.security.test.version}</version>
      <scope>test</scope>
    </dependency>

    <!-- https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-quartz -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-quartz</artifactId>
      <version>${spring.boot.starter.quartz.version}</version>
    </dependency>

    <!-- https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-mail -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-mail</artifactId>
      <version>${spring.boot.starter.mail.version}</version>
    </dependency>

    <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.datatype/jackson-datatype-jsr310 -->
    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jsr310</artifactId>
      <version>${jackson.version}</version>
    </dependency>

    <!-- https://mvnrepository.com/artifact/com.opencsv/opencsv -->
    <dependency>
      <groupId>com.opencsv</groupId>
      <artifactId>opencsv</artifactId>
      <version>${com.opencsv.version}</version>
    </dependency>

    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>itextpdf</artifactId>
      <version>${com.itextpdf.version}</version>
    </dependency>

    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
      <version>${org.apache.poi.version}</version>
    </dependency>

    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
      <version>${org.apache.poi.version}</version>
    </dependency>

    <dependency>
      <groupId>com.googlecode.libphonenumber</groupId>
      <artifactId>libphonenumber</artifactId>
      <version>${libphonenumber.version}</version> <!-- Use the latest version -->
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
      <version>${spring.boot.version}</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-logging</artifactId>
      <version>${spring.boot.version}</version>
    </dependency>

    <dependency>
      <groupId>org.openapitools</groupId>
      <artifactId>jackson-databind-nullable</artifactId>
      <version>${jackson-databind-nullable.version}</version>
    </dependency>

    <dependency>
      <groupId>co.elastic.apm</groupId>
      <artifactId>apm-agent-attach</artifactId>
      <version>${elastic-apm.version}</version>
    </dependency>

    <dependency>
      <groupId>org.jolokia</groupId>
      <artifactId>jolokia-core</artifactId>
      <version>${jolokia.version}</version>
    </dependency>

    <dependency>
      <groupId>net.evolveip.ossmosis.api</groupId>
      <artifactId>broadsoft-provisioning</artifactId>
      <version>0.0.1-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>net.evolveip.ossmosis.api</groupId>
      <artifactId>e911-provisioning</artifactId>
      <version>0.0.1-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>io.temporal</groupId>
      <artifactId>temporal-sdk</artifactId>
      <version>${io.temporal-sdk.version}</version>
    </dependency>

    <dependency>
      <groupId>io.temporal</groupId>
      <artifactId>temporal-testing</artifactId>
      <version>${io.temporal-sdk.version}</version>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <layers>
            <enabled>true</enabled>
            <!-- Uncomment the following line if you want a custom layer configuration -->
            <!-- <configuration>${project.basedir}/src/layers.xml</configuration> -->
          </layers>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>