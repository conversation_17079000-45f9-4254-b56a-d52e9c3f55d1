#!/bin/bash

# Usage: ./sonar-gate.sh --SONAR_URL https://sonarqube.lan --SONAR_KEY asdf --PROJECT project001 --VERSION 1
# Purpose: Based on the version check if that version is passing the sonarqube quality gate.
#
# AUTHOR(s):
# <PERSON> <when<PERSON><PERSON>@evolveip.net>
#
# Version: 20240710
#

# This is for just aligning the parameters
CHECK_MODE=FALSE
SONAR_URL=https://sonarqube01.evolveip.net
SONAR_KEY=${SONAR_KEY}
PROJECT=${PROJECT}
VERSION=${VERSION}
TIMEOUT=10
while [ $# -gt 0 ]; do
   if [[ $1 == *"--"* ]]; then
        param="${1/--/}"
        declare $param="$2"
   fi
  shift
done

exit_script() {
  # echo position 1 and exit with position 2 unless check mode is set to true
  echo $1
  CHECK_MODE=$(echo "${CHECK_MODE}" | tr '[a-z]' '[A-Z]')  # force capitalization
  if [ "${CHECK_MODE}" = "TRUE" ]; then
    exit 0
  fi
  exit $2
}

# Check if the jq package is installed
if ! [[ $(command -v jq) ]]; then
  exit_script "Please install jq and try again." 1
fi

# Check for required parameters
if [[ -z ${SONAR_URL} ]] || [[ -z ${SONAR_KEY} ]] || [[ -z ${PROJECT} ]] || [[ -z ${VERSION} ]]; then
  exit_script "SONAR_URL, SONAR_KEY, PROJECT, VERSION are required parameters." 1
fi

API="${SONAR_URL}/api"

# Verify that the API is up
HTTP_STATUS=$(curl -L -s -o /dev/null -I -w "%{http_code}" -u "${SONAR_KEY}":'' "${API}")
if [ "${HTTP_STATUS}" != "200" ]; then
  exit_script "API Endpoint ${API} seems to be down!" 1
fi

END=$((SECONDS+${TIMEOUT}))
while [ ${SECONDS} -lt ${END} ]; do
  COMPONENT=$(curl -s -u "${SONAR_KEY}":'' "${API}/components/show?component=${PROJECT}")
  COMPONENT_VERSION=$(echo ${COMPONENT} | jq -r '.component.version')
  if [ ${COMPONENT_VERSION} -ge ${VERSION} ]; then
    PROJECT_STATUS=$(curl -s -u "${SONAR_KEY}":'' "${API}/qualitygates/project_status?projectKey=${PROJECT}")
    STATUS=$(echo ${PROJECT_STATUS} | jq -r '.projectStatus.status')
    if [ "${STATUS}" = "OK" ]; then
      exit_script "SonarQube quality gate passing" 0
    fi
    exit_script "SonarQube quality gate failed" 1
  fi
done
exit_script "Timeout occurred waiting for project ${PROJECT} version ${VERSION}." 1
