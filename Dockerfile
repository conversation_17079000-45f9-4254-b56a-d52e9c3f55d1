ARG USERNAME=eip
ARG USER_UID=1001
ARG USER_GID=$USER_UID
ARG MAVEN_IMAGE=maven:3.9.6-amazoncorretto-17-al2023
ARG RUNTIME_IMAGE=amazoncorretto:17.0.15-al2023
############ DEV #############
# couldn't get buildkit caching to work nicely
# I'm expecting you to mount $HOME/.m2 to /root/.m2
# run mvn package:resolve on your own as needed
FROM ${MAVEN_IMAGE} as dev

RUN yum install -y make

########### BUILD ############
FROM ${MAVEN_IMAGE} AS builder

ARG SNYK_TOKEN
ARG SNYK_THRESHOLD=critical
ARG SONAR_HOST_URL
ARG SONAR_TOKEN
ARG PROJECT_VERSION
ENV SNYK_TOKEN=${SNYK_TOKEN} \
  SNYK_THRESHOLD=${SNYK_THRESHOLD} \
  SONAR_HOST_URL=${SONAR_HOST_URL} \
  SONAR_TOKEN=${SONAR_TOKEN} \
  SONAR_SCANNER_OPTS="-Dsonar.projectKey=eip-ossmosis6-api -Dsonar.projectVersion=${PROJECT_VERSION}"

WORKDIR /builder

COPY . .
COPY --from=snyk/snyk:linux /usr/local/bin/snyk /usr/local/bin/snyk

# this requires you to use the following command to build the image:
# DOCKER_BUILDKIT=1 docker build . --secret id=maven,src=$HOME/.m2/settings.xml
# we're assuming maven was pointed to nexus and the config file is in the default location
RUN --mount=type=secret,id=maven,target=/usr/share/maven/conf/settings.xml \
  --mount=type=cache,target=/root/.m2 \
  mvn clean install -DskipTests

# Snyk scan if SNYK_TOKEN
RUN if [[ -z "${SNYK_TOKEN}" ]]; then echo "Skipping Snyk"; else /usr/local/bin/snyk test --all-projects --org=evolve-ip --severity-threshold=${SNYK_THRESHOLD}; fi

# Sonar scan if SONAR_TOKEN 
RUN if [[ -n "${SONAR_TOKEN}" ]]; then \
  echo "SONAR_TOKEN detected. Setting up Sonar..."; \
    cd /tmp && \
    yum install -y unzip && \
    curl -O -s https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-4.8.1.3023-linux.zip && \
    unzip -qq sonar-scanner-cli-4.8.1.3023-linux.zip && \
    mv sonar-scanner-4.8.1.3023-linux /opt/sonar-scanner && \
    export PATH="$PATH:/opt/sonar-scanner/bin" && \
    cd /builder && \
    sonar-scanner -Dsonar.java.binaries=./eip-ossmosis6-web-services/target; \
  else \
    echo "Skipping Sonar setup and scanning (SONAR_TOKEN not set)."; \
  fi

# Ref https://docs.spring.io/spring-boot/reference/packaging/container-images/dockerfiles.html
RUN java -Djarmode=tools -jar /builder/eip-ossmosis6-web-services/target/*.jar extract --layers --destination /builder/extracted

########### Production Image ############
FROM ${RUNTIME_IMAGE} as production

# Add runtime user and group
ARG USERNAME
ARG USER_UID
ARG USER_GID
RUN echo -e "$USERNAME:x:$USER_GID:" >> /etc/group
RUN echo -e "$USERNAME:x:$USER_UID:$USER_GID::/nonexistent:/sbin/nologin" >> /etc/passwd

# Switch to runtime user and set working directory to /application
USER $USERNAME:$USERNAME
WORKDIR /application

# Copy the extracted jar contents from the builder container into the working directory in the runtime container
# Every copy step creates a new docker layer
# This allows docker to only pull the changes it really needs
# COPY --from=builder /builder/extracted/dependencies/ ./
# COPY --from=builder /builder/extracted/spring-boot-loader/ ./
# COPY --from=builder /builder/extracted/snapshot-dependencies/ ./
COPY --from=builder /builder/extracted/* ./ 

# Dynamically change name of the jar, if version name changes. 
RUN mv /application/eip-ossmosis6-web-services-*.jar /application/eip-ossmosis6-web-services.jar

# Start the application jar - this is not the uber jar used by the builder
# This jar only contains application code and references to the extracted jar files
# This layout is efficient to start up and CDS friendly
ENTRYPOINT ["java", "-jar", "eip-ossmosis6-web-services.jar"]
