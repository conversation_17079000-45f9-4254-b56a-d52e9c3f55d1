# this var is only necessary if you can't run docker without sudo
# if you can, just leave it empty, else set it to 'sudo'
SUDO?=


# ==================================================================================== #
# HELPERS
# ==================================================================================== #

## help: print this help message
.PHONY: help
help:
	@echo 'Usage:'
	@sed -n 's/^##//p' ${MAKEFILE_LIST} | column -t -s ':' |  sed -e 's/^/ /'

.PHONY: confirm
confirm:
	@echo -n 'Are you sure? [y/N] ' && read ans && [ $${ans:-N} = y ]

.PHONY: no-dirty
no-dirty:
	git diff --exit-code


# ==================================================================================== #
# QUALITY CONTROL
# ==================================================================================== #

## audit: TODO run quality control checks
.PHONY: audit
audit:
	# not implemented yet

## lint: TODO run linter
.PHONY: lint
lint:
	# not implemented yet

# ==================================================================================== #
# DEVELOPMENT
# ==================================================================================== #

## build: clean build the project with output to ./target/
.PHONY: build
build:
	mvn clean install -DskipTests

## run: clean build and run the project from jar artifact
.PHONY: run
run: build
	java -jar eip-ossmosis6-web-services/target/*.jar

## run/live: run the project from source code with hot reload provided by spring-boot-devtools
.PHONY: run/live
run/live:
	cd eip-ossmosis6-web-services && mvn spring-boot:run -Dspring-boot.run.jvmArguments="-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000"

## up: start full local environment with docker compose, includes app+db+db ui
.PHONY: up
up:
	$(SUDO) docker compose -f docker-compose.local.yaml up -d

## down: stop full local environment and delete it with docker compose
.PHONY: down
down:
	$(SUDO) docker compose -f docker-compose.local.yaml down

## stop: stop full local environment but no delete with docker compose
.PHONY: stop
stop:
	$(SUDO) docker compose -f docker-compose.local.yaml stop

## log: show logs of app container
.PHONY: log
log:
	$(SUDO) docker compose -f docker-compose.local.yaml logs -f local-dev

## shell: attach to the app container's shell
.PHONY: shell
shell:
	$(SUDO) docker compose -f docker-compose.local.yaml exec -it local-dev /bin/bash

## up/remote: TODO run the app with hot reload locally, but use remote db
.PHONY: up/remote
up/remote:
	# not implemented yet

## down/remote: TODO stop the local app that uses remote dependencies
.PHONY: down/remote
down/remote:
	# not implemented yet


## test: run test cases
.PHONY: test
test:
	mvn test

## testup: start full local environment with docker compose, for tests
.PHONY: testup
testup:
	$(SUDO) docker compose -f docker-compose.local.yaml run --rm local-dev make test


# ==================================================================================== #
# RELEASE
# ==================================================================================== #

## build/docker: build docker image
.PHONY: build/docker
build/docker:
	$(SUDO) DOCKER_BUILDKIT=1 docker build -t nexus.evolveip.net:5000/${IMAGE}:${TAG} .
