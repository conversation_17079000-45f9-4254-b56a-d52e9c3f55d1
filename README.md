# README #

This project represents the OSSmosis 6 API. The goal of this project is to provide an API for the OSSmosis 6 UI project.

## Requirements

* [JDK 17](https://www.oracle.com/java/technologies/downloads/#java17)
* [Maven 3.X](https://maven.apache.org/)

## How to run with docker compose ##

Requirements:

1. Recent (past 3 years) build of `docker`, because we use buildkit
1. You'll need `java` 
1. You'll need `maven` on your workstation **named or alised to mvn**, with appropriate config for nexus
1. You'll need `make` on your workstation (mac and linux already have it, use the scoop package on windows)
1. (Optional) `direnv` configured to read .env files

Procedure:

1. In the root directory create a `.env.properties` file and give it thecontents:
   Full contents are linked here in bitwarden: https://e-ip.link/jvrldd
    ```
      ENVIRONMENT=local
      DBENCSECRET=
      DBPASS=
      DBUSER=
      DBHOST=
      SNOW_USER=
      SNOW_PASS=
      RELAY_HOST=
      SNOW_HOST=
      OAUTH2_JWT_URI=
    ```
1. For full local dev, run `make up`. This will block until postgres is ready. TODO: For local code + remote dependencies, run `make up/remote`.
1. The outcome should be as follows:

    1. A postgres container will be created, it will load the sql statements in ./src/main/resources/sql/setup,
       and it will expose **localhost:5432** if you need it
    1. A pgadmin container will be created and configured to access the database and it will expose **http://localhost:8002**
    1. An application container will mount your code directory and your $HOME/.m2/ directory. It will then use spring devtools 
       to run an instance of the app that will reload on file save. Use `make log` if you want to watch the app's log output. 

          When the app loads it will be exposed at **http://localhost:8080** 

          Swagger will be reachable at **http://localhost:8080/ossmosis6/api/v1/swagger-ui/index.html#/**

    1. You will be able to attach your debugger to localhost:8000 via devtools proxy
    
          see: https://www.jetbrains.com/help/idea/tutorial-remote-debug.html

## How to run locally ##

In order to run these services locally you can run the embedded webserver.

## Start PostgreSQL Container ##

1. Download docker desktop latest version.
2. Download the latest PostgreSQL image in docker desktop. (postgres:16.1-bullseye)
3. Run the image you just downloaded and add these ENV variables
   - add a container name, example: ossmosis6_repo_container
   - Set ports = 5432
   - POSTGRES_USER=postgres
   - POSTGRES_PASSWORD= Same as DBPASS found in bitwarden
4. Download pgadmin4 latest to have a gui to connect to DB.
5. Connect to the DB with pgadmin as postgres with the user/pass above and run the command in
   resources/sql/setup/05.ossmosisOwnerRoleCreate.sql.
6. In Docker Desktop go to the container running and go to the exec tab.
   - create folder: mkdir -p '/var/lib/postgresql/ossmosis6_repo'
   - run this to set correct permissions on the folder: chown -R postgres:postgres
     /var/lib/postgresql/ossmosis6_repo
7. In the same shell that you created the ROLE ossmosis6_owner, run the command in
   resources/sql/setup/07.tablespaceCreate.sql
8. In the same shell that you created the ROLE ossmosis6_owner, create the database with the command
   in resources/sql/setup/08.databaseCreate.sql.
9. As ossmosis6_owner (password is the postgres password) run the commands in
   resources/sql/setup/09.schemaCreate.sql.
10. Run the sql script to populate the data after starting up o6 api and letting the api create the
    tables.

### Run Embedded Server ###

1. Go to EipOssmosisApiApplication.java
2. Make sure the ENV variable(s) are defined that are listed below. *** You do not need to define
   this if you want to target the LOCAL environment ***
3. Access via http://localhost:8080/

| Variable    | Value | Description                                               |
|-------------|-------|-----------------------------------------------------------|
| ENVIRONMENT| local    | Selects the proper internal resources to run the service. |
| DBENCSECRET|       | Postgres DB Encreption Secret |
| DBPASS|   | Postgres DB PASS |
| DBUSER|    | Postgres DB USER |
| SNOW_USER|     | Snowflake USER |
| SNOW_PASS |     | Snowflake PASS |
| SNOW_HOST |     | Snowflake URL |
| RELAY_HOST |  | Mail Relay Host URL |
| OAUTH2_JWT_URI |  | jkws URL for clearlogin tile |

### Code Styles ###

At this location: ./.idea/codeStyles/intellij-java-eip-style.xml is the configured code styles for the project based off of Google's code style.
Use this to reformat your code and set up your IDE to reformat the code and optimize imports before each commit.

### Change Log Level ###
Update Env Variable LOG_LEVEL


### Who do I talk to? ###

* Repo owner or admin
* Other community or team contact
