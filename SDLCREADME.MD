# SDLC Implementation for OSSmosis 6 Backend

## Project information

- **Project Name**: eip-ossmosis6-api
- **Project Key**: https://xtium.atlassian.net/jira/software/c/projects/OSS6/boards/445
- **Repository**: https://bitbucket.org/evolveip/eip-ossmosis6-api
- **Last Updated**: May 13th, 2025
- **Maintained By**: the OSSmosis Team - <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>,
  <PERSON>, <PERSON>

## Workflow implementation

### Jira workflow

| Required Element | Implementation                                                            | Notes/Deviations            |
|------------------|---------------------------------------------------------------------------|-----------------------------|
| Issue Types      | Epic, Story, Task, Spike, Bug                                             |                             |
| Workflow States  | Backlog > To Do > In Progress > Peer Reviewed > Ready for QA > UAT > Done |                             |
| Issue Hierarchy  | Epic > Story > Task                                                       | Spikes and bugs are lateral |
| Custom Fields    | N/A                                                                       |                             |

### Git standards

| Required Element      | Implementation              | Notes/Deviations                                              |
|-----------------------|-----------------------------|---------------------------------------------------------------|
| Commit Message Format | "OSS6-ticketnumber message" | Include ticket number with the commit                         |
| Branch Strategy       | Trunk-Based Development     | N/A                                                           |
| Release Naming        | Build Count                 | Automated                                                     |
| PR Guidelines         | Markdown template in repo   | Copy/Paste into bitbucket, no native support for PR templates |

## Security implementation

### Static analysis (SAST)

| Required Scan           | Tool Used | Configuration                           | Enforcement                    |
|-------------------------|-----------|-----------------------------------------|--------------------------------|
| Code Quality & Security | Snyk      | Configured in Snyk Cloud                | Block PRs with critical issues |
| Dependency Scanning     | Snyk      | Docker Container and Maven dependencies | Block PRs with critical issues |
| Infrastructure as Code  | Snyk      | Configured in Snyk Cloud                | Block PRs with critical issues |

### Secret scanning

| Required Scan            | Tool Used | Configuration                 | Enforcement                                    |
|--------------------------|-----------|-------------------------------|------------------------------------------------|
| Pre-Commit Scanning*     | TBD       | pre-commit hook configuration | secret scanning is done in the pre-commit hook |
| Repository Scanning*     | Snyk      | Configured in Snyk Cloud      | Block PRs with critical issues                 |
| CI/CD Pipeline Scanning* | Snyk      | Configured in Pipeline        | Critical issues are blocked                    |

### Code linting and formatting

| Language           | Linter/Formatter | Configuration                                                                                                                                           | Enforcement                                              |
|--------------------|------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------|
| Java w/Spring Boot | IntelliJ         | ".idea/intellij-java-eip-style.xml" loaded up as main style config in IntelliJ. Code analysis performed automatically as a pre-commit task by IntelliJ. | Runs before every commit to resolve any potential errors |
|

### Dynamic analysis (DAST)

| Required Scan             | Tool Used | Frequency  | Enforcement    |
|---------------------------|-----------|------------|----------------|
| API Security Testing*     | ZAP       | [When run] | [How enforced] |
| Web Application Scanning* | ZAP       | [When run] | [How enforced] |

## CI/CD Implementation

| Pipeline Stage | Tool/Service  | Configuration                                                                                                | Triggers                                        |
|----------------|---------------|--------------------------------------------------------------------------------------------------------------|-------------------------------------------------|
| Source Control | Bitbucket     | N/A                                                                                                          | N/A                                             |
| CI Server      | Bamboo        | In repo pipeline files                                                                                       | Merge into main/master branch                   |
| Build          | Bamboo        | In repo pipeline files                                                                                       | Every release/merge                             |
| Test           | Bamboo        | In repo pipeline files                                                                                       | Ran at build time                               |
| Deploy         | Bamboo        | In repo pipeline files                                                                                       | Orchestrated releases based on the sprint dates |
| Monitoring     | Elasticsearch | https://eip-elastic.kb.us-east-1.aws.found.io:9243/app/dashboards#/view/bb2a5ac0-ace8-11ef-abf4-13f4ddbe460c | Requests, payloads, errors                      |

## Metrics collection*

| Metric        | Data Source                                                                                                                        | Tool    | Reporting Frequency |
|---------------|------------------------------------------------------------------------------------------------------------------------------------|---------|---------------------|
| Velocity      | [Jira](https://xtium.atlassian.net/jira/software/c/projects/OSS6/boards/445/reports/velocity)                                      | DevLake | 2 weeks             |
| Cycle Time    | [Jira](https://xtium.atlassian.net/jira/software/c/projects/OSS6/reports/cycle-time)                                               | DevLake | 2 weeks             |
| Lead Time     | Jira                                                                                                                               | DevLake | 2 weeks             |
| Defect Rate   | [Jira](https://xtium.atlassian.net/plugins/servlet/ac/com.thed.zephyr.je/test-top-defects-chart?project.key=OSS6&project.id=14280) | DevLake | 2 weeks             |
| Test Coverage | Bamboo                                                                                                                             | DevLake | Every build         |

## Compliance

| Requirement      | Implementation    | Verification Method |
|------------------|-------------------|---------------------|
| [Compliance req] | [How implemented] | [How verified]      |
| [Compliance req] | [How implemented] | [How verified]      |

## Deviations and exceptions

| SDLC Requirement | Deviation        | Justification | Approval       | Expiration     |
|------------------|------------------|---------------|----------------|----------------|
| [Requirement]    | [How it differs] | [Why needed]  | [Who approved] | [When expires] |

## Action items

| Item                         | Description   | Owner   | Due Date | Status   |
|------------------------------|---------------|---------|----------|----------|
| Implement pre-commit hooks   | [Description] | [Owner] | [Date]   | [Status] |
| Implement repo scanning      | [Description] | [Owner] | [Date]   | [Status] |
| Implement metrics collecting |               |         |          |          |

