<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>net.evolveip.ossmosis.api</groupId>
    <artifactId>eip-ossmosis-api</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>

  <artifactId>broadsoft-provisioning</artifactId>

  <properties>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <eip-broadsoft-oci.version>2.5-SNAPSHOT</eip-broadsoft-oci.version>
    <eip-broadsoft-oci-as-ns-pojo-r24.version>1.1</eip-broadsoft-oci-as-ns-pojo-r24.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>net.evolveip</groupId>
      <artifactId>eip-broadsoft-oci-api</artifactId>
      <version>${eip-broadsoft-oci.version}</version>
    </dependency>
    <dependency>
      <groupId>net.evolveip</groupId>
      <artifactId>eip-broadsoft-oci-as-ns-pojo-r24</artifactId>
      <version>${eip-broadsoft-oci-as-ns-pojo-r24.version}</version>
    </dependency>
  </dependencies>

</project>