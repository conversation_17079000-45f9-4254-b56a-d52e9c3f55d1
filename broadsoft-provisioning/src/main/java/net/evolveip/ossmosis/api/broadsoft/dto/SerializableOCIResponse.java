package net.evolveip.ossmosis.api.broadsoft.dto;

import java.io.Serial;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import net.evolveip.broadsoft.oci.pojo.base.OCIResponse;

@Getter
public class SerializableOCIResponse implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  private final String type;
  private final String debugInfo;
  private final Map<String, Object> fields = new HashMap<>();

  public SerializableOCIResponse() {
    this.type = null;
    this.debugInfo = null;
  }

  // This is a generic class to make OCIResponse objects serializable for use with temporal
  // Make custom serializable wrappers when appropriate
  public SerializableOCIResponse(OCIResponse response) {
    this.type = response.getClass().getSimpleName();
    this.debugInfo = response.getDebugInfo();

    Class<?> clazz = response.getClass();
    while (clazz != null && clazz != Object.class) {
      for (Field field : clazz.getDeclaredFields()) {
        try {
          field.setAccessible(true);
          Object value = field.get(response);
          if (value != null) {
            fields.put(field.getName(), extractValue(value));
          }
        } catch (IllegalAccessException ignored) {
        }
      }
      clazz = clazz.getSuperclass();
    }
  }

  private Object extractValue(Object value) {
    if (value == null) return null;

    if (value instanceof String || value instanceof Number || value instanceof Boolean || value instanceof Enum) {
      return value;
    }

    Package pkg = value.getClass().getPackage();
    if (pkg != null && pkg.getName().startsWith("javax.")) {
      return value.toString();
    }

    Map<String, Object> result = new HashMap<>();
    Class<?> clazz = value.getClass();
    while (clazz != null && clazz != Object.class) {
      for (Field field : clazz.getDeclaredFields()) {
        try {
          field.setAccessible(true);
          Object nestedValue = field.get(value);
          if (nestedValue != null) {
            result.put(field.getName(), extractValue(nestedValue));
          }
        } catch (IllegalAccessException ignored) {
        }
      }
      clazz = clazz.getSuperclass();
    }
    return result;
  }

}
