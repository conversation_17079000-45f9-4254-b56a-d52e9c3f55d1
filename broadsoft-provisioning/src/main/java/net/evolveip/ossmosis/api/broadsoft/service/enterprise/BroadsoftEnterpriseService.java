package net.evolveip.ossmosis.api.broadsoft.service.enterprise;

import java.util.Objects;
import net.evolveip.broadsoft.oci.entities.connection.BroadsoftOCIConnectionData;
import net.evolveip.broadsoft.oci.message.response.InterfaceBroadsoftOCIResponseWrapper;
import net.evolveip.broadsoft.oci.pojo.as.ServiceProviderGetRequest22V4;
import net.evolveip.broadsoft.oci.pojo.as.ServiceProviderConsolidatedAddRequest;
import net.evolveip.broadsoft.oci.pojo.as.ServiceProviderGetResponse22V4;
import net.evolveip.broadsoft.oci.pojo.as.ServiceProviderModifyRequest;
import net.evolveip.broadsoft.oci.pojo.as.ServiceProviderDeleteRequest;
import net.evolveip.broadsoft.oci.pojo.as.ServiceProviderThirdPartyEmergencyCallingModifyRequest;
import net.evolveip.broadsoft.oci.pojo.base.ErrorResponse;
import net.evolveip.broadsoft.oci.pojo.base.OCIResponse;
import net.evolveip.broadsoft.oci.pojo.base.SuccessResponse;
import net.evolveip.ossmosis.api.broadsoft.dto.SerializableOCIResponse;
import net.evolveip.ossmosis.api.broadsoft.service.BroadsoftRequestHandler;
import net.evolveip.ossmosis.api.broadsoft.utils.exceptions.BroadsoftNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class BroadsoftEnterpriseService {

  private final BroadsoftRequestHandler broadsoftRequestHandler;

  @Autowired
  public BroadsoftEnterpriseService(BroadsoftRequestHandler broadsoftRequestHandler) {
    this.broadsoftRequestHandler = broadsoftRequestHandler;
  }

  public SerializableOCIResponse processGetRequestSerializable(BroadsoftOCIConnectionData connectionData, ServiceProviderGetRequest22V4 enterpriseGetRequest) {
    return new SerializableOCIResponse(processGetRequest(connectionData, enterpriseGetRequest));
  }

  public OCIResponse processGetRequest(BroadsoftOCIConnectionData connectionData, ServiceProviderGetRequest22V4 enterpriseGetRequest) {
    InterfaceBroadsoftOCIResponseWrapper responseWrapper = getBroadsoftEnterprise(connectionData, enterpriseGetRequest);

    OCIResponse firstResponse = responseWrapper.getFirstResponse();
    if (firstResponse instanceof ServiceProviderGetResponse22V4) {
      return firstResponse;
    } else {
      String message = "Unable to retrieve Broadsoft enterprise";
      if (firstResponse instanceof ErrorResponse) {
        message = message + ": " + ((ErrorResponse) firstResponse).getSummaryEnglish();
      }
      throw new BroadsoftNotFoundException(message);
    }
  }

  public SerializableOCIResponse processAddRequestSerializable(BroadsoftOCIConnectionData connectionData, ServiceProviderConsolidatedAddRequest enterpriseAddRequest) {
    return new SerializableOCIResponse(processAddRequest(connectionData, enterpriseAddRequest));
  }

  public OCIResponse processAddRequest(BroadsoftOCIConnectionData connectionData, ServiceProviderConsolidatedAddRequest enterpriseAddRequest) {
    InterfaceBroadsoftOCIResponseWrapper getResponseWrapper = getBroadsoftEnterprise(connectionData, mapToGetRequest(enterpriseAddRequest.getServiceProviderId()));

    OCIResponse getResponse = getResponseWrapper.getFirstResponse();
    if (getResponse instanceof ServiceProviderGetResponse22V4) {
      return getResponse;
    }

    InterfaceBroadsoftOCIResponseWrapper responseWrapper = broadsoftRequestHandler.sendRequest(connectionData, enterpriseAddRequest);

    OCIResponse firstResponse = responseWrapper.getFirstResponse();
    if (firstResponse instanceof SuccessResponse) {
      return firstResponse;
    } else {
      String message = "Unable to add Broadsoft enterprise";
      if (firstResponse instanceof ErrorResponse) {
        message = message + ": " + ((ErrorResponse) firstResponse).getSummaryEnglish();
      }
      throw new BroadsoftNotFoundException(message);
    }
  }

  public SerializableOCIResponse processModifyRequestSerializable(BroadsoftOCIConnectionData connectionData, ServiceProviderModifyRequest enterpriseModifyRequest) {
    return new SerializableOCIResponse(processModifyRequest(connectionData, enterpriseModifyRequest));
  }

  public OCIResponse processModifyRequest(BroadsoftOCIConnectionData connectionData, ServiceProviderModifyRequest enterpriseModifyRequest) {

    InterfaceBroadsoftOCIResponseWrapper getResponseWrapper = getBroadsoftEnterprise(connectionData, mapToGetRequest(enterpriseModifyRequest.getServiceProviderId()));

    OCIResponse getResponse = getResponseWrapper.getFirstResponse();
    if (getResponse instanceof ServiceProviderGetResponse22V4) {
      if (isUnchanged((ServiceProviderGetResponse22V4) getResponse, enterpriseModifyRequest)) {
        return getResponse;
      }
    } else {
      String message = "Enterprise not found in Broadsoft";
      throw new BroadsoftNotFoundException(message);
    }

    InterfaceBroadsoftOCIResponseWrapper responseWrapper = broadsoftRequestHandler.sendRequest(connectionData, enterpriseModifyRequest);

    OCIResponse firstResponse = responseWrapper.getFirstResponse();
    if (firstResponse instanceof SuccessResponse) {
      return firstResponse;
    } else {
      String message = "Unable to modify Broadsoft enterprise";
      if (firstResponse instanceof ErrorResponse) {
        message = message + ": " + ((ErrorResponse) firstResponse).getSummaryEnglish();
      }
      throw new BroadsoftNotFoundException(message);
    }
  }

  public SerializableOCIResponse processDeleteRequestSerializable(BroadsoftOCIConnectionData connectionData, ServiceProviderDeleteRequest enterpriseDeleteRequest) {
    return new SerializableOCIResponse(processDeleteRequest(connectionData, enterpriseDeleteRequest));
  }

  public OCIResponse processDeleteRequest(BroadsoftOCIConnectionData connectionData, ServiceProviderDeleteRequest enterpriseDeleteRequest) {

    InterfaceBroadsoftOCIResponseWrapper getResponseWrapper = getBroadsoftEnterprise(connectionData, mapToGetRequest(enterpriseDeleteRequest.getServiceProviderId()));

    OCIResponse getResponse = getResponseWrapper.getFirstResponse();
    if (!(getResponse instanceof ServiceProviderGetResponse22V4)) {
      return new SuccessResponse();
    }

    InterfaceBroadsoftOCIResponseWrapper responseWrapper = broadsoftRequestHandler.sendRequest(connectionData, enterpriseDeleteRequest);

    OCIResponse firstResponse = responseWrapper.getFirstResponse();
    if (firstResponse instanceof SuccessResponse) {
      return firstResponse;
    } else {
      String message = "Unable to delete Broadsoft enterprise";
      if (firstResponse instanceof ErrorResponse) {
        message = message + ": " + ((ErrorResponse) firstResponse).getSummaryEnglish();
      }
      throw new BroadsoftNotFoundException(message);
    }
  }

  public SerializableOCIResponse processThirdPartyEmergencyCallingModifyRequestSerializable(BroadsoftOCIConnectionData connectionData, ServiceProviderThirdPartyEmergencyCallingModifyRequest enterpriseThirdPartyEmergencyCallingModifyRequest) {
    return new SerializableOCIResponse(processThirdPartyEmergencyCallingModifyRequest(connectionData, enterpriseThirdPartyEmergencyCallingModifyRequest));
  }

  public OCIResponse processThirdPartyEmergencyCallingModifyRequest(BroadsoftOCIConnectionData connectionData, ServiceProviderThirdPartyEmergencyCallingModifyRequest enterpriseThirdPartyEmergencyCallingModifyRequest) {
    InterfaceBroadsoftOCIResponseWrapper responseWrapper = broadsoftRequestHandler.sendRequest(connectionData, enterpriseThirdPartyEmergencyCallingModifyRequest);

    OCIResponse firstResponse = responseWrapper.getFirstResponse();
    if (firstResponse instanceof SuccessResponse) {
      return firstResponse;
    } else {
      String message = "Unable to modify Broadsoft enterprise third party emergency calling";
      if (firstResponse instanceof ErrorResponse) {
        message = message + ": " + ((ErrorResponse) firstResponse).getSummaryEnglish();
      }
      throw new BroadsoftNotFoundException(message);
    }
  }

  private InterfaceBroadsoftOCIResponseWrapper getBroadsoftEnterprise(BroadsoftOCIConnectionData connectionData, ServiceProviderGetRequest22V4 enterpriseGetRequest) {
    return broadsoftRequestHandler.sendRequest(connectionData, enterpriseGetRequest);
  }

  private ServiceProviderGetRequest22V4 mapToGetRequest(String enterpriseId) {
    ServiceProviderGetRequest22V4 getRequest = new ServiceProviderGetRequest22V4();
    getRequest.setServiceProviderId(enterpriseId);
    return getRequest;
  }

  private boolean isUnchanged(ServiceProviderGetResponse22V4 getResponse, ServiceProviderModifyRequest enterpriseModifyRequest) {
    return Objects.equals(getResponse.getServiceProviderId(), enterpriseModifyRequest.getServiceProviderId()) &&
        Objects.equals(getResponse.getServiceProviderName(), enterpriseModifyRequest.getServiceProviderName().getValue()) &&
        Objects.equals(getResponse.getDefaultDomain(), enterpriseModifyRequest.getDefaultDomain());
  }
}
