package net.evolveip.ossmosis.api.broadsoft.service;

import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.broadsoft.oci.connection.BroadsoftOCIConnectionPool;
import net.evolveip.broadsoft.oci.connection.BroadsoftOCIConnectionStatus;
import net.evolveip.broadsoft.oci.connection.InterfaceBroadsoftOCIConnection;
import net.evolveip.broadsoft.oci.entities.connection.BroadsoftOCIConnectionData;
import net.evolveip.ossmosis.api.broadsoft.utils.exceptions.BroadsoftConnectionException;
import net.evolveip.ossmosis.api.broadsoft.utils.exceptions.BroadsoftUnknownException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class BroadsoftConnectionManagerService {

  @Value("${broadsoft.connection.maxRetries}")
  private int maxRetries;

  @Value("${broadsoft.connection.retryDelayMs}")
  private long retryDelayMs;

  public BroadsoftConnectionManagerService() {
  }

  public InterfaceBroadsoftOCIConnection getConnection(BroadsoftOCIConnectionData connectionData) {
    Optional.ofNullable(connectionData)
        .orElseThrow(() -> new BroadsoftConnectionException("No Broadsoft connection credentials found."));

    try {
      return tryConnectWithRetry(connectionData);
    } catch (Exception e) {
      String message = "Unexpected error retrieving Broadsoft connection";
      logger.error("{}: {}", message, e.getMessage(), e);
      throw new BroadsoftUnknownException(message);
    }
  }

  public void returnConnection(InterfaceBroadsoftOCIConnection connection) {
    BroadsoftOCIConnectionPool.getInstance().returnConnection(connection);
  }

  private InterfaceBroadsoftOCIConnection tryConnectWithRetry(BroadsoftOCIConnectionData connectionData) throws InterruptedException {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        InterfaceBroadsoftOCIConnection connection = BroadsoftOCIConnectionPool.getInstance().retrieveConnection(connectionData);

        if (connection.getConnectionStatus() == BroadsoftOCIConnectionStatus.CONNECTED_AND_AUTHENTICATED) {
          return connection;
        }

        logger.warn("Connection attempt {} failed: status {}", attempt, connection.getConnectionStatus());
      } catch (Exception e) {
        logger.warn("Attempt {} to connect failed: {}", attempt, e.getMessage(), e);
      }

      Thread.sleep(retryDelayMs);
    }

    throw new BroadsoftConnectionException("Failed to connect to Broadsoft after " + maxRetries + " attempts.");
  }
}