<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>net.evolveip.ossmosis.api</groupId>
    <artifactId>eip-ossmosis-api</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>

  <artifactId>e911-provisioning</artifactId>

  <properties>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <eip-redsky-client.version>1.0-SNAPSHOT</eip-redsky-client.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>net.evolveip</groupId>
      <artifactId>eip-redsky-client</artifactId>
      <version>${eip-redsky-client.version}</version>
    </dependency>
  </dependencies>

</project>