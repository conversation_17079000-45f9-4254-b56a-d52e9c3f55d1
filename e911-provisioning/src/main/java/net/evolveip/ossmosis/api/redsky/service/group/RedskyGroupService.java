package net.evolveip.ossmosis.api.redsky.service.group;

import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;
import net.evolveip.ossmosis.api.redsky.service.RedskyApiInstanceManager;
import net.evolveip.ossmosis.api.redsky.service.enterprise.RedskyEnterpriseService;
import net.evolveip.ossmosis.api.redsky.utils.exceptions.RedskyAddressValidationException;
import net.evolveip.ossmosis.api.redsky.utils.exceptions.RedskyNotFoundException;
import net.evolveip.ossmosis.api.redsky.utils.exceptions.RedskyUnknownException;
import net.evolveip.redsky.client.administration.model.CompanyTO;
import net.evolveip.redsky.client.geography.api.BuildingControllerApi;
import net.evolveip.redsky.client.geography.model.BuildingCompactInputTO;
import net.evolveip.redsky.client.geography.model.BuildingEditCompactInputTO;
import net.evolveip.redsky.client.geography.model.BuildingEditParsedInputTO;
import net.evolveip.redsky.client.geography.model.BuildingOperationResult;
import net.evolveip.redsky.client.geography.model.BuildingParsedInputTO;
import net.evolveip.redsky.client.geography.model.OrganizationalUnitTO;
import net.evolveip.redsky.okhttp.client.ApiException;
import net.evolveip.redsky.okhttp.client.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class RedskyGroupService {

    private static final String BUILDING_NAME_PROVIDED_ALREADY_EXISTS = "The building name provided already exists";

    private static final String ERROR_MESSAGE_PATTERN = "\"title\"\\s*:\\s*\"([^\"]+)\"";

    private final RedskyApiInstanceManager redskyApiInstanceManager;
    private final RedskyEnterpriseService redskyEnterpriseService;

    @Autowired
    public RedskyGroupService(RedskyApiInstanceManager redskyApiInstanceManager, RedskyEnterpriseService redskyEnterpriseService) {
        this.redskyApiInstanceManager = redskyApiInstanceManager;
        this.redskyEnterpriseService = redskyEnterpriseService;
    }

    public OrganizationalUnitTO processGetGroup(RedskyClientCredentials credentials, String enterpriseId, String groupId) {
        UUID orgId = redskyEnterpriseService.processGetEnterprise(credentials, enterpriseId).getId();

        OrganizationalUnitTO findBuildingResponse = findGroupByOrgId(credentials, orgId, groupId);

        if (findBuildingResponse == null) {
            String message = "Unable to retrieve group [" + groupId + "] from Redsky";
            throw new RedskyNotFoundException(message);
        }

        return findBuildingResponse;
    }

    public OrganizationalUnitTO processAddGroup(RedskyClientCredentials credentials, BuildingCompactInputTO buildingInfo, String enterpriseId) {
        BuildingControllerApi apiInstance = redskyApiInstanceManager.getBuildingControllerApiInstance(credentials);
        String authToken = redskyApiInstanceManager.getAuthToken(credentials);

        CompanyTO organization = redskyEnterpriseService.processGetEnterprise(credentials, enterpriseId);

        buildingInfo.getCompactAddressTO().setOrgId(organization.getId().toString());
        buildingInfo.getCompactAddressTO().setOrgName(organization.getName());

        if (buildingInfo.getParentOrganizationUnitId() == null) {
            buildingInfo.setParentOrganizationUnitId(organization.getId());
        }

        OrganizationalUnitTO existingBuilding = findGroupByOrgId(credentials, buildingInfo.getParentOrganizationUnitId(), getGroupId(buildingInfo.getName()));
        if (existingBuilding != null) {
            return existingBuilding;
        }

        OrganizationalUnitTO newBuilding = null;

        try {
            ApiResponse<BuildingOperationResult> addBuildingResponse = apiInstance.addBuildingCompactAddressUsingPOSTWithHttpInfo(authToken, buildingInfo);

            if (addBuildingResponse != null && addBuildingResponse.getStatusCode() == 200) {
                if (addBuildingResponse.getData().getAmbiguous() == true && addBuildingResponse.getData().getAddressList() != null && !addBuildingResponse.getData().getAddressList().isEmpty()) {
                    BuildingParsedInputTO newParsedBuilding = new BuildingParsedInputTO();
                    newParsedBuilding.setName(buildingInfo.getName());
                    newParsedBuilding.setSupplementalData(buildingInfo.getSupplementalData());
                    newParsedBuilding.setParentOrganizationUnitId(buildingInfo.getParentOrganizationUnitId());
                    newParsedBuilding.setParsedAddress(addBuildingResponse.getData().getAddressList().get(0));

                    ApiResponse<BuildingOperationResult> addNewBuildingResponse = apiInstance.addBuildingParsedAddressUsingPOSTWithHttpInfo(authToken, newParsedBuilding);

                    if (addNewBuildingResponse != null && addNewBuildingResponse.getStatusCode() == 200 && addNewBuildingResponse.getData().getAmbiguous() == false) {
                        newBuilding = addNewBuildingResponse.getData().getOrgUnitTO();
                    } else {
                        String message = "Unable to add group [" + getGroupId(buildingInfo.getName()) + "] to Redsky";
                        throw new RedskyNotFoundException(message);
                    }
                } else if (addBuildingResponse.getData().getAmbiguous() == false) {
                    newBuilding = addBuildingResponse.getData().getOrgUnitTO();
                } else {
                    String message = "Unable to validate address for group [" + getGroupId(buildingInfo.getName()) + "] in Redsky";
                    throw new RedskyAddressValidationException(message);
                }
            }
        } catch (ApiException e) {

            if (e.getResponseBody().contains(BUILDING_NAME_PROVIDED_ALREADY_EXISTS)) {
                UUID orgId = buildingInfo.getParentOrganizationUnitId();
                String groupId = getGroupId(buildingInfo.getName());

                OrganizationalUnitTO building = findGroupByOrgId(credentials, orgId, groupId);

                if (building != null) {
                    newBuilding = building;
                } else {
                    String message = "Unable to validate address for group [" + getGroupId(buildingInfo.getName()) + "] in Redsky";
                    throw new RedskyAddressValidationException(message);
                }
            } else {
                String message = "There was an unexpected error adding group [" + getGroupId(buildingInfo.getName()) + "] to Redsky";
                String detailMessage = extractErrorMessage(e.getResponseBody());
                if (!detailMessage.isEmpty()) message = message + ": " + detailMessage;
                logger.error("{}: {}", message, e.getMessage());
                throw new RedskyUnknownException(message);
            }
        }

        return newBuilding;
    }

    public OrganizationalUnitTO processModifyGroup(RedskyClientCredentials credentials, BuildingEditCompactInputTO buildingInfo, String enterpriseId, String groupId) {
        BuildingControllerApi apiInstance = redskyApiInstanceManager.getBuildingControllerApiInstance(credentials);
        String authToken = redskyApiInstanceManager.getAuthToken(credentials);

        OrganizationalUnitTO existingBuilding = processGetGroup(credentials, enterpriseId, groupId);
        if (existingBuilding == null) {
            throw new RedskyNotFoundException("Enterprise not found in Redsky");
        }

        if (isUnchanged(existingBuilding, buildingInfo)) {
            return existingBuilding;
        }

        buildingInfo.setBuildingId(existingBuilding.getId());

        CompanyTO organization = redskyEnterpriseService.processGetEnterprise(credentials, enterpriseId);
        buildingInfo.getCompactAddressTO().setOrgId(organization.getId().toString());
        buildingInfo.getCompactAddressTO().setOrgName(organization.getName());

        OrganizationalUnitTO newBuilding = null;

        try {            
            ApiResponse<BuildingOperationResult> modifyBuildingResponse = apiInstance.editBuildingCompactAddressUsingPUTWithHttpInfo(authToken, buildingInfo);

            if (modifyBuildingResponse != null && modifyBuildingResponse.getStatusCode() == 200) {
                if (modifyBuildingResponse.getData().getAmbiguous() == true && modifyBuildingResponse.getData().getAddressList() != null && !modifyBuildingResponse.getData().getAddressList().isEmpty()) {
                    BuildingEditParsedInputTO newParsedBuilding = new BuildingEditParsedInputTO();
                    newParsedBuilding.setName(buildingInfo.getName());
                    newParsedBuilding.setSupplementalData(buildingInfo.getSupplementalData());
                    newParsedBuilding.setParsedAddress(modifyBuildingResponse.getData().getAddressList().get(0));

                    ApiResponse<BuildingOperationResult> modifyNewBuildingResponse = apiInstance.editBuildingParsedAddressUsingPUTWithHttpInfo(authToken, newParsedBuilding);

                    if (modifyNewBuildingResponse != null && modifyNewBuildingResponse.getStatusCode() == 200 && modifyBuildingResponse.getData().getAmbiguous() == false) {
                        newBuilding = modifyNewBuildingResponse.getData().getOrgUnitTO();
                    } else {
                        String message = "Unable to modify group [" + getGroupId(buildingInfo.getName()) + "] in Redsky";
                        throw new RedskyNotFoundException(message);
                    }
                } else if (modifyBuildingResponse.getData().getAmbiguous() == false) {
                    newBuilding = modifyBuildingResponse.getData().getOrgUnitTO();
                } else {
                    String message = "Unable to validate address for group [" + getGroupId(buildingInfo.getName()) + "] in Redsky";
                    throw new RedskyAddressValidationException(message);
                }
            }
        } catch (ApiException e) {
            String message = "There was an unexpected error modifying group [" + getGroupId(buildingInfo.getName()) + "] in Redsky";
            String detailMessage = extractErrorMessage(e.getResponseBody());
            if (!detailMessage.isEmpty()) message = message + ": " + detailMessage;
            logger.error("{}: {}", message, e.getMessage());
            throw new RedskyUnknownException(message);
        }

        return newBuilding;
    }

    public void processDeleteGroup(RedskyClientCredentials credentials, String enterpriseId, String groupId) {
        BuildingControllerApi apiInstance = redskyApiInstanceManager.getBuildingControllerApiInstance(credentials);

        OrganizationalUnitTO building = findGroupByOrgId(credentials, redskyEnterpriseService.processGetEnterprise(credentials, enterpriseId).getId(), groupId);

        if (building != null) {
            try {
                ApiResponse<String> deleteBuildingResponse = apiInstance.deleteBuildingByIdUsingDELETEWithHttpInfo(
                    building.getId());
            } catch (ApiException e) {
                String message = "There was an unexpected error deleting group [" + groupId + "] from Redsky";
                String detailMessage = extractErrorMessage(e.getResponseBody());
                if (!detailMessage.isEmpty()) message = message + ": " + detailMessage;
                logger.error("{}: {}", message, e.getMessage());
                throw new RedskyUnknownException(message);
            }
        }
    }

    private OrganizationalUnitTO findGroupByOrgId(RedskyClientCredentials credentials, UUID parentId, String groupId) {
        BuildingControllerApi apiInstance = redskyApiInstanceManager.getBuildingControllerApiInstance(credentials);

        OrganizationalUnitTO foundBuilding = null;

        try {
            ApiResponse<List<OrganizationalUnitTO>> findBuildingResponse = apiInstance.findByParentIdUsingGETWithHttpInfo(parentId, null, null, null, null, null, null, null, groupId);

            if (findBuildingResponse != null && findBuildingResponse.getStatusCode() == 200) {
                for (OrganizationalUnitTO building : findBuildingResponse.getData()) {
                    if (getGroupId(building.getName()).equals(groupId)) {
                        foundBuilding = building;
                        break;
                    }
                }
            }
        } catch (ApiException e) {
            String message = "There was an unexpected error retrieving group [" + groupId + "] in Redsky";
            String detailMessage = extractErrorMessage(e.getResponseBody());
            if (!detailMessage.isEmpty()) message = message + ": " + detailMessage;
            logger.error("{}: {}", message, e.getMessage());
            throw new RedskyUnknownException(message);
        }

        return foundBuilding;
    }

    private String getGroupId(String buildingName) {
        return buildingName.substring(buildingName.lastIndexOf(";") + 1);
    }

    private String extractErrorMessage(String responseBody) {
        Pattern pattern = Pattern.compile(ERROR_MESSAGE_PATTERN);
        Matcher matcher = pattern.matcher(responseBody);

        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }

    private boolean isUnchanged(OrganizationalUnitTO existingBuilding, BuildingEditCompactInputTO buildingInfo) {
        return Objects.equals(existingBuilding.getSupplementalData(), buildingInfo.getSupplementalData()) &&
            Objects.equals(existingBuilding.getName(), buildingInfo.getName()) &&
            Objects.equals(existingBuilding.getAddress().getNormalizedAddress(), buildingInfo.getCompactAddressTO().getNormalizedAddress());
    }

}