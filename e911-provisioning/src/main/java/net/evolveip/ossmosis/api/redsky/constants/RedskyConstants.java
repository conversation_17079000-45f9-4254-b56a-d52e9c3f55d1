package net.evolveip.ossmosis.api.redsky.constants;

public class RedskyConstants {
  public static final String AUTH_NAME = "JWT";
  public static final String EXPIRATION = "exp";
  public static final String TOKEN_ID = "id";

  public static final String EMERGENCY_CALL_RECEIVED = "EMERGENCY_CALL_RECEIVED";
  public static final String STANDARD_EMERGENCY_CALL_TEMPLATE = "EIP Standard Emergency Call Alert Template";
  public static final String EIP_VALIDATE_ADDRESS_ENTERPRISE_ID = "eip-validateAddress";
  public static final String VALIDATE_ADDRESS_GROUP = "Validate Address Group";

  public static final String AUTH_SERVICE = "auth-service";
  public static final String GEOGRAPHY_SERVICE = "geography-service";
  public static final String ADMIN_SERVICE = "admin-service";

  public static final String DID_ROUTING_FALLBACK = "DID_ROUTING_FALLBACK";
} 