package net.evolveip.ossmosis.api.redsky.service.enterprise;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.redsky.constants.RedskyConstants;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;
import net.evolveip.ossmosis.api.redsky.service.RedskyApiInstanceManager;
import net.evolveip.ossmosis.api.redsky.utils.exceptions.RedskyNotFoundException;
import net.evolveip.ossmosis.api.redsky.utils.exceptions.RedskyUnknownException;
import net.evolveip.redsky.client.administration.api.CompanyControllerApi;
import net.evolveip.redsky.client.administration.api.CompanyFeaturesControllerApi;
import net.evolveip.redsky.client.administration.model.CompanyAddTO;
import net.evolveip.redsky.client.administration.model.CompanyEditTO;
import net.evolveip.redsky.client.administration.model.CompanySecretsTO;
import net.evolveip.redsky.client.administration.model.CompanyTO;
import net.evolveip.redsky.okhttp.client.ApiException;
import net.evolveip.redsky.okhttp.client.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RedskyEnterpriseService {

  private static final String ORGANIZATION_NAME_UNIQUE_ERROR = "Organization name must be unique";

  private static final String ERROR_MESSAGE_PATTERN = "\"title\"\\s*:\\s*\"([^\"]+)\"";

  private final RedskyApiInstanceManager redskyApiInstanceManager;

  @Autowired
  public RedskyEnterpriseService(RedskyApiInstanceManager redskyApiInstanceManager) {
    this.redskyApiInstanceManager = redskyApiInstanceManager;
  }

  public CompanyTO processGetEnterprise(RedskyClientCredentials credentials, String enterpriseId) {
    CompanyControllerApi apiInstance = redskyApiInstanceManager.getCompanyControllerApiInstance(credentials);

    CompanyTO company = getRedskyEnterprise(credentials, enterpriseId);

    if (company == null) {
      String message = "Unable to retrieve enterprise [" + enterpriseId + "] from Redsky";
      throw new RedskyNotFoundException(message);
    }

    return company;
  }

  public CompanyTO processAddEnterprise(RedskyClientCredentials credentials, CompanyAddTO enterpriseAddRequest) {
    CompanyControllerApi apiInstance = redskyApiInstanceManager.getCompanyControllerApiInstance(credentials);

    CompanyTO existingEnterprise = getRedskyEnterprise(credentials, enterpriseAddRequest.getExternalOrgId());
    if (existingEnterprise != null) {
      return existingEnterprise;
    }

    UUID parentId = redskyApiInstanceManager.getParentOrgIdFromToken(credentials);
    enterpriseAddRequest.setParentId(parentId);

    CompanyTO newCompany = null;

    try {
      ApiResponse<CompanyTO> addEnterpriseResponse = apiInstance.addCompanyUsingPOSTWithHttpInfo(enterpriseAddRequest);

      if (addEnterpriseResponse != null && addEnterpriseResponse.getStatusCode() == 200) {
        newCompany = addEnterpriseResponse.getData();
      } else {
        String message = "Unable to add enterprise [" + enterpriseAddRequest.getExternalOrgId() + "] to Redsky";
        throw new RedskyNotFoundException(message);
      }
    } catch (ApiException e) {

      if (e.getResponseBody().contains(ORGANIZATION_NAME_UNIQUE_ERROR)) {

        CompanyTO company = processGetEnterprise(credentials, enterpriseAddRequest.getExternalOrgId());

        if (company != null && company.getName().equals(enterpriseAddRequest.getName()) && company.getExternalOrgId().equals(enterpriseAddRequest.getExternalOrgId())) {
          newCompany = company;
        } else {
          String message = "Unable to add enterprise [" + enterpriseAddRequest.getExternalOrgId() + "] to Redsky";
          throw new RedskyNotFoundException(message);
        }
      } else {
        String message = "There was an unexpected error adding enterprise [" + enterpriseAddRequest.getExternalOrgId() + "] to Redsky";
        String detailMessage = extractErrorMessage(e.getResponseBody());
        if (!detailMessage.isEmpty()) message = message + ": " + detailMessage;
        logger.error("{}: {}", message, e.getMessage());
        throw new RedskyUnknownException(message);
      }
    }

    enableOrganizationDIDFallback(credentials, newCompany.getId(), newCompany.getExternalOrgId());

    return newCompany;
  }

  public CompanyTO processModifyEnterprise(RedskyClientCredentials credentials, CompanyEditTO enterpriseModifyRequest) {
    CompanyControllerApi apiInstance = redskyApiInstanceManager.getCompanyControllerApiInstance(credentials);

    UUID orgId = processGetEnterprise(credentials, enterpriseModifyRequest.getExternalOrgId()).getId();
    UUID parentId = redskyApiInstanceManager.getParentOrgIdFromToken(credentials);
    enterpriseModifyRequest.setId(orgId);
    enterpriseModifyRequest.setParentId(parentId);

    CompanyTO existingEnterprise = getRedskyEnterprise(credentials, enterpriseModifyRequest.getExternalOrgId());
    if (existingEnterprise == null) {
      throw new RedskyNotFoundException("Enterprise not found in Redsky");
    }

    if (isUnchanged(existingEnterprise, enterpriseModifyRequest)) {
      return existingEnterprise;
    }

    CompanyTO newCompany = null;

    try {      
      ApiResponse<CompanyTO> modifyEnterpriseResponse = apiInstance.editCompanyUsingPUTWithHttpInfo(enterpriseModifyRequest);

      if (modifyEnterpriseResponse != null && modifyEnterpriseResponse.getStatusCode() == 200) {
        newCompany = modifyEnterpriseResponse.getData();
      } else {
        String message = "Unable to modify enterprise [" + enterpriseModifyRequest.getExternalOrgId() + "] in Redsky";
        throw new RedskyNotFoundException(message);
      }
    } catch (ApiException e) {
      String message = "There was an unexpected error modifying enterprise [" + enterpriseModifyRequest.getExternalOrgId() + "] in Redsky";
      String detailMessage = extractErrorMessage(e.getResponseBody());
      if (!detailMessage.isEmpty()) message = message + ": " + detailMessage;
      logger.error("{}: {}", message, e.getMessage());
      throw new RedskyUnknownException(message);
    }

    enableOrganizationDIDFallback(credentials, newCompany.getId(), newCompany.getExternalOrgId());

    return newCompany;
  }

  public void processDeleteEnterprise(RedskyClientCredentials credentials, String enterpriseId) {
    CompanyControllerApi apiInstance = redskyApiInstanceManager.getCompanyControllerApiInstance(credentials);

    CompanyTO org = getRedskyEnterprise(credentials, enterpriseId);

    if (org != null) {
      UUID orgId = org.getId();

      try {
        ApiResponse<String> deleteEnterpriseResponse = apiInstance.deleteCompanyByIdUsingDELETEWithHttpInfo(
            orgId);
      } catch (ApiException e) {
        String message = "There was an unexpected error deleting enterprise [" + enterpriseId + "] from Redsky";
        String detailMessage = extractErrorMessage(e.getResponseBody());
        if (!detailMessage.isEmpty()) message = message + ": " + detailMessage;
        logger.error("{}: {}", message, e.getMessage());
        throw new RedskyUnknownException(message);
      }
    }
  }

  public CompanySecretsTO processGetEnterpriseHELDCredentials(RedskyClientCredentials credentials, String enterpriseId) {
    CompanyControllerApi apiInstance = redskyApiInstanceManager.getCompanyControllerApiInstance(credentials);

    UUID orgId = processGetEnterprise(credentials, enterpriseId).getId();

    CompanySecretsTO heldCredentials = null;

    try {
      ApiResponse<CompanySecretsTO> getEnterpriseHELDCredentialsResponse = apiInstance.getCompanySecretsUsingGETWithHttpInfo(orgId);

      if (getEnterpriseHELDCredentialsResponse != null && getEnterpriseHELDCredentialsResponse.getStatusCode() == 200) {
        heldCredentials = getEnterpriseHELDCredentialsResponse.getData();
      } else {
        String message = "Unable to retrieve enterprise [" + enterpriseId + "] HELD credentials from Redsky";
        throw new RedskyNotFoundException(message);
      }
    } catch (ApiException e) {
      String message = "There was an unexpected error retrieving enterprise [" + enterpriseId + "] HELD credentials from Redsky";
      String detailMessage = extractErrorMessage(e.getResponseBody());
      if (!detailMessage.isEmpty()) message = message + ": " + detailMessage;
      logger.error("{}: {}", message, e.getMessage());
      throw new RedskyUnknownException(message);
    }

    return heldCredentials;
  }

  private void enableOrganizationDIDFallback(RedskyClientCredentials credentials, UUID orgId, String enterpriseId) {
    CompanyFeaturesControllerApi apiInstance = redskyApiInstanceManager.getCompanyFeaturesControllerApiInstance(credentials);
    
    List<String> features = Arrays.asList(RedskyConstants.DID_ROUTING_FALLBACK);

    try {
      ApiResponse<Void> addCompanyFeaturesResponse = apiInstance.addCompanyFeaturesUsingPUTWithHttpInfo(orgId, features);
    } catch (ApiException e) {
      String message = "There was an unexpected error enabling DID Fallback for  enterprise [" + enterpriseId + "] in Redsky";
      String detailMessage = extractErrorMessage(e.getResponseBody());
      if (!detailMessage.isEmpty()) message = message + ": " + detailMessage;
      logger.error("{}: {}", message, e.getMessage());
      throw new RedskyUnknownException(message);
    }
  }

  private String extractErrorMessage(String responseBody) {
    Pattern pattern = Pattern.compile(ERROR_MESSAGE_PATTERN);
    Matcher matcher = pattern.matcher(responseBody);

    if (matcher.find()) {
      return matcher.group(1);
    }
    return "";
  }

  private CompanyTO getRedskyEnterprise(RedskyClientCredentials credentials, String enterpriseId) {
    CompanyControllerApi apiInstance = redskyApiInstanceManager.getCompanyControllerApiInstance(credentials);

    CompanyTO company = null;

    try {
      ApiResponse<List<CompanyTO>> organizationResponse = apiInstance.findByExternalOrgIdUsingGETWithHttpInfo(
          enterpriseId, null);

      if (organizationResponse != null && organizationResponse.getStatusCode() == 200 && !organizationResponse.getData().isEmpty()) {
        company = organizationResponse.getData().get(0);
      }

    } catch (ApiException e) {
      String message = "There was an unexpected error retrieving enterprise [" + enterpriseId + "] from Redsky";
      String detailMessage = extractErrorMessage(e.getResponseBody());
      if (!detailMessage.isEmpty()) message = message + ": " + detailMessage;
      logger.error("{}: {}", message, e.getMessage());
      throw new RedskyUnknownException(message);
    }

    return company;
  }

  private boolean isUnchanged(CompanyTO existingEnterprise, CompanyEditTO enterpriseModifyRequest) {
    return Objects.equals(existingEnterprise.getExternalOrgId(), enterpriseModifyRequest.getExternalOrgId()) &&
        Objects.equals(existingEnterprise.getName(), enterpriseModifyRequest.getName());
  }

}
