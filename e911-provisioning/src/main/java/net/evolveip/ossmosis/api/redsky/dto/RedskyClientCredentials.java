package net.evolveip.ossmosis.api.redsky.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RedskyClientCredentials {

  private String hostname;
  private String username;
  private String password;
  private Integer requestsPerSecond;
  private Boolean enabled;
} 