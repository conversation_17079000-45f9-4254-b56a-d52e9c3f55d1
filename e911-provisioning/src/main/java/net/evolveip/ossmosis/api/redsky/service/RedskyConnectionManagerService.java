package net.evolveip.ossmosis.api.redsky.service;

import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;
import net.evolveip.ossmosis.api.redsky.constants.RedskyConstants;
import net.evolveip.ossmosis.api.redsky.utils.exceptions.RedskyConnectionException;
import net.evolveip.redsky.client.authentication.api.LoginControllerApi;
import net.evolveip.redsky.client.authentication.api.TokenControllerApi;
import net.evolveip.redsky.client.authentication.model.AuthResultTO;
import net.evolveip.redsky.client.authentication.model.LoginTO;
import net.evolveip.redsky.okhttp.client.ApiClient;
import net.evolveip.redsky.okhttp.client.ApiClient.BasicBuilder;
import org.springframework.stereotype.Service;
import net.evolveip.redsky.okhttp.client.auth.JWT;
import net.evolveip.redsky.okhttp.client.ApiResponse;
import java.util.UUID;

@Service
@Slf4j
public class RedskyConnectionManagerService {

  private static ApiClient redskyClient;

  public RedskyConnectionManagerService() { }

  public ApiClient getConnection(RedskyClientCredentials credentials) {
    
    if (!checkRedskyToken()) {
      ApiClient defaultClient = new ApiClient();
      defaultClient.setBasePath(credentials.getHostname());

      AuthResultTO authResultTO = redskyLogin(credentials, defaultClient);
      if (authResultTO != null) {
        
        if (credentials.getRequestsPerSecond() != null && credentials.getRequestsPerSecond() > 0) {
          BasicBuilder builder = new BasicBuilder(credentials.getHostname());
          builder.ratelimit(credentials.getRequestsPerSecond());

          defaultClient = new ApiClient(builder);
        }

        redskyClient = defaultClient;

        setRedskyTokenInfo(authResultTO);
      } else {
        String message = "Unable to retrieve connection to Redsky";
        throw new RedskyConnectionException(message);
      }
    }

    return redskyClient;
  }

  private static synchronized AuthResultTO redskyLogin(RedskyClientCredentials credentials, ApiClient redskyClient) {
    
    LoginControllerApi apiInstance = new LoginControllerApi(redskyClient);
    apiInstance.setCustomBaseUrl(redskyClient.getBasePath() + "/" + RedskyConstants.AUTH_SERVICE);

    LoginTO loginCredentials = new LoginTO();
    loginCredentials.setUsername(credentials.getUsername());
    loginCredentials.setPassword(credentials.getPassword());

    try {
      ApiResponse<AuthResultTO> response = apiInstance.loginUsingPOST1WithHttpInfo(loginCredentials);
      if (response != null && response.getStatusCode() == 200) {
        return response.getData();
      } else {
        String message = "Unable to log in to Redsky";
        throw new RedskyConnectionException(message);
      }
    } catch (Exception e) {
      String message = "Unable to log in to Redsky";
      logger.error("{}: {}", message, e.getMessage());
      throw new RedskyConnectionException(message);
    }
  }

  private static synchronized boolean checkRedskyToken() {
    boolean valid = false;

    if (redskyClient != null) {
      JWT jwt = (JWT) redskyClient.getAuthentication(RedskyConstants.AUTH_NAME);

      if (jwt.getAccessToken() != null && jwt.getAccessToken().length() > 0) {
        if (jwt.getTokenExpiration() < System.currentTimeMillis()) {
          TokenControllerApi apiInstance = new TokenControllerApi(redskyClient);
          try {
            ApiResponse<AuthResultTO> response = apiInstance.accessTokenRefreshUsingPOSTWithHttpInfo(jwt.getAccessToken(), UUID.fromString(jwt.getRefreshTokenId()));
            if (response != null && response.getStatusCode() == 200) {
              setRedskyTokenInfo(response.getData());
              valid = true;
            }
          } catch (Exception e) {
            logger.error("Error refreshing Redsky token: {}", e.getMessage());
          }
        } else {
          valid = true;
        }
      }
    }
    return valid;
  }

  private static synchronized void setRedskyTokenInfo(AuthResultTO authResultTO) {

    String refreshTokenId;
    Long expiration;

    JWT jwt = (JWT) redskyClient.getAuthentication(RedskyConstants.AUTH_NAME);

    try {
      LinkedTreeMap claims = (LinkedTreeMap) authResultTO.getClaims();
      LinkedTreeMap refreshTokenInfo = (LinkedTreeMap) authResultTO.getRefreshTokenInfo();
      refreshTokenId = (String) refreshTokenInfo.get(RedskyConstants.TOKEN_ID);
      expiration = (Double.valueOf(Double.parseDouble(String.valueOf(claims.get(RedskyConstants.EXPIRATION))))).longValue();

      jwt.setAccessToken(authResultTO.getAccessToken());
      jwt.setRefreshTokenId(refreshTokenId);
      jwt.setTokenExpiration(expiration);
    } catch (Exception e) {
      String message = "Unable to store redsky authentication token";
      logger.error("{}: {}", message, e.getMessage());
      jwt = null;
      throw new RedskyConnectionException(message);
    }
  }
}
