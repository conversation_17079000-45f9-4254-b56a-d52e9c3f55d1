package net.evolveip.ossmosis.api.redsky.service;

import com.google.gson.internal.LinkedTreeMap;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import net.evolveip.ossmosis.api.redsky.constants.RedskyConstants;
import net.evolveip.ossmosis.api.redsky.dto.RedskyClientCredentials;
import net.evolveip.ossmosis.api.redsky.utils.exceptions.RedskyNotFoundException;
import net.evolveip.redsky.client.administration.api.AlertSubscriptionControllerApi;
import net.evolveip.redsky.client.administration.api.AlertTemplateControllerApi;
import net.evolveip.redsky.client.administration.api.CompanyControllerApi;
import net.evolveip.redsky.client.administration.api.CompanyFeaturesControllerApi;
import net.evolveip.redsky.client.authentication.api.TokenControllerApi;
import net.evolveip.redsky.client.authentication.model.AuthResultTO;
import net.evolveip.redsky.client.geography.api.AddressControllerApi;
import net.evolveip.redsky.client.geography.api.BuildingControllerApi;
import net.evolveip.redsky.client.geography.api.LocationControllerApi;
import net.evolveip.redsky.okhttp.client.ApiClient;
import net.evolveip.redsky.okhttp.client.ApiException;
import net.evolveip.redsky.okhttp.client.ApiResponse;
import net.evolveip.redsky.okhttp.client.auth.JWT;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RedskyApiInstanceManager {

  private final RedskyConnectionManagerService redskyConnectionManagerService;

  public RedskyApiInstanceManager(RedskyConnectionManagerService redskyConnectionManagerService) {
    this.redskyConnectionManagerService = redskyConnectionManagerService;
  }

  public CompanyControllerApi getCompanyControllerApiInstance(RedskyClientCredentials credentials) {
    ApiClient redskyClient = redskyConnectionManagerService.getConnection(credentials);
    CompanyControllerApi apiInstance = new CompanyControllerApi(redskyClient);
    apiInstance.setCustomBaseUrl(redskyClient.getBasePath() + "/" + RedskyConstants.ADMIN_SERVICE);
    return apiInstance;
  }

  public CompanyFeaturesControllerApi getCompanyFeaturesControllerApiInstance(RedskyClientCredentials credentials) {
    ApiClient redskyClient = redskyConnectionManagerService.getConnection(credentials);
    CompanyFeaturesControllerApi apiInstance = new CompanyFeaturesControllerApi(redskyClient);
    apiInstance.setCustomBaseUrl(redskyClient.getBasePath() + "/" + RedskyConstants.ADMIN_SERVICE);
    return apiInstance;
  }

  public BuildingControllerApi getBuildingControllerApiInstance(RedskyClientCredentials credentials) {
    ApiClient redskyClient = redskyConnectionManagerService.getConnection(credentials);
    BuildingControllerApi apiInstance = new BuildingControllerApi(redskyClient);
    apiInstance.setCustomBaseUrl(redskyClient.getBasePath() + "/" + RedskyConstants.GEOGRAPHY_SERVICE);
    return apiInstance;
  }

  public AddressControllerApi getAddressControllerApiInstance(RedskyClientCredentials credentials) {
    ApiClient redskyClient = redskyConnectionManagerService.getConnection(credentials);
    AddressControllerApi apiInstance = new AddressControllerApi(redskyClient);
    apiInstance.setCustomBaseUrl(redskyClient.getBasePath() + "/" + RedskyConstants.GEOGRAPHY_SERVICE);
    return apiInstance;
  }

  public LocationControllerApi getLocationControllerApiInstance(RedskyClientCredentials credentials) {
    ApiClient redskyClient = redskyConnectionManagerService.getConnection(credentials);
    LocationControllerApi apiInstance = new LocationControllerApi(redskyClient);
    apiInstance.setCustomBaseUrl(redskyClient.getBasePath() + "/" + RedskyConstants.GEOGRAPHY_SERVICE);
    return apiInstance;
  }

  public AlertSubscriptionControllerApi getAlertSubscriptionControllerApiInstance(RedskyClientCredentials credentials) {
    ApiClient redskyClient = redskyConnectionManagerService.getConnection(credentials);
    AlertSubscriptionControllerApi apiInstance = new AlertSubscriptionControllerApi(redskyClient);
    apiInstance.setCustomBaseUrl(redskyClient.getBasePath() + "/" + RedskyConstants.ADMIN_SERVICE);
    return apiInstance;
  }

  public AlertTemplateControllerApi getAlertTemplateControllerApiInstance(RedskyClientCredentials credentials) {
    ApiClient redskyClient = redskyConnectionManagerService.getConnection(credentials);
    AlertTemplateControllerApi apiInstance = new AlertTemplateControllerApi(redskyClient);
    apiInstance.setCustomBaseUrl(redskyClient.getBasePath() + "/" + RedskyConstants.ADMIN_SERVICE);
    return apiInstance;
  }

  public TokenControllerApi getTokenControllerApiInstance(RedskyClientCredentials credentials) {
    ApiClient redskyClient = redskyConnectionManagerService.getConnection(credentials);
    TokenControllerApi apiInstance = new TokenControllerApi(redskyClient);
    apiInstance.setCustomBaseUrl(redskyClient.getBasePath() + "/" + RedskyConstants.AUTH_SERVICE);
    return apiInstance;
  }

  public String getAuthToken(RedskyClientCredentials credentials) {
    ApiClient redskyClient = redskyConnectionManagerService.getConnection(credentials);
    return ((JWT) redskyClient.getAuthentication(RedskyConstants.AUTH_NAME)).getAccessToken();
  }

  public UUID getParentOrgIdFromToken(RedskyClientCredentials credentials) {
    ApiClient redskyClient = redskyConnectionManagerService.getConnection(credentials);
    TokenControllerApi apiInstance = new TokenControllerApi(redskyClient);
    apiInstance.setCustomBaseUrl(redskyClient.getBasePath() + "/" + RedskyConstants.AUTH_SERVICE);

    String authToken = ((JWT) redskyClient.getAuthentication(RedskyConstants.AUTH_NAME)).getAccessToken();
    UUID exchangeToken = UUID.fromString(((JWT) redskyClient.getAuthentication(RedskyConstants.AUTH_NAME)).getRefreshTokenId());

    UUID parentId = null;

    try {
      ApiResponse<AuthResultTO> exchangeResponse = apiInstance.accessTokenRefreshUsingPOSTWithHttpInfo(authToken, exchangeToken);

      if (exchangeResponse != null && exchangeResponse.getStatusCode() == 200) {
        AuthResultTO authResult = exchangeResponse.getData();

        LinkedTreeMap claims = (LinkedTreeMap) authResult.getClaims();

        parentId = UUID.fromString((String) claims.get("rsOrg"));

        if (parentId == null) {
          String message = "Unable to retrieve parent organization id from access token in Redsky";
          throw new RedskyNotFoundException(message);
        }
      }
    } catch (ApiException e) {
      String message = "Unable to retrieve parent organization id from access token in Redsky";
      logger.error("{}: {}", message, e.getMessage());
      throw new RedskyNotFoundException(message);
    }

    return parentId;
  }

}
