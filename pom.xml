<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.3.5</version>
    <relativePath/> <!-- lookup parent from repository -->
  </parent>
  <groupId>net.evolveip.ossmosis.api</groupId>
  <artifactId>eip-ossmosis-api</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>eip-ossmosis-api</name>
  <description>Ossmosis 6</description>

  <modules>
    <module>eip-ossmosis6-web-services</module>
    <module>broadsoft-provisioning</module>
    <module>e911-provisioning</module>
  </modules>

  <properties>
    <java.version>17</java.version>
    <lombok.mapstruct.binding>0.2.0</lombok.mapstruct.binding>
    <maven.compiler.plugin>3.11.0</maven.compiler.plugin>
    <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
    <apache-commons-csv.version>1.11.0</apache-commons-csv.version>
    <apache-commons-lang3.version>3.17.0</apache-commons-lang3.version>
    <spring.boot.version>3.3.5</spring.boot.version>
    <jaxb-api.version>2.3.1</jaxb-api.version>
    <jaxb-impl.version>2.3.1</jaxb-impl.version>
    <jaxb-core.version>*******</jaxb-core.version>
    <io.temporal-sdk.version>1.29.0</io.temporal-sdk.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok.version}</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <version>${org.mapstruct.version}</version>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok-mapstruct-binding</artifactId>
      <version>${lombok.mapstruct.binding}</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-csv</artifactId>
      <version>${apache-commons-csv.version}</version>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>${apache-commons-lang3.version}</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
      <version>${spring.boot.version}</version>
    </dependency>

    <dependency>
      <groupId>javax.xml.bind</groupId>
      <artifactId>jaxb-api</artifactId>
      <version>${jaxb-api.version}</version>
    </dependency>
    <dependency>
      <groupId>com.sun.xml.bind</groupId>
      <artifactId>jaxb-impl</artifactId>
      <version>${jaxb-impl.version}</version>
    </dependency>
    <dependency>
      <groupId>com.sun.xml.bind</groupId>
      <artifactId>jaxb-core</artifactId>
      <version>${jaxb-core.version}</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven.compiler.plugin}</version>
        <configuration>
          <source>1.17</source>
          <target>1.17</target>
          <annotationProcessorPaths>
            <path>
              <groupId>org.projectlombok</groupId> <!-- IMPORTANT - LOMBOK BEFORE MAPSTRUCT -->
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok-mapstruct-binding</artifactId>
              <version>${lombok.mapstruct.binding}</version>
            </path>
            <path>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>${org.mapstruct.version}</version>
            </path>
          </annotationProcessorPaths>
          <showWarnings>true</showWarnings>
          <compilerArgs>
            <arg>
              -Amapstruct.defaultComponentModel=spring
            </arg>
            <arg>
              -Amapstruct.defaultInjectionStrategy=constructor
            </arg>
            <arg>
              -Amapstruct.suppressGeneratorVersionInfoComment=true
            </arg>
            <arg>
              -Amapstruct.verbose=true
            </arg>
            <arg>
              -Amapstruct.unmappedTargetPolicy=ERROR
            </arg>
          </compilerArgs>
        </configuration>
      </plugin>

    </plugins>
  </build>
</project>