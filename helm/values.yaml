# Image properties
image:
  repository: nexus.evolveip.net:5000/eip-ossmosis6-api
  pullPolicy: Always
  pullSecretName: nexus
  tag: "149"
ingress:
  className: nginx
  domain: evolveip.net
service:
  name: ossmosis6-api
  type: ClusterIP
  port: 8080
  protocol: TCP
# Deployment properties
deployment:
  replicas: 1
# App config properties
appConfig:
  # US, UK, EU
  systemCountry: US
  # dev, qa, test, beta, preprod, prod
  env: dev-bw-phl01
  # elasticSearch:
  #   enabled: true
  #   apmActive: false
  #   logging:
  #     family: ossmosis6
  #     format: json
