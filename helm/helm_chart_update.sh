#!/bin/bash
# Usage: ./helm_chart_update.sh --branch master --build 1
# Purpose: This will update the helm chart based on the build number and push it to the repo
#
# AUTHOR(s):
# <PERSON> <when<PERSON><PERSON>@evolveip.net>

# This is for just aligning the parameters
branch=`git rev-parse --abbrev-ref HEAD`
build=${build}
prefix=${prefix}
while [ $# -gt 0 ]; do
   if [[ $1 == *"--"* ]]; then
        param="${1/--/}"
        declare $param="$2"
   fi
  shift
done

# Build is required to run
if [[ -z ${build} ]]; then
  echo "Build number is required to push the helm updates"
  exit 1
fi

commit_messge="Helm Update"
if [[ ! -z ${prefix} ]]; then
  commit_messge="${prefix} ${commit_messge}"
fi

# Not sure why but bamboo is checking out a SHA so need to get back to the default branch
current_branch=`git rev-parse --abbrev-ref HEAD`
if [[ ${current_branch} != ${branch} ]]; then
  git checkout ${branch}
fi

# This will handle the standard helm charts in the root of helm and also the one offs of subfolders
for d in helm/ helm/*/ ; do
    if [[ ! -f ${d}values.yaml ]] && [[ ! -f ${d}Chart.yaml ]]; then
        continue
    fi
    
    HELM_CHART_VERSION=$(yq e '.version' ${d}Chart.yaml | sed 's![^.]*$!!' | sed -e "s/$/${build}/")
    yq -i e '.image.tag = "'${build}'"' ${d}values.yaml
    yq -i e '.version = "'${HELM_CHART_VERSION}'"' ${d}Chart.yaml
    HELM_CHART_FILENAME=$(cat ${d}Chart.yaml | egrep "^name:|^version:" | awk '{print $2}' | tr '\n' '-' | sed 's/-$/.tgz/')
    helm dependency update ${d}
    helm package ${d}
    helm cm-push ${HELM_CHART_FILENAME} nexus --context-path=/repository/charts
    
    # Only add and commit if we made changes, should only occur is re-ran with same buildNumber
    if [[ `git status ${d}values.yaml ${d}Chart.yaml --porcelain` ]]; then
        git add ${d}values.yaml ${d}Chart.yaml
        git commit -m "${commit_messge} ${d} ${build}"
    fi
done

