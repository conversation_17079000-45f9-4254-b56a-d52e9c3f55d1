{"version": "1.5.0", "plugins_used": [{"name": "ArtifactoryDetector"}, {"name": "AWSKeyDetector"}, {"name": "AzureStorageKeyDetector"}, {"name": "Base64HighEntropyString", "limit": 4.5}, {"name": "BasicAuthDetector"}, {"name": "CloudantDetector"}, {"name": "DiscordBotTokenDetector"}, {"name": "GitHubTokenDetector"}, {"name": "GitLabTokenDetector"}, {"name": "HexHighEntropyString", "limit": 3.0}, {"name": "IbmCloudIamDetector"}, {"name": "IbmCosHmacDetector"}, {"name": "IPPublicDetector"}, {"name": "JwtTokenDetector"}, {"name": "KeywordDetector", "keyword_exclude": ""}, {"name": "MailchimpDetector"}, {"name": "NpmDetector"}, {"name": "OpenAIDetector"}, {"name": "PrivateKeyDetector"}, {"name": "PypiTokenDetector"}, {"name": "SendGridDetector"}, {"name": "SlackDetector"}, {"name": "SoftlayerDetector"}, {"name": "SquareOAuthDetector"}, {"name": "StripeDetector"}, {"name": "TelegramBotTokenDetector"}, {"name": "TwilioKeyDetector"}], "filters_used": [{"path": "detect_secrets.filters.allowlist.is_line_allowlisted"}, {"path": "detect_secrets.filters.common.is_ignored_due_to_verification_policies", "min_level": 2}, {"path": "detect_secrets.filters.heuristic.is_indirect_reference"}, {"path": "detect_secrets.filters.heuristic.is_likely_id_string"}, {"path": "detect_secrets.filters.heuristic.is_lock_file"}, {"path": "detect_secrets.filters.heuristic.is_not_alphanumeric_string"}, {"path": "detect_secrets.filters.heuristic.is_potential_uuid"}, {"path": "detect_secrets.filters.heuristic.is_prefixed_with_dollar_sign"}, {"path": "detect_secrets.filters.heuristic.is_sequential_string"}, {"path": "detect_secrets.filters.heuristic.is_swagger_file"}, {"path": "detect_secrets.filters.heuristic.is_templated_secret"}], "results": {"eip-ossmosis6-web-services/src/main/java/net/evolveip/ossmosis/api/scheduler/job/BaseJobRunner.java": [{"type": "Base64 High Entropy String", "filename": "eip-ossmosis6-web-services/src/main/java/net/evolveip/ossmosis/api/scheduler/job/BaseJobRunner.java", "hashed_secret": "bb814cf15cf9478829e6b85205824b0f1fd8ca08", "is_verified": false, "line_number": 71}], "eip-ossmosis6-web-services/src/main/resources/sql/setup/05.ossmosisOwnerRoleCreate.sql": [{"type": "Secret Keyword", "filename": "eip-ossmosis6-web-services/src/main/resources/sql/setup/05.ossmosisOwnerRoleCreate.sql", "hashed_secret": "8277963bf3cb8ded418e5f16b5a27c33c744dfbb", "is_verified": false, "line_number": 8}], "eip-ossmosis6-web-services/src/test/resources/application.properties": [{"type": "Secret Keyword", "filename": "eip-ossmosis6-web-services/src/test/resources/application.properties", "hashed_secret": "5baa61e4c9b93f3f0682250b6cf8331b7ee68fd8", "is_verified": false, "line_number": 20}], "helm/values.yaml": [{"type": "Secret Keyword", "filename": "helm/values.yaml", "hashed_secret": "eb3591cac1239f323ddb46e0ef040587c69e2edc", "is_verified": false, "line_number": 5}]}, "generated_at": "2025-05-27T15:27:47Z"}