# Handle line endings automatically for files detected as text
# and leave all files detected as binary untouched.
* text=auto

# These files are text and should be normalized (Convert crlf => lf)
*.java          eol=lf
*.yml           eol=lf

# These files are binary and should be left untouched
# (binary is a macro for -text -diff)
*.class         binary
*.dll           binary
*.ear           binary
*.gif           binary
*.ico           binary
*.jar           binary
*.jpg           binary
*.jpeg          binary
*.png           binary
*.so            binary
*.war           binary
